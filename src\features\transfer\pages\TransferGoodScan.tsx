import { useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import TransferGoodService from 'services/TransferGoodService';
import { showToast } from 'utils/common';
import ScanZone from '../components/ScanZone';
import UpdateTransferGoodForm from '../components/UpdateTransferGoodForm';

export default function TransferGoodScan() {
    const { t } = useTranslation();
    const [hasScan, setHasScan] = useState(false);
    const [barcode, setBarcode] = useState('');

    const getItem = async (tId: string) => {
        const response = await TransferGoodService.getByBarcode(tId);
        if (response.success) {
            setHasScan(true);
            return response.data;
        }
        return null;
    };

    const {
        data: transferGood,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: [QUERY_KEY.TRANSFER_GOOD, barcode],
        queryFn: ({ queryKey }) => getItem(queryKey[1] ?? ''),
        enabled: !!barcode,
    });

    if (barcode && !isLoading && !transferGood) showToast(false, [t('error.barcode')]);

    return (
        <>
            <Helmet>
                <title>{t('scan.transfer')}</title>
            </Helmet>
            <ContentHeader title={t('scan.transfer')} />
            <div className="content-body">
                <div className="col-12">
                    {!hasScan && <ScanZone onScan={setBarcode} />}
                    {isLoading && <Spinner />}
                    {!isLoading && hasScan && transferGood && (
                        <UpdateTransferGoodForm
                            transferGood={transferGood}
                            refetch={refetch}
                            isScan={true}
                            onRemove={() => {
                                setHasScan(false);
                                setBarcode('');
                            }}
                        />
                    )}
                </div>
            </div>
        </>
    );
}
