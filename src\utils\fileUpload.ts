import { TFunction } from 'i18next';
import { showToast } from './common';

export interface FileUploadConfig {
    maxFileSize?: number; // bytes
    allowedTypes?: string[];
    maxFiles?: number;
    maxFileNameLength?: number;
}

export interface PreviewFile {
    id: string;
    file: File;
    preview: string;
    uploadedId?: string;
}

export interface FileValidationResult {
    validFiles: File[];
    errors: string[];
}

export interface FileUploadResult {
    success: boolean;
    files: PreviewFile[];
    errors: string[];
}

/**
 * Default configuration for file upload
 */
export const DEFAULT_FILE_CONFIG: FileUploadConfig = {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/svg+xml'],
    maxFiles: 10,
    maxFileNameLength: 255,
};

/**
 * Validates selected files based on configuration
 * @param files - Array of files to validate
 * @param config - Configuration for validation
 * @param t - Translation function
 * @param currentFileCount - Current number of files already selected
 * @returns Validation result with valid files and errors
 */
export const validateFiles = (
    files: File[],
    config: FileUploadConfig,
    t: TFunction,
    currentFileCount: number = 0
): FileValidationResult => {
    const {
        maxFileSize = DEFAULT_FILE_CONFIG.maxFileSize!,
        allowedTypes = DEFAULT_FILE_CONFIG.allowedTypes!,
        maxFiles = DEFAULT_FILE_CONFIG.maxFiles!,
        maxFileNameLength = DEFAULT_FILE_CONFIG.maxFileNameLength!,
    } = config;

    const validFiles: File[] = [];
    const errors: string[] = [];

    // Check total files limit first
    if (currentFileCount + files.length > maxFiles) {
        errors.push(t('error.too_many_files'));
        return { validFiles: [], errors };
    }

    files.forEach((file) => {
        // Validate file type - more flexible check
        const isImageFile =
            file.type.startsWith('image/') ||
            allowedTypes.includes(file.type.toLowerCase()) ||
            /\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(file.name);

        if (!isImageFile) {
            const supportedTypes = allowedTypes.map((type) => type.split('/')[1].toUpperCase()).join(', ');
            errors.push(t('error.invalid_file_type', { fileName: file.name, supportedTypes }));
            return;
        }

        // Validate file size
        if (file.size > maxFileSize) {
            const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
            const maxMB = (maxFileSize / (1024 * 1024)).toFixed(0);
            errors.push(t('error.file_too_large', { fileName: file.name, size: sizeMB, maxSize: maxMB }));
            return;
        }

        // Validate file name length
        if (file.name.length > maxFileNameLength) {
            errors.push(t('error.file_name_too_long', { fileName: file.name, maxLength: maxFileNameLength }));
            return;
        }

        // Validate file is not empty
        if (file.size === 0) {
            errors.push(t('error.file_empty', { fileName: file.name }));
            return;
        }

        validFiles.push(file);
    });

    return { validFiles, errors };
};

/**
 * Checks file magic bytes to verify it's a real image
 * @param file - File to check
 * @returns Promise<boolean> - True if valid image magic bytes
 */
const checkImageMagicBytes = (file: File): Promise<boolean> =>
    new Promise((resolve) => {
        const reader = new FileReader();

        reader.onload = (e) => {
            const buffer = e.target?.result as ArrayBuffer;
            if (!buffer) {
                resolve(false);
                return;
            }

            const bytes = new Uint8Array(buffer);

            // Check common image magic bytes
            const magicBytes = [
                [0xff, 0xd8, 0xff], // JPEG
                [0x89, 0x50, 0x4e, 0x47], // PNG
                [0x47, 0x49, 0x46], // GIF
                [0x52, 0x49, 0x46, 0x46], // WebP (RIFF)
                [0x42, 0x4d], // BMP
            ];

            const isValidMagic = magicBytes.some((magic) => magic.every((byte, index) => bytes[index] === byte));

            resolve(isValidMagic);
        };

        reader.onerror = () => resolve(false);

        // Read first 12 bytes to check magic bytes
        reader.readAsArrayBuffer(file.slice(0, 12));
    });

/**
 * Validates if a file is a valid image by checking its content
 * @param file - File to validate
 * @returns Promise<boolean> - True if valid image
 */
const validateImageContent = async (file: File): Promise<boolean> => {
    // First check magic bytes
    const hasValidMagicBytes = await checkImageMagicBytes(file);
    if (!hasValidMagicBytes) {
        return false;
    }

    // Then try to load as image
    return new Promise((resolve) => {
        const img = new Image();
        const url = URL.createObjectURL(file);

        let resolved = false;

        img.onload = () => {
            if (!resolved) {
                resolved = true;
                URL.revokeObjectURL(url);
                // Check if image has valid dimensions
                resolve(img.width > 0 && img.height > 0);
            }
        };

        img.onerror = () => {
            if (!resolved) {
                resolved = true;
                URL.revokeObjectURL(url);
                resolve(false);
            }
        };

        img.src = url;

        // Timeout after 5 seconds to avoid hanging
        setTimeout(() => {
            if (!resolved) {
                resolved = true;
                URL.revokeObjectURL(url);
                resolve(false);
            }
        }, 5000);
    });
};

/**
 * Processes files by reading them as data URLs for preview
 * @param files - Array of files to process
 * @param t - Translation function
 * @returns Promise with processed files
 */
export const processFilesForPreview = async (files: File[], t: TFunction): Promise<FileUploadResult> => {
    if (files.length === 0) {
        return { success: true, files: [], errors: [] };
    }

    const validFiles: PreviewFile[] = [];
    const errors: string[] = [];

    for (const file of files) {
        try {
            // First validate if it's a real image
            const isValidImage = await validateImageContent(file);
            if (!isValidImage) {
                errors.push(t('error.file_corrupted', { fileName: file.name }));
                continue;
            }

            // Then read as data URL
            const result = await new Promise<string>((resolve, reject) => {
                const reader = new FileReader();

                reader.onload = (e) => {
                    const result = e.target?.result;
                    if (typeof result === 'string' && result.startsWith('data:image/')) {
                        resolve(result);
                    } else {
                        reject(new Error('Invalid data URL'));
                    }
                };

                reader.onerror = () => reject(new Error('Reader error'));

                try {
                    reader.readAsDataURL(file);
                } catch (error) {
                    reject(error);
                }
            });

            validFiles.push({
                id: Math.random().toString(36).substring(2, 9),
                file,
                preview: result,
            });
        } catch (error) {
            errors.push(t('error.file_read_failed', { fileName: file.name }));
        }
    }

    return {
        success: validFiles.length > 0 || errors.length === 0,
        files: validFiles,
        errors,
    };
};

/**
 * Main function to handle file upload with validation and preview processing
 * @param fileList - FileList from input element
 * @param config - Configuration for file upload
 * @param t - Translation function
 * @param currentFiles - Current preview files array
 * @param showSuccessToast - Whether to show success toast
 * @returns Promise with upload result
 */
export const handleFileUpload = async (
    fileList: FileList | null,
    config: FileUploadConfig,
    t: TFunction,
    currentFiles: PreviewFile[] = [],
    showSuccessToast: boolean = true
): Promise<FileUploadResult> => {
    // Validate input
    if (!fileList || fileList.length === 0) {
        return { success: false, files: [], errors: [] };
    }

    const files = Array.from(fileList);

    // Validate files
    const validationResult = validateFiles(files, config, t, currentFiles.length);

    // Show validation errors
    if (validationResult.errors.length > 0) {
        showToast(false, validationResult.errors);
    }

    // Process valid files if any
    if (validationResult.validFiles.length === 0) {
        return { success: false, files: [], errors: validationResult.errors };
    }

    // Process files for preview
    const uploadResult = await processFilesForPreview(validationResult.validFiles, t);

    // Combine all errors (validation + processing)
    const allErrors = [...validationResult.errors, ...uploadResult.errors];

    if (uploadResult.files.length > 0) {
        // Show success message
        if (showSuccessToast) {
            showToast(true, [t('success.files_uploaded', { count: uploadResult.files.length })]);
        }

        // Also show errors if any files failed
        if (allErrors.length > 0) {
            showToast(false, allErrors);
        }

        return {
            success: true,
            files: uploadResult.files,
            errors: allErrors,
        };
    } else {
        // No files processed successfully
        if (allErrors.length > 0) {
            showToast(false, allErrors);
        }

        return {
            success: false,
            files: [],
            errors: allErrors,
        };
    }
};

/**
 * Cleanup preview URLs to prevent memory leaks
 * @param files - Array of preview files to cleanup
 */
export const cleanupPreviewUrls = (files: PreviewFile[]): void => {
    files.forEach((file) => {
        if (file.preview && file.preview.startsWith('blob:')) {
            URL.revokeObjectURL(file.preview);
        }
    });
};

/**
 * Gets file size in human readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size string
 */
export const getFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Checks if file type is supported image
 * @param file - File to check
 * @param allowedTypes - Array of allowed MIME types
 * @returns Boolean indicating if file is supported
 */
export const isSupportedImageFile = (file: File, allowedTypes: string[] = DEFAULT_FILE_CONFIG.allowedTypes!): boolean =>
    allowedTypes.includes(file.type.toLowerCase());
