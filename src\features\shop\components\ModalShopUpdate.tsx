import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { optionModelDefault, REACT_APP_IS_ADMIN, REACT_APP_MAIN_PARTNER_ID } from 'constants/common';
import { find, includes } from 'lodash';
import { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select, { SingleValue } from 'react-select';
import { SelectOptionModel } from 'types/common/Item';
import Shop, { UpdateShop } from 'types/Shop';
import User, { displayUserOnSelect, UserRole } from 'types/User';
import { getSelectStyle, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';

interface IProps {
    show: boolean;
    shop: Shop | undefined;
    partners: User[];
    isLoading: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: UpdateShop) => void;
    currentUser: User | null;
}

export default function ModalShopUpdate({
    show,
    shop,
    partners,
    isLoading,
    changeShow,
    submitAction,
    currentUser,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [partnerValue, setPartnerValue] = useState<SelectOptionModel>(optionModelDefault);
    const schema = yup
        .object({
            code: yup.string().required(t('error.required')).trim(),
            domain: yup.string().required(t('error.required')).trim(),
            partner_id: yup.string().required(t('error.required')).trim(),
            access_token: yup.string().required(t('error.required')).trim(),
            webhook_secret: yup.string().required(t('error.required')).trim(),
        })
        .required();
    const {
        register,
        handleSubmit,
        reset,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm<UpdateShop>({
        resolver: yupResolver(schema),
    });

    const isDisableUpdate = useMemo(
        () => !includes([UserRole.ADMIN, UserRole.PARTNER], currentUser?.role_id),
        [currentUser]
    );

    useEffect(() => {
        if (shop && show) {
            const item = find(partners, { id: shop.partner_id });
            if (item) {
                setPartnerValue({ value: item.id!, label: displayUserOnSelect(item) });
            }
            reset({
                id: shop.id,
                code: shop.code,
                domain: shop.domain,
                partner_id: shop.partner_id,
                access_token: '',
                webhook_secret: '',
            });
        } else {
            let partnerId = '';
            if (partners.length === 1) {
                partnerId = partners[0].id!;
                setPartnerValue({ value: partnerId, label: displayUserOnSelect(partners[0]) });
            } else if (partners.length > 1) {
                partnerId = REACT_APP_MAIN_PARTNER_ID;
                setPartnerValue({ value: partnerId, label: displayUserOnSelect(find(partners, { id: partnerId })) });
            }
            reset({
                code: '',
                domain: '',
                partner_id: partnerId,
                access_token: '',
                webhook_secret: '',
            });
        }
    }, [shop, partners, show]);

    const onChangePartner = (option: SingleValue<SelectOptionModel>) => {
        setPartnerValue(option ?? optionModelDefault);
        setValue('partner_id', option?.value ?? '');
        if (option?.value) clearErrors('partner_id');
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t(shop ? 'shop.edit' : 'shop.add')}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('code')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('code')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.code?.message),
                                            })}
                                            disabled={isDisableUpdate}
                                        />
                                        <span className="error">{errors.code?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('domain')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('domain')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.domain?.message),
                                            })}
                                            disabled={isDisableUpdate}
                                        />
                                        <span className="error">{errors.domain?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('partner.single')} <span className="error">*</span>
                                        </label>
                                        <Select
                                            options={partners.map((item) => ({
                                                value: item.id!,
                                                label: displayUserOnSelect(item),
                                            }))}
                                            onChange={onChangePartner}
                                            value={partnerValue}
                                            isDisabled={!!shop || !REACT_APP_IS_ADMIN}
                                            styles={{
                                                control: (baseStyles, state) =>
                                                    getSelectStyle(baseStyles, state, !!errors.partner_id?.message),
                                            }}
                                        />
                                        <span className="error">{errors.partner_id?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6" />
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('accessToken')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('access_token')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.access_token?.message),
                                            })}
                                            disabled={isDisableUpdate}
                                        />
                                        <span className="error">{errors.access_token?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('webhookSecret')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('webhook_secret')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.webhook_secret?.message),
                                            })}
                                            disabled={isDisableUpdate}
                                        />
                                        <span className="error">{errors.webhook_secret?.message}</span>
                                    </div>
                                </div>
                            </div>
                            {!isDisableUpdate && (
                                <div className="modal-footer">
                                    <UpdateButton
                                        btnText={t(shop ? 'update' : 'add')}
                                        isLoading={isLoading}
                                        hasDivWrap={false}
                                    />
                                </div>
                            )}
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
