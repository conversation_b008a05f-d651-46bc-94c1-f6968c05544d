import { useState } from 'react';

const MODAL_TYPES = {
    CREATE: 'create',
    EDIT: 'edit',
    VIEW: 'view',
    DELETE: 'delete',
} as const;

type ModalType = (typeof MODAL_TYPES)[keyof typeof MODAL_TYPES];

export const useDesignTemplateModal = () => {
    const [modalState, setModalState] = useState({
        isOpen: false,
        type: MODAL_TYPES.CREATE as ModalType,
        itemId: '',
    });

    const [viewModalState, setViewModalState] = useState({
        isOpen: false,
        itemId: '',
    });

    const openModal = (type: ModalType, itemId: string = '') => {
        setModalState({
            isOpen: true,
            type,
            itemId,
        });
    };

    const closeModal = () => {
        setModalState({
            isOpen: false,
            type: MODAL_TYPES.CREATE,
            itemId: '',
        });
    };

    const openViewModal = (itemId: string) => {
        setViewModalState({
            isOpen: true,
            itemId,
        });
    };

    const closeViewModal = () => {
        setViewModalState({
            isOpen: false,
            itemId: '',
        });
    };

    const openCreateModal = () => openModal(MODAL_TYPES.CREATE);
    const openEditModal = (id: string) => openModal(MODAL_TYPES.EDIT, id);
    const openDeleteModal = (id: string) => openModal(MODAL_TYPES.DELETE, id);

    return {
        modalState,
        viewModalState,
        openModal,
        closeModal,
        openViewModal,
        closeViewModal,
        openCreateModal,
        openEditModal,
        openDeleteModal,
        isEditMode: modalState.type === MODAL_TYPES.EDIT,
        isCreateMode: modalState.type === MODAL_TYPES.CREATE,
        isViewMode: modalState.type === MODAL_TYPES.VIEW,
        isDeleteMode: modalState.type === MODAL_TYPES.DELETE,
    };
};
