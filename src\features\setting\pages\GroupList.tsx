import { useMutation, useQuery } from '@tanstack/react-query';
import classNames from 'classnames';
import ModalConfirm from 'components/partials/ModalConfirm';
import ModalItemParamUpdate from 'components/partials/ModalItemParamUpdate';
import { QUERY_KEY } from 'constants/common';
import { find } from 'lodash';
import { useState } from 'react';
import { Edit, Menu, PlusCircle, Search, Trash2, X } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';
import ActionService from 'services/ActionService';
import GroupService from 'services/GroupService';
import ItemParamService from 'services/ItemParamService';
import { ItemParamModel, ItemParamType } from 'types/common/Item';
import { getFieldInArrayObject, showToast } from 'utils/common';
import ListGroupAction from '../components/ListGroupAction';
import './../../../components/partials/chat/app-chat-list.css';
import './../../../components/partials/chat/app-chat.css';

export default function GroupList() {
    const [searchText, setSearchText] = useState('');
    const [show, setShow] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState('');
    const [itemEditId, setItemEditId] = useState('');
    const { t } = useTranslation();

    const getActionIds = async (groupId: string) => {
        const response = await GroupService.getActions(groupId);
        if (response.success) return response.data;
        return [];
    };

    const { data: groups, refetch } = useQuery({
        queryKey: [QUERY_KEY.GROUPS],
        queryFn: () => ItemParamService.getList(ItemParamType.GROUP),
    });

    const { data: actions } = useQuery({
        queryKey: [QUERY_KEY.ACTIONS],
        queryFn: () => ActionService.getList(true),
    });

    const { data: actionIds } = useQuery({
        enabled: !!itemId,
        queryKey: [QUERY_KEY.GROUP_ACTIONS, itemId],
        queryFn: ({ queryKey }) => getActionIds(queryKey[1]),
    });

    const updateMutation = useMutation({
        mutationFn: (param: ItemParamModel) => ItemParamService.update(ItemParamType.GROUP, param, param.id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowUpdate(false);
                    setItemId(data.data.id);
                    setItemEditId(data.data.id);
                    refetch();
                }
            }
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => ItemParamService.deleteItem(ItemParamType.GROUP, id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    setItemEditId('');
                    refetch();
                }
            }
        },
    });

    const updateGroupActionMutation = useMutation({
        mutationFn: ({ id, ids }: { id: string; ids: string[] }) => GroupService.updateActions(id, ids),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) showToast(data.success, data.messages);
        },
    });

    const onChooseGroup = (groupId: string) => {
        setItemId(groupId);
        setItemEditId(groupId);
        updateGroupActionMutation.reset();
    };

    const handleUpdate = (isAdd: boolean) => {
        updateMutation.reset();
        setShowUpdate(true);
        if (isAdd) setItemEditId('');
    };

    const handleDelete = () => {
        deleteMutation.reset();
        setShowDelete(true);
    };

    const updateItem = async (data: ItemParamModel) => updateMutation.mutate(data);

    const deleteItem = async () => {
        if (itemEditId) {
            deleteMutation.mutate(itemEditId);
        }
    };

    const grantActions = (ids: string[]) => updateGroupActionMutation.mutate({ id: itemId, ids });

    return (
        <>
            <Helmet>
                <title>{t('group.multiple')}</title>
            </Helmet>
            <div className="sidebar-left">
                <div className="sidebar">
                    <div className={classNames('sidebar-content', { show })}>
                        <span className="sidebar-close-icon" onClick={() => setShow(false)}>
                            <X size={14} />
                        </span>
                        <div className="chat-fixed-search">
                            <div className="d-flex align-items-center w-100">
                                <div className="sidebar-profile-toggle">
                                    <PlusCircle
                                        size={24}
                                        className="text-primary cursor-pointer"
                                        onClick={() => handleUpdate(true)}
                                    />
                                </div>
                                <div className="input-group input-group-merge ms-1 w-100">
                                    <span className="input-group-text round">
                                        <Search size={14} className="text-muted" />
                                    </span>
                                    <input
                                        type="text"
                                        className="form-control round"
                                        placeholder={t('keywords')}
                                        value={searchText}
                                        onChange={(e) => setSearchText(e.target.value.trim())}
                                    />
                                </div>
                            </div>
                        </div>
                        <PerfectScrollbar className="chat-user-list-wrapper list-group">
                            <ul className="chat-users-list chat-list media-list">
                                {(groups ?? [])
                                    .filter((item) => item.name.toLowerCase().includes(searchText.toLowerCase()))
                                    .map((item) => (
                                        <li key={item.id} className={classNames({ active: item.id === itemId })}>
                                            <div
                                                className="chat-info flex-grow-1"
                                                onClick={() => onChooseGroup(item.id)}
                                            >
                                                <h5 className="mb-0">{item.name}</h5>
                                            </div>
                                        </li>
                                    ))}
                            </ul>
                        </PerfectScrollbar>
                    </div>
                </div>
            </div>
            {itemId && (
                <div className="content-right w-100">
                    <div className="content-wrapper container-xxl p-0">
                        <div className="content-header row" />
                        <div className="content-body">
                            <div className="body-content-overlay" />
                            <section className="chat-app-window">
                                <div className="active-chat">
                                    <div className="chat-navbar">
                                        <header className="chat-header">
                                            <div className="d-flex align-items-center">
                                                <div
                                                    className="sidebar-toggle d-block d-lg-none me-1"
                                                    onClick={() => setShow(true)}
                                                >
                                                    <Menu size={14} />
                                                </div>
                                                <h6
                                                    className="mb-0 text-primary cursor-pointer"
                                                    onClick={() => handleUpdate(false)}
                                                >
                                                    {getFieldInArrayObject(groups ?? [], itemId)} <Edit size={14} />
                                                </h6>
                                            </div>
                                            <div className="d-flex align-items-center">
                                                {actionIds?.length === 0 && (
                                                    <button
                                                        type="button"
                                                        title={t('delete')}
                                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                        onClick={handleDelete}
                                                    >
                                                        <Trash2 size={14} />
                                                    </button>
                                                )}
                                            </div>
                                        </header>
                                    </div>
                                    <ListGroupAction
                                        actions={actions ?? []}
                                        actionIds={actionIds ?? []}
                                        loading={updateGroupActionMutation.isPending}
                                        handleSubmit={grantActions}
                                    />
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            )}
            <ModalItemParamUpdate
                itemTypeId={ItemParamType.GROUP}
                show={showUpdate}
                itemParam={find(groups, { id: itemEditId })}
                isLoading={updateMutation.isPending}
                changeShow={(s: boolean) => setShowUpdate(s)}
                submitAction={updateItem}
            />
            <ModalConfirm
                show={showDelete}
                text={t('confirm.delete')}
                btnDisabled={deleteMutation.isPending}
                changeShow={(s: boolean) => setShowDelete(s)}
                submitAction={deleteItem}
            />
        </>
    );
}
