import { keepPreviousData, useQuery } from '@tanstack/react-query';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import UpdateButton from 'components/partials/UpdateButton';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isEmpty, isUndefined, omitBy, some } from 'lodash';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TransferGoodService from 'services/TransferGoodService';
import WarehouseService from 'services/WarehouseService';
import { useAuthStore } from 'stores/authStore';
import { ItemStatus, TrueFalse } from 'types/common/Item';
import {
    SearchSupplierProduct,
    SearchSupplierProductParam,
    TransferGoodProduct,
    TransferType,
} from 'types/TransferGood';
import { showToast } from 'utils/common';
import List<PERSON>rossCheck from './ListCrossCheck';
import SearchCrossCheckForm from './SearchCrossCheckForm';
import { SearchWarehouse } from '../../../types/Warehouse';

interface IProps {
    supplierId: string;
}

export default function CrossCheckList({ supplierId }: Readonly<IProps>) {
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);
    const [loading, setLoading] = useState(false);
    const [show, setShow] = useState(false);

    const { queryParams, setQueryParams } = useQueryParams<SearchSupplierProductParam>();
    const paramConfig: SearchSupplierProductParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            warehouse_id: queryParams.warehouse_id,
            quantity__lt: queryParams.quantity__lt,
            quantity__gt: queryParams.quantity__gt,
            status_id: queryParams.status_id,
            supplier_id: supplierId,
            supplier_debt: queryParams.supplier_debt,
        },
        isUndefined
    );

    const { data: warehouses } = useQuery({
        enabled: !!supplierId && !!user,
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }, user],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse, user!.id),
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!supplierId,
        queryKey: [QUERY_KEY.SUPPLIER_PRODUCTS, paramConfig],
        queryFn: ({ queryKey }) => TransferGoodService.getSupplierProduct(queryKey[1] as SearchSupplierProduct),
        placeholderData: keepPreviousData,
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const createTransfer = async () => {
        if (data?.items && queryParams.supplier_debt && queryParams.warehouse_id) {
            const products: TransferGoodProduct[] = [];
            const supplierProductIds: string[] = [];
            const isReceived = +queryParams.supplier_debt === TrueFalse.TRUE;
            data.items.forEach((item) => {
                if (isReceived && item.status_id === ItemStatus.PENDING && item.quantity > 0) {
                    products.push({
                        product_child_id: item.product_child_id,
                        quantity: item.quantity,
                        order_ids: item.order_ids,
                    });
                    supplierProductIds.push(item.supplier_product_id);
                }
                if (!isReceived && item.status_id === ItemStatus.PENDING && item.quantity < 0) {
                    products.push({
                        product_child_id: item.product_child_id,
                        quantity: -item.quantity,
                        order_ids: item.order_ids,
                    });
                    supplierProductIds.push(item.supplier_product_id);
                }
            });
            if (!isEmpty(products) && !isEmpty(supplierProductIds)) {
                setLoading(true);
                const response = await TransferGoodService.add({
                    warehouse_id: queryParams.warehouse_id,
                    supplier_id: supplierId,
                    type_id: isReceived ? TransferType.RECEIVED : TransferType.DELIVERY,
                    products,
                    supplier_product_ids: supplierProductIds,
                });
                setLoading(false);
                showToast(response.success, response.messages);
                if (response.success) {
                    setShow(false);
                    refetch();
                }
            }
        }
    };

    return (
        <>
            <div className="content-body">
                <div className="col-12">
                    <SearchCrossCheckForm isLoading={isLoading || isRefetching} warehouses={warehouses ?? []} />
                    {(isLoading || isRefetching) && <Spinner />}
                    <div className="alert alert-warning">
                        <div className="alert-body">{t('supplier.alertDebt')}</div>
                    </div>
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListCrossCheck items={data.items} warehouses={warehouses ?? []} paging={data.pagination} />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                            {queryParams.supplier_debt &&
                                queryParams.warehouse_id &&
                                some(data.items, { status_id: ItemStatus.PENDING }) && (
                                    <div className="col-12">
                                        <UpdateButton
                                            btnText={t(
                                                `${
                                                    +queryParams.supplier_debt === TrueFalse.TRUE
                                                        ? 'receive'
                                                        : 'delivery'
                                                }.create`
                                            )}
                                            isLoading={loading}
                                            btnClass={['me-2', 'mb-2']}
                                            onSubmit={() => setShow(true)}
                                        />
                                    </div>
                                )}
                        </div>
                    )}
                </div>
            </div>
            <ModalConfirm
                show={show}
                text={t('confirm.createTransfer')}
                btnDisabled={loading}
                changeShow={(s: boolean) => setShow(s)}
                submitAction={createTransfer}
            />
        </>
    );
}
