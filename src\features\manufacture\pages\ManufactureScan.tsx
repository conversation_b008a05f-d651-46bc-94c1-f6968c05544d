import { useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import ScanZone from 'features/transfer/components/ScanZone';
import { includes, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import OrderService from 'services/OrderService';
import { OrderProductStatus } from 'types/Order';
import { showToast } from 'utils/common';
import StartFinishManufacturing from '../components/StartFinishManufacturing';

export default function ManufactureScan() {
    const { t } = useTranslation();
    const { type } = useParams();
    const [typeId, setTypeId] = useState(0);
    const [hasScan, setHasScan] = useState(false);
    const [barcode, setBarcode] = useState('');
    const navigate = useNavigate();

    useEffect(() => {
        if (includes(['start', 'finish'], type)) {
            setTypeId(type === 'start' ? OrderProductStatus.PENDING : OrderProductStatus.FINISH);
            setHasScan(false);
            setBarcode('');
        } else navigate('/not-found');
    }, [type]);

    const getItem = async (tId: string) => {
        const response = await OrderService.getByProductBarcode(tId, 0);
        if (response.success && !isEmpty(response.data.products)) {
            const statusId = response.data.products[0].status_id;
            if (
                (statusId === OrderProductStatus.PENDING && typeId === OrderProductStatus.PENDING) ||
                (statusId === OrderProductStatus.MANUFACTURING && typeId === OrderProductStatus.FINISH)
            ) {
                setHasScan(true);
                return response.data;
            } else showToast(false, [t('error.barcode')]);
        } else showToast(false, [t('error.barcode')]);
        return null;
    };

    const { data, isLoading } = useQuery({
        queryKey: [QUERY_KEY.ORDER_MANUFACTURE, barcode],
        queryFn: ({ queryKey }) => getItem(queryKey[1] ?? ''),
        enabled: !!barcode && typeId > 0,
    });

    return (
        <>
            <Helmet>
                <title>{t(`manufacturer.${typeId === OrderProductStatus.PENDING ? 'start' : 'finish'}`)}</title>
            </Helmet>
            <ContentHeader title={t(`manufacturer.${typeId === OrderProductStatus.PENDING ? 'start' : 'finish'}`)} />
            <div className="content-body">
                <div className="col-12">
                    {!hasScan && <ScanZone onScan={setBarcode} />}
                    {isLoading && <Spinner />}
                    {!isLoading && hasScan && data && (
                        <StartFinishManufacturing
                            data={data}
                            isStart={typeId === OrderProductStatus.PENDING}
                            onRemove={() => {
                                setHasScan(false);
                                setBarcode('');
                            }}
                        />
                    )}
                </div>
            </div>
        </>
    );
}
