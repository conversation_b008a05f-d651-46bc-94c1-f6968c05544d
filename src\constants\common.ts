import { startsWith } from 'lodash';
import Action from 'types/Action';
import { SelectOption, SelectOptionModel } from 'types/common/Item';

export enum DEFAULT_VALUE {
    IMAGE = '/assets/images/prp/default.png',
}

export const REACT_APP_API_URL = import.meta.env.VITE_REACT_APP_API_URL ?? '';
export const REACT_APP_ADMIN_URL = import.meta.env.VITE_REACT_APP_ADMIN_URL ?? '';
export const VITE_REACT_APP_PARTNER_URL = import.meta.env.VITE_REACT_APP_PARTNER_URL ?? '';
export const VITE_REACT_APP_MANUFACTURER_URL = import.meta.env.VITE_REACT_APP_MANUFACTURER_URL ?? '';
export const VITE_REACT_APP_DESIGNER_URL = import.meta.env.VITE_REACT_APP_DESIGNER_URL ?? '';
export const VITE_REACT_APP_MAIN_SHOP_ID = import.meta.env.VITE_REACT_APP_MAIN_SHOP_ID ?? '';
export const REACT_APP_MAIN_PARTNER_ID = import.meta.env.VITE_REACT_APP_MAIN_PARTNER_ID ?? '';
export const REACT_APP_MAIN_SUPPLIER_ID = import.meta.env.VITE_REACT_APP_MAIN_SUPPLIER_ID ?? '';
export const REACT_APP_STRIPE_PUBLIC_KEY = import.meta.env.VITE_REACT_APP_STRIPE_PUBLIC_KEY ?? '';
export const REACT_APP_IS_ADMIN = startsWith(window.location.href, REACT_APP_ADMIN_URL);
export const REACT_APP_IS_PARTNER = startsWith(window.location.href, VITE_REACT_APP_PARTNER_URL);
export const REACT_APP_IS_MANUFACTURER = startsWith(window.location.href, VITE_REACT_APP_MANUFACTURER_URL);
export const REACT_APP_IS_DESIGNER = startsWith(window.location.href, VITE_REACT_APP_DESIGNER_URL);
export const BASE_PIXEL = import.meta.env.VITE_BASE_PIXEL ?? 5392;
export const BASE_CENTI = import.meta.env.VITE_BASE_CENTI ?? 400;

export const PAGINATION = {
    countItem: 0,
    totalPage: 1,
    currentPage: 1,
    limit: 10,
};

export const optionConstantDefault: SelectOption = { value: 0, label: '' };
export const optionModelDefault: SelectOptionModel = { value: '', label: '' };

export const MenuPartners: Action[] = [
    { id: '1', name: 'Customers', url: '/user/list/customer', display_order: 1, icon: 'user-check', children: [] },
    { id: '2', name: 'Products', url: '/product', display_order: 1, icon: 'gift', children: [] },
    { id: '5', name: 'Partner Invoices', url: '/partnerInvoice', display_order: 1, icon: '', children: [] },
    {
        id: '6',
        name: 'Design templates',
        url: '/template',
        display_order: 1,
        icon: 'layout',
        children: [],
    },
    { id: '3', name: 'Orders', url: '/order', display_order: 1, icon: 'shopping-cart', children: [] },
    {
        id: '4',
        name: 'Settings',
        url: '',
        display_order: 1,
        icon: 'settings',
        children: [{ id: '41', name: 'Shops', url: '/shop', display_order: 1, icon: '' }],
    },
];

export const MenuManufacturers: Action[] = [
    { id: '1', name: 'Warehouses', url: '/warehouse', display_order: 1, icon: 'map-pin', children: [] },
    {
        id: '2',
        name: 'Transfer Products',
        url: '',
        display_order: 1,
        icon: 'truck',
        children: [
            { id: '21', name: 'Receive Products', url: '/transfer/list/receive', display_order: 1, icon: '' },
            { id: '22', name: 'Delivery Products', url: '/transfer/list/delivery', display_order: 1, icon: '' },
            { id: '23', name: 'Transfer Scan', url: '/transfer/scan', display_order: 1, icon: 'code' },
        ],
    },
    { id: '3', name: 'Orders', url: '/order', display_order: 1, icon: 'shopping-cart', children: [] },
    {
        id: '4',
        name: 'Manufactures',
        url: '',
        display_order: 1,
        icon: '',
        children: [
            {
                id: '41',
                name: 'Wait Manufacturing',
                url: '/manufacture/product/pending',
                display_order: 1,
                icon: 'gift',
            },
            { id: '42', name: 'Start Manufacturing', url: '/manufacture/scan/start', display_order: 1, icon: 'code' },
            {
                id: '43',
                name: 'Manufacturing Products',
                url: '/manufacture/product/manufacturing',
                display_order: 1,
                icon: 'gift',
            },
            { id: '44', name: 'Finish Manufacture', url: '/manufacture/scan/finish', display_order: 1, icon: 'code' },
        ],
    },
    {
        id: '5',
        name: 'Packing Orders',
        url: '',
        display_order: 1,
        icon: '',
        children: [
            { id: '51', name: 'Products', url: '/manufacture/product/finish', display_order: 1, icon: 'gift' },
            { id: '52', name: 'Packing Scan', url: '/packing/scan', display_order: 1, icon: 'code' },
        ],
    },
];

export const MenuDesigners: Action[] = [
    { id: '1', name: 'Orders', url: '/designerOrder', display_order: 1, icon: 'shopping-cart', children: [] },
    { id: '2', name: 'Holiday Schedules', url: '/designerSchedule', display_order: 1, icon: '', children: [] },
];

export enum QUERY_KEY {
    ACTIONS = 'actions',
    USERS = 'users',
    USER = 'user',
    PROFILE = 'profile',
    USER_ACTIONS = 'user_actions',
    GROUPS = 'groups',
    USER_GROUPS = 'user_groups',
    GROUP_ACTIONS = 'group_action',
    USER_2FA = 'user_2fa',
    NOTIFICATIONS = 'notifications',
    SHOPS = 'shops',
    SHOP = 'shop',
    COUNTRIES = 'countries',
    REQUEST_ORDERS = 'request_orders',
    PARTNER_INVOICES = 'partner_invoices',
    PARTNER_INVOICE = 'partner_invoice',
    ORDERS = 'orders',
    ORDER_COUNT = 'order_count',
    ORDER = 'order',
    //LOCATIONS = 'locations',
    WAREHOUSES = 'warehouses',
    DESIGN_TEMPLATES = 'design_templates',
    PRODUCTS = 'products',
    PRODUCT_DESIGNS = 'product_designs',
    DESIGNS = 'designs',
    DESIGN_SIZES = 'design_sizes',
    DESIGN_CODES = 'design_codes',
    TRANSFER_GOODS = 'transfer_goods',
    TRANSFER_GOOD = 'transfer_good',
    CHILDREN_PRODUCTS = 'children_products',
    SUPPLIER_PRODUCTS = 'supplier_products',
    ORDER_MANUFACTURE = 'order_manufacture',
    ORDER_PRODUCTS = 'order_products',
    ORDER_TRACKS = 'order_tracks',
    BARCODE = 'barcode',
    DASHBOARD_ORDER_COUNT = 'dashboard_order_count',
    DASHBOARD_PRODUCTS_SOLD = 'dashboard_products_sold',
    DASHBOARD_TOTAL_PRICE = 'dashboard_total_price',
    PRODUCT_WEIGHTS = 'product_weights',
    DESIGNER_SCHEDULES = 'designer_schedules',
    COUNT_ORDER_BY_STATUS = 'count_order_by_status',
}
