// src/components/PaymentForm.js
import React, { useState } from 'react';
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js';
import PartnerInvoiceService from '../../../services/PartnerInvoiceService';
import { useTranslation } from 'react-i18next';
import { showToast } from '../../../utils/common';

interface IProps {
    id: string;
    onPaymentSuccess: () => void;
}

export default function PaymentForm({ id, onPaymentSuccess }: Readonly<IProps>) {
    const stripe = useStripe();
    const elements = useElements();
    const [clientSecret, setClientSecret] = useState('');
    const { t } = useTranslation();

    const handleSubmit = async (event: { preventDefault: () => void }) => {
        event.preventDefault();
        if (!stripe || !elements) return;
        let cs = clientSecret;
        if (!cs) {
            const response = await PartnerInvoiceService.createPaymentIntent(1000);
            if (response.success) {
                cs = response.data.clientSecret;
                setClientSecret(cs);
            }
        }
        const cardElement = elements.getElement(CardElement);
        if (cardElement && cs) {
            const { error, paymentIntent } = await stripe.confirmCardPayment(cs, {
                payment_method: { card: cardElement },
            });
            if (error) {
                showToast(false, [t('error.common')]);
            } else {
                showToast(true, [t('partnerInvoice.paymentSuccess')]);
                await PartnerInvoiceService.updateStatus(id);
                onPaymentSuccess();
            }
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <CardElement />
            <button type="submit" disabled={!stripe}>
                {t('partnerInvoice.payment')}
            </button>
        </form>
    );
}
