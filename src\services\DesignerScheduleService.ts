import { REACT_APP_IS_DESIGNER } from 'constants/common';
import { ApiResponse, DataListResponse } from 'types/common';
import DesignerSchedule, { SearchDesignerSchedule } from 'types/DesignerSchedule';
import Http from './http.class';

const http = new Http().instance;

const DesignerScheduleService = {
    async getList(searchParams: SearchDesignerSchedule, userId?: string) {
        const params = { ...searchParams };
        if (REACT_APP_IS_DESIGNER && userId) {
            params.designer_id__i = userId;
        }
        const { data } = await http.get<ApiResponse<DataListResponse<DesignerSchedule>>>('/designer_schedules', {
            params,
        });
        if (data.success) return data.data;
    },

    async update(param: DesignerSchedule, id: string = '') {
        delete param.id;
        const { data } = id
            ? await http.put<ApiResponse<DesignerSchedule>>(`/designer_schedules/${id}`, param)
            : await http.post<ApiResponse<DesignerSchedule>>('/designer_schedules', param);
        return data;
    },

    async deleteItem(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/designer_schedules/${id}`);
        return data;
    },
};

export default DesignerScheduleService;
