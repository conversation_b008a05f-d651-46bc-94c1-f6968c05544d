import { ApiResponse, DataListResponse } from 'types/common';
import DesignTemplate, { SearchTemplateParam } from 'types/DesignTemplate';
import Http from './http.class';
import FileService from './FileService';

const http = new Http().instance;

const DesignTemplateService = {
    async getList(params: SearchTemplateParam) {
        const { data } = await http.get<ApiResponse<DataListResponse<DesignTemplate>>>('/design-templates', {
            params,
        });
        if (data.success) return data.data;
        return null;
    },

    async getById(id: string) {
        const { data } = await http.get<ApiResponse<DesignTemplate>>(`/design-templates/${id}`);
        if (data.success) {
            const file_ids = data.data.file_ids;
            let file_urls: string[] = [];
            if (Array.isArray(file_ids) && file_ids.length > 0) {
                file_urls = await FileService.getFileUrlsByIds(file_ids);
            }
            return { ...data.data, file_urls };
        }
        return null;
    },

    async create(param: Partial<DesignTemplate>) {
        const { data } = await http.post<ApiResponse<DesignTemplate>>('/design-templates', param);
        return data;
    },

    async update(param: Partial<DesignTemplate>, id: string = '') {
        const { data } = await http.put<ApiResponse<DesignTemplate>>(`/design-templates/${id}`, param);
        return data;
    },

    async delete(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/design-templates/${id}`);
        return data;
    },
};

export default DesignTemplateService;
