import { REACT_APP_IS_ADMIN } from 'constants/common';
import { Gift, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ItemStatusNames } from 'types/common/Item';
import Country from 'types/Country';
import User from 'types/User';
import Warehouse from 'types/Warehouse';
import { getFieldHtml, getFieldInArrayObject } from 'utils/common';

interface IProps {
    items: Warehouse[];
    countries: Country[];
    manufacturers: User[];
    handleEdit: (id: string) => void;
    handleDelete: (id: string) => void;
}

export default function ListWarehouse({ items, countries, manufacturers, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th>{t('name')}</th>
                        <th>{t('manufacturer.single')}</th>
                        <th>{t('address')}</th>
                        <th>{t('country')}</th>
                        <th className="text-center">{t('quantityWarning')}</th>
                        <th className="text-center">{t('statusName')}</th>
                        {REACT_APP_IS_ADMIN && <th className="thAction1"></th>}
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Warehouse, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{index + 1}</td>
                            <td>
                                <span
                                    className="text-primary cursor-pointer"
                                    onClick={() => REACT_APP_IS_ADMIN && handleEdit(item.id!)}
                                >
                                    {item.name}
                                </span>
                            </td>
                            <td>{getFieldInArrayObject(manufacturers, item.manufacturer_id, 'first_name')}</td>
                            <td>{item.address}</td>
                            <td>{getFieldInArrayObject(countries, item.country_id)}</td>
                            <td className="text-center">{item.quantity_minimum}</td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                            {REACT_APP_IS_ADMIN && (
                                <td className="text-center">
                                    <Link
                                        to={`/product?children.stocks.warehouse_id=${item.id}`}
                                        target="_blank"
                                        title={t('product.views')}
                                        className="btn btn-icon btn-sm"
                                    >
                                        <Gift size={14} />
                                    </Link>
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                </td>
                            )}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
