import classNames from 'classnames';
import { DEFAULT_VALUE, QUERY_KEY, REACT_APP_IS_ADMIN } from 'constants/common';
import { includes, isEmpty, join } from 'lodash';
import { useLayoutEffect, useState } from 'react';
import { Bell, Circle, Disc, Grid, Menu, Moon, Power, Sun, User as UserX, X } from 'react-feather';
import { useTranslation } from 'react-i18next';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';
import { Link, NavLink, useNavigate } from 'react-router-dom';
import Action from 'types/Action';
import Shop from 'types/Shop';
import User, { displayUserName, UserRole, UserRoleNames } from 'types/User';
import { getIcon } from 'utils/actionIcon';
import { getFieldInArrayObject, showToast } from 'utils/common';
import { getLocalStorage, setLocalStorage } from 'utils/localStorage';
import Notification, { NotificationItemType, SearchNotification } from '../../types/Notification';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { ItemStatus } from '../../types/common/Item';
import NotificationService from '../../services/NotificationService';
import WarehouseService from '../../services/WarehouseService';
import { SearchWarehouse } from '../../types/Warehouse';

interface IProps {
    actions: Action[];
    user: User | null | undefined;
    shops: Shop[];
    countOrderNoWarehouse: number;
    handleLogout: () => void;
}

const HasShopUrls = ['/product', '/stockWarning', '/design', '/user/list/customer'];

export default function Header({ actions, user, shops, countOrderNoWarehouse, handleLogout }: Readonly<IProps>) {
    const [darkMode, setDarkMode] = useState(false);
    const [showUser, setShowUser] = useState(false);
    const [showNotification, setShowNotification] = useState(false);
    const [onScrollMenu, setOnScrollMenu] = useState(false);
    const [collapsedMenu, setCollapsedMenu] = useState(false);
    const [fixedMenu, setFixedMenu] = useState(!getLocalStorage('prp_fixed_menu'));
    const [openActionIds, setOpenActionIds] = useState<string[]>([]);
    const navigate = useNavigate();
    const { t } = useTranslation();

    const { data: notifications, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.NOTIFICATIONS, { status_id: ItemStatus.PENDING }, user],
        queryFn: ({ queryKey }) =>
            NotificationService.getList({
                ...(queryKey[1] as SearchNotification),
                user_id: user!.id!,
            }),
        placeholderData: keepPreviousData,
    });

    const { data: warehouses } = useQuery({
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE, out_of_stock: true }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
    });

    useLayoutEffect(() => {
        document.body.classList.remove('blank-page');
        document.body.classList.add('menu-expanded');
    }, [actions]);

    useLayoutEffect(() => {
        const menuElement = document.getElementById('main-menu-navigation');
        if (menuElement) {
            Array.from(menuElement.getElementsByClassName('nav-item')).forEach((element) => {
                Array.from(element.getElementsByTagName('a')).forEach((aEle) => {
                    if (aEle.classList.contains('active')) {
                        if (element.getElementsByClassName('menu-content').length > 0) {
                            aEle.closest('li')?.classList.add('active');
                            element.classList.add('sidebar-group-active');
                            element.classList.add('has-sub');
                            element.classList.add('open');
                        } else {
                            element.classList.add('active');
                        }
                        return false;
                    } else {
                        aEle.closest('li')?.classList.remove('active');
                    }
                });
            });
        }
    });

    useLayoutEffect(() => {
        if (darkMode) {
            document.body.classList.add('dark-layout');
        } else {
            document.body.classList.remove('dark-layout');
        }
    }, [darkMode]);

    useLayoutEffect(() => {
        if (fixedMenu) expandedMenuFn();
        else collapsedMenuFn();
        setLocalStorage('prp_fixed_menu', fixedMenu ? '' : '1');
    }, [fixedMenu]);

    useLayoutEffect(() => {
        function changBodyClass() {
            if (window.innerWidth < 1200) {
                document.body.classList.add('vertical-overlay-menu');
                document.body.classList.add('menu-close');
                document.body.classList.remove('menu-collapsed');
                document.body.classList.remove('menu-expanded');
            } else {
                document.body.classList.remove('vertical-overlay-menu');
                document.body.classList.remove('menu-close');
            }
        }
        window.addEventListener('resize', changBodyClass);
        changBodyClass();
        return () => window.removeEventListener('resize', changBodyClass);
    }, []);

    const toggleMenuItem = (id: string) => {
        const items = [...openActionIds];
        if (includes(items, id)) {
            const index = items.indexOf(id);
            if (index > -1) items.splice(index, 1);
        } else items.push(id);
        setOpenActionIds(items);
    };

    const handleToggleMenu = () => {
        if (document.body.classList.contains('menu-close')) {
            document.body.classList.remove('menu-close');
            document.body.classList.add('menu-open');
            document.body.classList.remove('vertical-menu-modern');
        } else {
            document.body.classList.remove('menu-open');
            document.body.classList.add('menu-close');
            document.body.classList.add('vertical-menu-modern');
        }
    };

    const enterMenu = () => {
        if (!fixedMenu && window.innerWidth >= 1200) {
            expandedMenuFn();
        }
    };

    const leaveMenu = () => {
        if (!fixedMenu && window.innerWidth >= 1200) {
            collapsedMenuFn();
        }
    };

    const expandedMenuFn = () => {
        document.body.classList.remove('menu-collapsed');
        document.body.classList.add('menu-expanded');
        setCollapsedMenu(false);
    };

    const collapsedMenuFn = () => {
        document.body.classList.remove('menu-expanded');
        document.body.classList.add('menu-collapsed');
        setCollapsedMenu(true);
    };

    const readNotification = async (n: Notification) => {
        const response = await NotificationService.read(n.id!);
        showToast(response.success, response.messages);
        if (response.success) refetch();
        if (n.item_id && n.item_type_id) {
            navigate(
                n.item_type_id === NotificationItemType.TRANSFER_GOOD
                    ? `/transfer/edit/${n.item_id}`
                    : `/order/view/${n.item_id}`
            );
        }
    };

    const readAllNotification = async () => {
        const response = await NotificationService.readAll();
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    return (
        <>
            <nav className="header-navbar navbar navbar-expand-lg align-items-center floating-nav navbar-light navbar-shadow container-xxl">
                <div className="navbar-container d-flex content">
                    <div className="bookmark-wrapper d-flex align-items-center">
                        <ul className="nav navbar-nav d-xl-none">
                            <li className="nav-item">
                                <a className="nav-link menu-toggle" onClick={handleToggleMenu}>
                                    <Menu size={14} />
                                </a>
                            </li>
                        </ul>
                    </div>
                    <ul className="nav navbar-nav align-items-center ms-auto">
                        <li className="nav-item d-none d-lg-block">
                            <a
                                className="nav-link nav-link-style"
                                onClick={() => setDarkMode((prevDarkMode) => !prevDarkMode)}
                            >
                                {darkMode ? <Sun size={14} /> : <Moon size={14} />}
                            </a>
                        </li>
                        <li className="nav-item dropdown dropdown-notification me-25">
                            <a
                                className="nav-link"
                                onClick={() => {
                                    if (!isEmpty(notifications?.items)) {
                                        setShowNotification((prevShow) => !prevShow);
                                    }
                                }}
                            >
                                <Bell size={14} className="ficon" />
                                {(notifications?.pagination.count_item || !isEmpty(warehouses)) && (
                                    <span className="badge rounded-pill bg-danger badge-up">
                                        {(notifications?.pagination?.count_item ?? 0) + (warehouses ?? []).length}
                                    </span>
                                )}
                            </a>
                            <ul
                                className={classNames('dropdown-menu dropdown-menu-media dropdown-menu-end', {
                                    show: showNotification,
                                })}
                            >
                                <li className="dropdown-menu-header">
                                    <div className="dropdown-header d-flex">
                                        <h4 className="notification-title mb-0 me-auto">
                                            <a className="cursor-pointer">{t('notifications')}</a>
                                        </h4>
                                        {(notifications?.pagination.count_item || !isEmpty(warehouses)) && (
                                            <div className="badge rounded-pill badge-light-primary">
                                                {(notifications?.pagination?.count_item ?? 0) +
                                                    (warehouses ?? []).length}
                                            </div>
                                        )}
                                    </div>
                                </li>
                                {(!isEmpty(notifications?.items) || !isEmpty(warehouses)) && (
                                    <>
                                        <li>
                                            <PerfectScrollbar className="scrollable-container media-list">
                                                <a className="d-flex cursor-pointer">
                                                    <div className="list-item d-flex align-items-start">
                                                        <div className="me-1">
                                                            <div className="avatar">
                                                                <img
                                                                    src={DEFAULT_VALUE.IMAGE}
                                                                    alt=""
                                                                    width={32}
                                                                    height={32}
                                                                />
                                                            </div>
                                                        </div>
                                                        <div className="list-item-body flex-grow-1">
                                                            <p className="media-heading">
                                                                <span className="fw-bolder">
                                                                    {t('warehouse.multiple')}
                                                                </span>
                                                            </p>
                                                            <small className="notification-text">
                                                                {t('warehouse.productsOutOfStock', {
                                                                    data: join(
                                                                        (warehouses ?? []).map((item) => item.name),
                                                                        ','
                                                                    ),
                                                                })}
                                                            </small>
                                                        </div>
                                                    </div>
                                                </a>
                                                {notifications?.items.map((n: Notification) => (
                                                    <a
                                                        key={n.id}
                                                        className="d-flex cursor-pointer"
                                                        onClick={() => readNotification(n)}
                                                    >
                                                        <div className="list-item d-flex align-items-start">
                                                            <div className="me-1">
                                                                <div className="avatar">
                                                                    <img
                                                                        src={DEFAULT_VALUE.IMAGE}
                                                                        alt=""
                                                                        width={32}
                                                                        height={32}
                                                                    />
                                                                </div>
                                                            </div>
                                                            <div className="list-item-body flex-grow-1">
                                                                <p className="media-heading">
                                                                    <span className="fw-bolder">{n.title}</span>
                                                                </p>
                                                                <small className="notification-text">{n.content}</small>
                                                            </div>
                                                        </div>
                                                    </a>
                                                ))}
                                            </PerfectScrollbar>
                                        </li>
                                        <li className="dropdown-menu-footer">
                                            <a className="btn btn-primary w-100" onClick={readAllNotification}>
                                                {t('readAll')}
                                            </a>
                                        </li>
                                    </>
                                )}
                            </ul>
                        </li>
                        <li className="nav-item dropdown dropdown-user">
                            <a
                                className="nav-link dropdown-toggle dropdown-user-link"
                                onClick={() => setShowUser((prevShow) => !prevShow)}
                            >
                                <div className="user-nav d-sm-flex d-none">
                                    <span className="user-name fw-bolder">{displayUserName(user)}</span>
                                    <span className="user-status">
                                        {t(
                                            `${getFieldInArrayObject(
                                                UserRoleNames,
                                                user?.role_id ?? UserRole.ADMIN
                                            )}.single`
                                        )}
                                    </span>
                                </div>
                                <span className="avatar">
                                    <img
                                        className="round"
                                        src={user?.avatar ?? DEFAULT_VALUE.IMAGE}
                                        alt="avatar"
                                        height={40}
                                        width={40}
                                    />
                                    <span className="avatar-status-online" />
                                </span>
                            </a>
                            <div className={classNames('dropdown-menu dropdown-menu-end', { show: showUser })}>
                                <Link
                                    to="/user/profile"
                                    className="dropdown-item"
                                    onClick={() => setShowUser((prevShow) => !prevShow)}
                                >
                                    <span className="me-50">
                                        <UserX size={14} />
                                    </span>{' '}
                                    {t('profile')}
                                </Link>
                                <Link to="/" className="dropdown-item" onClick={handleLogout}>
                                    <span className="me-50">
                                        <Power size={14} />
                                    </span>{' '}
                                    {t('logout')}
                                </Link>
                            </div>
                        </li>
                    </ul>
                </div>
            </nav>
            <div
                className={classNames('main-menu menu-fixed menu-light menu-accordion menu-shadow', {
                    expanded: !collapsedMenu,
                })}
                onMouseEnter={enterMenu}
                onMouseLeave={leaveMenu}
            >
                <div className="navbar-header">
                    <ul className="nav navbar-nav flex-row">
                        <li className="nav-item me-auto">
                            <Link className="navbar-brand" to="/dashboard">
                                <span className="brand-logo">
                                    <img src="/assets/images/prp/logo-2.png" alt="" />
                                </span>
                            </Link>
                        </li>
                        <li className="nav-item nav-toggle">
                            <a className="nav-link modern-nav-toggle pe-0">
                                <span
                                    className="d-block d-xl-none text-primary toggle-icon font-medium-4"
                                    onClick={handleToggleMenu}
                                >
                                    <X size={14} />
                                </span>
                                <span
                                    className="d-none d-xl-block collapse-toggle-icon font-medium-4 text-primary"
                                    onClick={() => setFixedMenu((prevFixedMenu) => !prevFixedMenu)}
                                >
                                    {!fixedMenu && (
                                        <span>
                                            <Circle size={14} />
                                        </span>
                                    )}
                                    {fixedMenu && (
                                        <span>
                                            <Disc size={14} />
                                        </span>
                                    )}
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div className={classNames('shadow-bottom', { onScrollMenu })} />
                <PerfectScrollbar
                    className="main-menu-content"
                    onScrollY={() => setOnScrollMenu((prevOnScrollMenu) => !prevOnScrollMenu)}
                >
                    <ul className="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation">
                        {user?.role_id !== UserRole.DESIGNER && (
                            <li className="nav-item">
                                <NavLink to="/dashboard" className="d-flex align-items-center">
                                    <Grid size={14} />
                                    <span className="menu-title text-truncate">{t('dashboards')}</span>
                                </NavLink>
                            </li>
                        )}
                        {actions?.map((action) => (
                            <li
                                className={classNames('nav-item', {
                                    'has-sub': !isEmpty(action.children) || includes(HasShopUrls, action.url),
                                    open:
                                        (!isEmpty(action.children) || includes(HasShopUrls, action.url)) &&
                                        includes(openActionIds, action.id),
                                })}
                                key={action.id}
                                onClick={() => toggleMenuItem(action.id!)}
                            >
                                {(!isEmpty(action.children) || includes(HasShopUrls, action.url)) && (
                                    <>
                                        <a className="d-flex align-items-center">
                                            {getIcon(action.icon)}
                                            <span className="menu-title text-truncate">{action.name}</span>
                                        </a>
                                        <ul className="menu-content">
                                            {action.children!.map((children) => (
                                                <li key={children.id}>
                                                    <NavLink to={children.url} className="d-flex align-items-center">
                                                        {getIcon(children.icon)}
                                                        <span className="menu-title text-truncate">
                                                            {children.name}
                                                        </span>
                                                    </NavLink>
                                                </li>
                                            ))}
                                            {action.url === '/user/list/customer' &&
                                                shops.map((children) => (
                                                    <li key={children.id}>
                                                        <NavLink
                                                            to={`/user/list/customer?shop_id__i=${children.id}`}
                                                            className="d-flex align-items-center"
                                                        >
                                                            <Circle size={14} />
                                                            <span className="menu-title text-truncate">
                                                                {children.name}
                                                            </span>
                                                        </NavLink>
                                                    </li>
                                                ))}
                                            {action.url === '/product' &&
                                                shops.map((children) => (
                                                    <li key={children.id}>
                                                        <NavLink
                                                            to={`/product/${children.id}/${children.domain}`}
                                                            className="d-flex align-items-center"
                                                        >
                                                            <Circle size={14} />
                                                            <span className="menu-title text-truncate">
                                                                {children.name}
                                                            </span>
                                                        </NavLink>
                                                    </li>
                                                ))}
                                            {action.url === '/stockWarning' &&
                                                shops.map((children) => (
                                                    <li key={children.id}>
                                                        <NavLink
                                                            to={`/stockWarning/${children.id}/${children.domain}`}
                                                            className="d-flex align-items-center"
                                                        >
                                                            <Circle size={14} />
                                                            <span className="menu-title text-truncate">
                                                                {children.name}
                                                            </span>
                                                        </NavLink>
                                                    </li>
                                                ))}
                                        </ul>
                                    </>
                                )}
                                {(!action.children ||
                                    (action.children.length === 0 && !includes(HasShopUrls, action.url))) && (
                                    <NavLink to={action.url} className="d-flex align-items-center">
                                        {getIcon(action.icon)}
                                        <span className="menu-title text-truncate">{action.name}</span>
                                        {REACT_APP_IS_ADMIN && action.url === '/order' && countOrderNoWarehouse > 0 && (
                                            <span className="badge badge-light-danger rounded-pill ms-auto">
                                                {countOrderNoWarehouse}
                                            </span>
                                        )}
                                    </NavLink>
                                )}
                            </li>
                        ))}
                    </ul>
                </PerfectScrollbar>
            </div>
        </>
    );
}
