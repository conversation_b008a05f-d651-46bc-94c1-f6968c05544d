import ContentHeader from 'components/partials/ContentHeader';
import CountOrderByStatus from 'features/dashboard/components/CountOrderByStatus';
import OrderCount from 'features/dashboard/components/OrderCount';
import ProductsSold from 'features/dashboard/components/ProductsSold';
import TotalPrice from 'features/dashboard/components/TotalPrice';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from 'stores/authStore';
import { UserRole } from 'types/User';
import { includes } from 'lodash';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export default function Dashboard() {
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);
    const navigate = useNavigate();

    useEffect(() => {
        if (user && user.role_id === UserRole.DESIGNER) {
            navigate('/designerOrder');
        }
    }, [user]);

    return (
        <>
            <Helmet>
                <title>{t('dashboards')}</title>
            </Helmet>
            <ContentHeader title={t('dashboards')} />
            <div className="content-body">
                <div className="row">
                    {user && (
                        <>
                            {!includes([UserRole.DESIGNER, UserRole.MANUFACTURER], user.role_id) && (
                                <div className="col-12">
                                    <OrderCount user={user} />
                                </div>
                            )}
                            {[UserRole.ADMIN, UserRole.PARTNER].includes(user.role_id) && (
                                <>
                                    <div className="col-12">
                                        <TotalPrice user={user} />
                                    </div>
                                    <div className="col-12">
                                        <ProductsSold />
                                    </div>
                                </>
                            )}
                            {[UserRole.ADMIN, UserRole.MANUFACTURER].includes(user.role_id) && (
                                <div className="col-12">
                                    <CountOrderByStatus user={user} />
                                </div>
                            )}
                        </>
                    )}
                </div>
            </div>
        </>
    );
}
