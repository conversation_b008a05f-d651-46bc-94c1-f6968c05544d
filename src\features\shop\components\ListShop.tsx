import UserLink from 'components/partials/UserLink';
import { REACT_APP_IS_ADMIN } from 'constants/common';
import { find } from 'lodash';
import { Link, MapPin, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Paging } from 'types/common';
import { ItemStatusNames } from 'types/common/Item';
import Country from 'types/Country';
import Shop from 'types/Shop';
import User, { displayUserName, UserRole } from 'types/User';
import { genTableIndex, getFieldHtml, getFieldInArrayObject } from 'utils/common';

interface IProps {
    items: Shop[];
    countries: Country[];
    partners: User[];
    paging: Paging;
    handleEdit: (id: string) => void;
    handleDelete: (id: string) => void;
    showLocations: (id: string) => void;
    currentUser: User | null;
}

export default function ListShop({
    items,
    countries,
    partners,
    paging,
    handleEdit,
    handleDelete,
    showLocations,
    currentUser,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const getUserLink = (id: string) => {
        const item = find(partners, { id });
        if (item) {
            return <UserLink to={`/user/edit/${item.id}`} avatar={item.avatar} name={displayUserName(item)} />;
        }
        return <></>;
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th>{t('name')}</th>
                        <th className="text-center">{t('code')}</th>
                        {REACT_APP_IS_ADMIN && <th>{t('partner.single')}</th>}
                        <th>{t('email')}</th>
                        <th>{t('shopOwner')}</th>
                        <th>{t('currency')}</th>
                        <th>{t('country')}</th>
                        <th className="text-center">{t('statusName')}</th>
                        <th className="thAction3"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Shop, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <span className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                    {item.name}
                                </span>
                            </td>
                            <td className="text-center">{item.code}</td>
                            {REACT_APP_IS_ADMIN && <td>{getUserLink(item.partner_id)}</td>}
                            <td>
                                <a href={`mailto:${item.email}`} target="_blank">
                                    {item.email}
                                </a>
                            </td>
                            <td>{item.shop_owner}</td>
                            <td>{item.currency}</td>
                            <td>{getFieldInArrayObject(countries, item.country_id)}</td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                            <td className="text-center">
                                <a href={`https://${item.domain}`} target="_blank">
                                    <Link size={14} />
                                </a>
                                <button
                                    type="button"
                                    title={t('location.single')}
                                    className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                    onClick={() => showLocations(item.id!)}
                                >
                                    <MapPin size={14} />
                                </button>
                                {currentUser?.role_id !== UserRole.ADMIN && (
                                    <button
                                        type="button"
                                        title={t('delete')}
                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                        onClick={() => handleDelete(item.id!)}
                                    >
                                        <Trash2 size={14} />
                                    </button>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
