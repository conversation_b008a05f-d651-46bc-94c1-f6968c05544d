import axios, { AxiosInstance, ResponseType } from 'axios';
import { AUTH_KEYS } from 'constants/auth';
import { REACT_APP_API_URL } from 'constants/common';
import jwt_decode from 'jwt-decode';
import { includes, startsWith } from 'lodash';
import User from 'types/User';
import { Language } from 'types/common/Item';
import { getLocalStorage, removeLocalStorage, setLocalStorage } from 'utils/localStorage';

export default class Http {
    instance: AxiosInstance;
    private refreshTokenRes: string;
    private accessToken: string;
    constructor(contentType = 'application/json', responseType: ResponseType = 'json') {
        this.accessToken = getLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
        this.refreshTokenRes = getLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
        this.instance = axios.create({
            baseURL: REACT_APP_API_URL,
            timeout: 10000,
            headers: {
                'Content-Type': contentType,
            },
            responseType,
        });

        this.instance.interceptors.request.use(
            async (config) => {
                if (config.headers) {
                    if (this.accessToken) config.headers.Authorization = `Bearer ${this.accessToken}`;
                    config.headers['lang-code'] = getLocalStorage(AUTH_KEYS.LANGUAGE_CODE, Language.EN);
                    if (startsWith(config.url, '/product')) {
                        config.headers['X-validate-token'] = 'printdropexpress';
                    }
                }
                if (
                    !includes(
                        [
                            '/auth/login',
                            '/auth/logout',
                            '/auth/register',
                            '/auth/forgot-password',
                            '/auth/change-password',
                            '/auth/reset-password',
                        ],
                        config.url
                    )
                ) {
                    if (this.accessToken) {
                        try {
                            // @ts-ignore
                            const { exp } = jwt_decode(this.accessToken);
                            if (exp && Date.now() > exp * 1000) {
                                await this.getToken((act: string) => {
                                    if (act) this.accessToken = act;
                                });
                            }
                        } catch (_err) {
                            await this.getToken((act: string) => {
                                if (act) this.accessToken = act;
                            });
                        }
                    } else {
                        await this.getToken((act: string) => {
                            if (act) this.accessToken = act;
                        });
                    }
                }
                return config;
            },
            (error) => Promise.reject(error)
        );

        this.instance.interceptors.response.use(
            async (response) => {
                const { url } = response.config;
                if (url === `/auth/login`) {
                    const dataLogin = response.data.data as User;
                    this.accessToken = dataLogin.token.access_token;
                    this.refreshTokenRes = dataLogin.token.refresh_token;
                    if (!dataLogin.two_factor_enabled) {
                        setLocalStorage(AUTH_KEYS.ACCESS_TOKEN, this.accessToken);
                        setLocalStorage(AUTH_KEYS.REFRESH_TOKEN, this.refreshTokenRes);
                    }
                } else if (url === '/auth/logout') {
                    this.accessToken = '';
                    this.refreshTokenRes = '';
                    removeLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
                    removeLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
                } else if (response.data.status === 401) {
                    await this.getToken(() => {
                        location.reload();
                    });
                }
                return response;
            },
            (error) => error.response
        );
    }

    getToken = async (fnSuccess: Function) => {
        try {
            const res = await axios.post(
                REACT_APP_API_URL + '/auth/refresh',
                { refresh_token: this.refreshTokenRes },
                {
                    headers: {
                        'Content-type': 'application/json',
                    },
                }
            );
            let isSuccess = false;
            if (res.status === 200) {
                if (res.data.data.accessToken && res.data.data.refreshToken) {
                    isSuccess = true;
                    const { accessToken: newAccessToken, refreshToken: newRefreshToken } = res.data.data;
                    this.accessToken = newAccessToken;
                    this.refreshTokenRes = newRefreshToken;
                    setLocalStorage(AUTH_KEYS.ACCESS_TOKEN, newAccessToken);
                    setLocalStorage(AUTH_KEYS.REFRESH_TOKEN, newRefreshToken);
                    fnSuccess(newAccessToken);
                }
            }
            if (!isSuccess) {
                this.handleErrorRefreshToken();
            }
        } catch (e) {
            this.handleErrorRefreshToken();
        }
    };

    private readonly handleErrorRefreshToken = () => {
        this.accessToken = '';
        this.refreshTokenRes = '';
        removeLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
        removeLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
        window.location.href = '/';
    };
}
