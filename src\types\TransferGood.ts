import { BaseModel, BaseSearch } from './common';
import { Barcode, ItemParam, ProductBarcode } from './common/Item';

export interface SearchTransferGood extends BaseSearch {
    warehouse_id__i?: string[] | string;
    supplier_id__i?: string[] | string;
    status_id__i?: number[] | string;
    is_auto?: string;
    type_id?: number;
}

export interface SearchSupplierProduct extends BaseSearch {
    warehouse_id?: string;
    quantity__lt?: number;
    quantity__gt?: number;
    status_id?: number;
    supplier_id?: string;
    supplier_debt?: string;
}

export type SearchTransferGoodParam = {
    [key in keyof SearchTransferGood]: string;
};

export type SearchSupplierProductParam = {
    [key in keyof SearchSupplierProduct]: string;
};

export enum TransferType {
    RECEIVED = 1,
    DELIVERY = 2,
}

export enum TransferStatus {
    PENDING = 1,
    APPROVED = 2,
    SENT = 3,
    TRANSFERRING = 4,
    FINISH = 5,
    REJECTED = 6,
    CANCELLED = 7,
}

export const TransferTypeNames: ItemParam[] = [
    { id: TransferType.RECEIVED, name: 'receive' },
    { id: TransferType.DELIVERY, name: 'delivery' },
];

export const TransferStatusNames: ItemParam[] = [
    { id: TransferStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-danger' },
    { id: TransferStatus.APPROVED, name: 'approved', className: 'badge badge-glow bg-info' },
    { id: TransferStatus.SENT, name: 'sent', className: 'badge badge-glow bg-primary' },
    { id: TransferStatus.TRANSFERRING, name: 'transferring', className: 'badge badge-glow bg-warning' },
    { id: TransferStatus.FINISH, name: 'finish', className: 'badge badge-glow bg-success' },
    { id: TransferStatus.REJECTED, name: 'rejected', className: 'badge badge-glow bg-secondary' },
    { id: TransferStatus.CANCELLED, name: 'cancelled', className: 'badge badge-glow bg-secondary' },
];

export interface TransferGoodProduct {
    quantity: number;
    product_child_id: string;
    order_ids?: string[];
}

export interface TransferGoodScanProduct {
    scan_quantity: number;
    product_child_id: string;
}

export interface AddTransferGood {
    warehouse_id: string;
    supplier_id?: string;
    type_id: TransferType;
    products: TransferGoodProduct[];
    supplier_product_ids?: string[];
}

export interface TransferGoodBarcodeProduct extends ProductBarcode {
    transfer_good_product_id: string;
    quantity: number;
    scan_quantity: number;
    order_id?: string;
    isDesign: boolean;
    productChildId: string; //shopify
    productId: string; //shopify
}

export interface SupplierProduct extends TransferGoodBarcodeProduct {
    supplier_product_id: string;
    warehouse_id: string;
    supplier_id: string;
    status_id: number;
    new_transfer_good: {
        id: string;
        barcode: string;
    };
    order_ids: string[];
}

export interface TransferGoodBarcode {
    id: string;
    warehouse_id: string;
    type_id: TransferType;
    status_id: TransferStatus;
    is_auto: boolean;
    sent_at?: string;
    supplier: {
        id: string;
        first_name: string;
        last_name: string;
        avatar: string;
    };
    warehouse: {
        id: string;
        name: string;
    };
    totalItems: number;
    poNumber: number;
    date: string;
    barcode: Barcode;
    products: TransferGoodBarcodeProduct[];
    orderProducts: TransferGoodBarcodeProduct[];
}

export default interface TransferGood extends BaseModel {
    barcode: string;
    warehouse_id: string;
    supplier_id: string;
    type_id: TransferType;
    status_id: TransferStatus;
    is_auto: boolean;
    sent_at?: string;
}
