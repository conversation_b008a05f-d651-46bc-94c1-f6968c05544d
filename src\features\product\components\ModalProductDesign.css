/* Thêm vào cuối file */
.design-table-container {
    position: relative;
    max-height: calc(100vh - 300px); /* <PERSON><PERSON><PERSON> cao màn hình trừ đi 300px */
    overflow: auto;
}

.design-table {
    position: relative;
    width: 100%;
}

.design-table thead,
.design-table tfoot {
    position: sticky;
    background-color: #fff; /* <PERSON>àu nền cho header và footer */
    z-index: 1;
}

.design-table thead {
    top: 0;
}

.design-table tfoot {
    bottom: 0;
    border-top: 2px solid #dee2e6; /* Thêm đường viền phía trên của footer */
}

/* <PERSON><PERSON><PERSON> bảo các cột có chiều rộng cố định */
.design-table th,
.design-table td {
    white-space: nowrap;
}

/* Responsive cho màn hình nhỏ hơn */
@media (max-height: 768px) {
    .design-table-container {
        max-height: calc(100vh - 250px);
    }
}

@media (max-height: 576px) {
    .design-table-container {
        max-height: calc(100vh - 200px);
    }
}
