export interface SSIDelivery {
    services: {
        carrier: string;
        carrier_name: string;
        service_name: string;
        service_code: string;
        total_price: number;
    }[];
    default_service?: {
        carrier: string;
        carrier_name: string;
        service_name: string;
        service_code: string;
        total_price: number;
    };
    success: true;
}

export interface SSIPrintLabel {
    order_id: number;
    order_number: string;
    carrier_name: string;
    tracking_numbers: string[];
    labels: string[];
    label_types: string[];
    success: true;
}

export interface SSITracking {
    results: {
        order_number: string;
        order_status: string;
        carrier_name: string;
        carrier_service: string;
        tracking_number?: string;
        tracking_url: string;
        shipment_date: string;
        tracking_status?: string;
        last_updated_date: string;
        tracking_events?: {
            event_datetime: string;
            status: string;
            details: string;
        }[];
    };
    success: boolean;
}
