import { REACT_APP_IS_MANUFACTURER } from 'constants/common';
import { find } from 'lodash';
import { ApiResponse, DataListResponse } from 'types/common';
import { TrueFalse } from 'types/common/Item';
import TransferGood, {
    AddTransferGood,
    SearchSupplierProduct,
    SearchTransferGood,
    SupplierProduct,
    TransferGoodBarcode,
    TransferGoodProduct,
    TransferGoodScanProduct,
} from 'types/TransferGood';
import Warehouse from 'types/Warehouse';
import Http from './http.class';

const http = new Http().instance;

const TransferGoodService = {
    async getList(params: SearchTransferGood, warehouses: Warehouse[], manufacturerId?: string) {
        if (REACT_APP_IS_MANUFACTURER && manufacturerId) {
            const warehouse = find(warehouses, { manufacturer_id: manufacturerId });
            if (warehouse) params.warehouse_id__i = warehouse.id;
            else return null;
        }
        const { data } = await http.get<ApiResponse<DataListResponse<TransferGood>>>('/transfer-good', { params });
        if (data.success) return data.data;
    },

    async getItem(id: string) {
        const { data } = await http.get<ApiResponse<TransferGood>>(`/transfer-good/${id}`);
        return data;
    },

    async getByBarcode(barcode: string) {
        const { data } = await http.get<ApiResponse<TransferGoodBarcode>>(`/transfer-good/barcode/${barcode}`);
        return data;
    },

    async add(param: AddTransferGood) {
        const { data } = await http.post<ApiResponse<TransferGood>>('/transfer-good', param);
        return data;
    },

    async deleteItem(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/transfer-good/${id}`);
        return data;
    },

    async updateStatus(id: string, statusId: number) {
        const { data } = await http.patch<ApiResponse<{}>>(`/transfer-good/${id}/updateStatus`, {
            status_id: statusId,
        });
        return data;
    },

    async finish(id: string, products: TransferGoodScanProduct[]) {
        const { data } = await http.patch<ApiResponse<{}>>(`/transfer-good/${id}/finish`, { products });
        return data;
    },

    async printItem(id: string) {
        const { data } = await http.get<ApiResponse<string>>(`/transfer-good/${id}/print`);
        return data;
    },

    async getSupplierProduct(searchParams: SearchSupplierProduct) {
        const params = { ...searchParams };
        if (params.supplier_debt) {
            if (+params.supplier_debt === TrueFalse.TRUE) params.quantity__gt = 0;
            else params.quantity__lt = 0;
            delete params.page;
            delete params.limit;
            delete params.supplier_debt;
        }
        const { data } = await http.get<ApiResponse<DataListResponse<SupplierProduct>>>('/supplier-product', {
            params,
        });
        if (data.success) return data.data;
    },

    async addProducts(id: string, products: TransferGoodProduct[]) {
        const { data } = await http.post<ApiResponse<{}>>(`/transfer-good/${id}/product`, { products });
        return data;
    },

    async updateProduct(id: string, tgProductId: string, quantity: number) {
        const { data } = await http.patch<ApiResponse<{}>>(`/transfer-good/${id}/product/${tgProductId}`, { quantity });
        return data;
    },

    async deleteProduct(id: string, tgProductId: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/transfer-good/${id}/product/${tgProductId}`);
        return data;
    },

    generateProductCard(
        imageUrl: string,
        name: string,
        code: string,
        size: string,
        color: string,
        barcode: string,
        barcodeImg: string, //align-items: center;
        isOnlyBarcode = false
    ) {
        return `
        <div style="width: 378px; ${
            isOnlyBarcode ? 'height: 100px; padding: 40px 10px 10px 10px;' : 'height: 227px; padding: 10px;'
        } border: 1px solid #ddd; border-radius: 8px; font-family: Arial, sans-serif; display: flex; flex-direction: column; justify-content: space-between; background: #fff; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
            ${
                isOnlyBarcode
                    ? ''
                    : `<div style="display: flex; height: 50%; border-bottom: 1px solid #ddd;">
            <div style="width: 50%; display: flex; justify-content: center; align-items: center; border-right: 1px solid #ddd;">
                <img src="${imageUrl}" alt="${name}" style="width: 80%; height: 80%; object-fit: contain; border-radius: 4px;">
            </div>
            <div style="width: 50%; padding: 10px; display: flex; flex-direction: column; justify-content: center;">
                <h3 style="margin: 0; font-size: 16px; font-weight: bold;">${name} | ${code}</h3>
                <p style="margin: 2px 0; font-size: 14px; color: #555;">${code}</p>
                <p style="margin: 2px 0; font-size: 14px;"><strong>Size:</strong> ${size}</p>
                <p style="margin: 2px 0; font-size: 14px;"><strong>Color:</strong> ${color}</p>
            </div>
            </div>`
            }
            <div style="text-align: center; height: 50%; display: flex; flex-direction: column; justify-content: center;">
            <img src="${barcodeImg}" alt="Barcode" style="width: 100%; height: 50px; object-fit: contain;">
            <p style="margin: 4px 0 0; font-size: 14px;">${barcode}</p>
            </div>
        </div>
    `;
    },
};

export default TransferGoodService;
