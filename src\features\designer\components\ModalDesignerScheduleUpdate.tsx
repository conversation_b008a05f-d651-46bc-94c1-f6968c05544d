import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import DesignerSchedule from 'types/DesignerSchedule';
import { toggleModalOpen } from 'utils/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import * as yup from 'yup';

interface IProps {
    show: boolean;
    designerSchedule: DesignerSchedule | undefined;
    isLoading: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: DesignerSchedule) => void;
}

export default function ModalDesignerScheduleUpdate({
    show,
    designerSchedule,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            start_date: yup.string().required(t('error.required')).trim(),
            end_date: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<DesignerSchedule>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (designerSchedule && show) {
            reset({
                id: designerSchedule.id,
                start_date: formatDateTime(designerSchedule.start_date, FORMAT_DATE.DB_DATE),
                end_date: formatDateTime(designerSchedule.end_date, FORMAT_DATE.DB_DATE),
            });
        } else {
            reset({
                start_date: '',
                end_date: '',
            });
        }
    }, [designerSchedule, show]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {t(designerSchedule ? 'designerSchedule.edit' : 'designerSchedule.add')}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('startDate')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('start_date')}
                                            type="date"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.start_date?.message),
                                            })}
                                        />
                                        <span className="error">{errors.start_date?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('endDate')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('end_date')}
                                            type="date"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.end_date?.message),
                                            })}
                                        />
                                        <span className="error">{errors.end_date?.message}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={t(designerSchedule ? 'update' : 'add')}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
