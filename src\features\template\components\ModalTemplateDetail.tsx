import classNames from 'classnames';
import Spinner from 'components/partials/Spinner';
import { ForwardedRef, forwardRef, ReactNode, useEffect, useImperativeHandle, useLayoutEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import DesignTemplateService from 'services/DesignTemplateService';
import { ItemStatus } from 'types/common/Item';
import DesignTemplate from 'types/DesignTemplate';
import { toggleModalOpen } from 'utils/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import { useUserName } from '../hooks/useUserName';
import './TemplateModal.css';

interface ModalTemplateDetailProps {
    show: boolean;
    itemId: string;
    onClose: () => void;
    showInfo?: boolean;
    modalClassName?: string;
    renderButtonActions?: ReactNode;
}

interface TemplateDetailData extends DesignTemplate {
    file_urls?: string[];
    description?: string;
}

export interface ModalTemplateDetailRef {
    getSelectedImage: () => string | undefined;
}

const ModalTemplateDetail = forwardRef(
    (
        {
            show,
            itemId,
            onClose,
            showInfo = true,
            modalClassName,
            renderButtonActions,
        }: Readonly<ModalTemplateDetailProps>,
        ref: ForwardedRef<ModalTemplateDetailRef | undefined>
    ) => {
        const { t } = useTranslation();
        useLayoutEffect(() => toggleModalOpen(show), [show]);

        const [templateData, setTemplateData] = useState<TemplateDetailData | null>(null);
        const [loading, setLoading] = useState(false);
        const [selectedImageIndex, setSelectedImageIndex] = useState(0);

        const { userName: creatorName, isLoading: isLoadingCreator } = useUserName({
            userId: templateData?.created_by,
            enabled: !!templateData?.created_by,
        });

        useEffect(() => {
            if (show && itemId) {
                fetchTemplateDetail();
            }
        }, [show, itemId]);

        const fetchTemplateDetail = async () => {
            try {
                setLoading(true);
                const data = await DesignTemplateService.getById(itemId);
                setTemplateData(data);
                setSelectedImageIndex(0);
            } catch {
                // Silent error handling
            } finally {
                setLoading(false);
            }
        };

        const handleImageSelect = (index: number) => {
            setSelectedImageIndex(index);
        };

        const handleClose = () => {
            setTemplateData(null);
            setSelectedImageIndex(0);
            onClose();
        };

        const getCreatorName = () => {
            if (isLoadingCreator) {
                return t('loading');
            }
            if (creatorName) {
                return creatorName;
            }
            if (templateData?.created_by) {
                return `User ID: ${templateData.created_by}`;
            }
            return t('unknown');
        };

        const getImageCount = () => {
            if (templateData?.file_urls?.length) {
                return templateData.file_urls.length;
            }
            return templateData?.file_ids?.length ?? 0;
        };

        useImperativeHandle(ref, () => ({
            getSelectedImage: () => templateData?.file_urls?.[selectedImageIndex],
        }));

        if (!show) return null;

        return (
            <>
                <div
                    className={`modal fade show modal-template-detail ${modalClassName ? modalClassName : ''}`}
                    style={{ display: 'block' }}
                >
                    <div className={`modal-dialog modal-xl modal-dialog-centered`}>
                        <div className="modal-content">
                            <div className="modal-header">
                                <h5 className="modal-title">{t('template.detail')}</h5>
                                <button type="button" className="btn-close" onClick={handleClose} aria-label="Close" />
                            </div>

                            <div className="modal-body">
                                {loading && (
                                    <div className="text-center py-5">
                                        <Spinner />
                                    </div>
                                )}

                                {!loading && templateData && (
                                    <div className="template-detail-container">
                                        <div className="image-thumbnails">
                                            {templateData.file_urls && templateData.file_urls.length > 0 ? (
                                                templateData.file_urls.map((url: string, index: number) => (
                                                    <div
                                                        key={index}
                                                        className={classNames('thumbnail-item', {
                                                            active: selectedImageIndex === index,
                                                        })}
                                                        onClick={() => handleImageSelect(index)}
                                                        title={`${t('photo')} ${index + 1}`}
                                                    >
                                                        <img
                                                            src={url}
                                                            alt={`${templateData.name} ${index + 1}`}
                                                            loading="lazy"
                                                        />
                                                    </div>
                                                ))
                                            ) : (
                                                <div className="no-images">
                                                    <p className="text-muted">{t('template.noImages')}</p>
                                                </div>
                                            )}
                                        </div>

                                        <div className="main-image">
                                            {templateData.file_urls && templateData.file_urls.length > 0 ? (
                                                <img
                                                    src={templateData.file_urls[selectedImageIndex]}
                                                    alt={templateData.name}
                                                    className="main-preview"
                                                />
                                            ) : (
                                                <div className="no-image-placeholder">
                                                    <p className="text-muted">{t('template.noImage')}</p>
                                                </div>
                                            )}

                                            {templateData.file_urls && templateData.file_urls.length > 1 && (
                                                <div className="image-counter">
                                                    {selectedImageIndex + 1} / {templateData.file_urls.length}
                                                </div>
                                            )}
                                        </div>

                                        {showInfo && (
                                            <div className="template-info">
                                                <div className="info-section">
                                                    <h3 className="template-name">{templateData.name}</h3>
                                                </div>

                                                <div className="info-section">
                                                    <div className="info-item">
                                                        <label>{t('statusName')}</label>
                                                        <span
                                                            className={classNames('badge', {
                                                                'bg-success':
                                                                    templateData.status_id === ItemStatus.ACTIVE,
                                                                'bg-danger':
                                                                    templateData.status_id !== ItemStatus.ACTIVE,
                                                            })}
                                                        >
                                                            {templateData.status_id === ItemStatus.ACTIVE
                                                                ? t('constants.active')
                                                                : t('constants.inactive')}
                                                        </span>
                                                    </div>

                                                    <div className="info-item">
                                                        <label>{t('partner.single')}</label>
                                                        <span
                                                            title={templateData.created_by}
                                                            className={isLoadingCreator ? 'text-muted' : ''}
                                                        >
                                                            {getCreatorName()}
                                                        </span>
                                                    </div>

                                                    <div className="info-item">
                                                        <label>{t('createdTime')}</label>
                                                        <span>
                                                            {templateData.created_at
                                                                ? formatDateTime(
                                                                      templateData.created_at,
                                                                      FORMAT_DATE.SHOW_DATE_TIME
                                                                  )
                                                                : t('unknown')}
                                                        </span>
                                                    </div>

                                                    <div className="info-item">
                                                        <label>{t('updatedTime')}</label>
                                                        <span>
                                                            {templateData.updated_at
                                                                ? formatDateTime(
                                                                      templateData.updated_at,
                                                                      FORMAT_DATE.SHOW_DATE_TIME
                                                                  )
                                                                : ''}
                                                        </span>
                                                    </div>

                                                    <div className="info-item">
                                                        <label>{t('totalImages')}</label>
                                                        <span>{getImageCount()}</span>
                                                    </div>
                                                </div>

                                                {templateData.description && (
                                                    <div className="info-section">
                                                        <label>{t('description')}</label>
                                                        <p className="description">{templateData.description}</p>
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="modal-footer">
                                {renderButtonActions ? (
                                    renderButtonActions
                                ) : (
                                    <button type="button" className="btn btn-secondary" onClick={handleClose}>
                                        {t('close')}
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
                {show && <div className="modal-backdrop fade show" />}
            </>
        );
    }
);

ModalTemplateDetail.displayName = 'ModalTemplateDetail';

export default ModalTemplateDetail;
