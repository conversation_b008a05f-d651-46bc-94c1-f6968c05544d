import { useTranslation } from 'react-i18next';
import PartnerInvoice from 'types/PartnerInvoice';
import { Paging } from '../../../types/common';
import { Link } from 'react-router-dom';
import { genTableIndex, getFieldHtml } from '../../../utils/common';
import { ItemStatusNames } from '../../../types/common/Item';
import User, { displayUserName } from '../../../types/User';
import { find } from 'lodash';
import UserLink from '../../../components/partials/UserLink';
import FormatNumber from '../../../components/partials/FormatNumber';
import { REACT_APP_IS_ADMIN } from '../../../constants/common';

interface IProps {
    items: PartnerInvoice[];
    partners: User[];
    paging: Paging;
}

export default function ListPartnerInvoice({ items, partners, paging }: Readonly<IProps>) {
    const { t } = useTranslation();

    const getUserLink = (id: string) => {
        const item = find(partners, { id });
        if (item) {
            return <UserLink to={`/user/edit/${item.id}`} avatar={item.avatar} name={displayUserName(item)} />;
        }
        return <></>;
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th className="text-center">{t('partnerInvoice.code')}</th>
                        <th className="text-center">{t('orderCode')}</th>
                        {REACT_APP_IS_ADMIN && <th>{t('partner.single')}</th>}
                        <th>{t('product.cost')}</th>
                        <th>{t('design.cost')}</th>
                        <th>{t('order.shippingCost')}</th>
                        <th>{t('partner.cost')}</th>
                        <th>{t('totalCost')}</th>
                        <th className="text-center">{t('statusName')}</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: PartnerInvoice, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td className="text-center">
                                <Link to={`/partnerInvoice/view/${item.id}`}>{item.barcode}</Link>
                            </td>
                            <td className="text-center">
                                <Link to={`/order/view/${item.order_id}`}>{item.order?.order_number}</Link>
                            </td>
                            {REACT_APP_IS_ADMIN && <td>{getUserLink(item.partner_id)}</td>}
                            <td>
                                <FormatNumber value={item.product_cost} isInput={false} renderText={(value) => value} />
                            </td>
                            <td>
                                <FormatNumber value={item.design_cost} isInput={false} renderText={(value) => value} />
                            </td>
                            <td>
                                <FormatNumber value={item.ship_cost} isInput={false} renderText={(value) => value} />
                            </td>
                            <td>
                                {item.partner_percent > 0 ? (
                                    item.partner_percent + '%'
                                ) : (
                                    <FormatNumber
                                        value={item.partner_cost}
                                        isInput={false}
                                        renderText={(value) => value}
                                    />
                                )}
                            </td>
                            <td>
                                <FormatNumber value={item.sum_cost} isInput={false} renderText={(value) => value} />
                            </td>
                            <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
