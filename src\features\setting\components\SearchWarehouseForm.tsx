import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { ItemStatusNames } from 'types/common/Item';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
}

export default function SearchWarehouseForm({ isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <SearchForm
            fields={[
                { name: 'search_text', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12' },
                {
                    name: 'status_id',
                    type: 'select',
                    label: t('statusName'),
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: false,
                        choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
                    },
                },
            ]}
            isLoading={isLoading}
        />
    );
}
