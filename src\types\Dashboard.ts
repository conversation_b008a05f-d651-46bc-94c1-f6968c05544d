export interface OrderCountParam {
    order_created_at__le?: string;
    order_created_at__ge?: string;
    status_id__i?: number;
    shop_id?: string;
    warehouse_id?: string;
    'warehouse.manufacturer_id'?: string;
    'shop.partner_id'?: string;
}

export interface ProductsSoldParam {
    'orderProducts.order.order_created_at__le'?: string;
    'orderProducts.order.order_created_at__ge'?: string;
    search_text?: string;
    'product.vendor'?: string;
    'product.product_type'?: string;
    sku__i?: string;
    'product.shop_id'?: string;
    limit?: number;
    page?: number;
}

export interface ProductsSoldList {
    'product-title': string;
    'product-vendor': string;
    'product-product_type': string;
    'product-status': string;
    'product-handle': string;
    'product-shop_id': number;
    'product_children-product_id': number;
    'product_children-title': string;
    'product_children-color': string | null;
    'product_children-price': string;
    'product_children-sku': string;
    'product_children-image': string;
    quantity_sold: number | null;
}

export interface TotalPriceList {
    month: string;
    total_revenue: string;
}

export interface TotalPriceParam {
    status_id__i?: string;
    shop__id?: string;
    warehouse_id?: string;
    'warehouse.manufacturer_id'?: string;
    'shop.partner_id'?: string;
    year: number;
}

export interface CountOrderByStatusParam {
    order_created_at__le?: string;
    order_created_at__ge?: string;
    shop_id?: string;
    warehouse_id?: string;
    'warehouse.manufacturer_id'?: string;
    'shop.partner_id'?: string;
}

export interface OrderCountStatusResponse {
    pending: number;
    requested: number;
    enough_stock: number;
    manufacturing: number;
    wait_pack: number;
    packed: number;
    transporting: number;
    finish: number;
}
