import classNames from 'classnames';
import UserLink from 'components/partials/UserLink';
import { REACT_APP_IS_ADMIN, REACT_APP_IS_DESIGNER, REACT_APP_IS_MANUFACTURER } from 'constants/common';
import { find } from 'lodash';
import { CheckCircle, CheckSquare, Download, Edit } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import OrderService from 'services/OrderService';
import { useAuthStore } from 'stores/authStore';
import { Paging } from 'types/common';
import Order, { OrderStatusNames } from 'types/Order';
import Shop from 'types/Shop';
import User, { displayUserName, UserRole } from 'types/User';
import Warehouse from 'types/Warehouse';
import { genTableIndex, getFieldHtml, getFieldInArrayObject, showToast } from 'utils/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';

interface IProps {
    items: Order[];
    shops: Shop[];
    warehouses: Warehouse[];
    customers: User[];
    partners: User[];
    paging: Paging;
    onChooseWarehouse: (id: string) => void;
    onDoneDesigner: (id: string) => void;
    isFromCustomer?: boolean;
    designers?: User[];
}

export default function ListOrder({
    items,
    shops,
    warehouses,
    customers,
    partners,
    paging,
    onChooseWarehouse,
    onDoneDesigner,
    isFromCustomer = false,
    designers = [],
}: Readonly<IProps>) {
    const user = useAuthStore((state) => state.user);
    const { t } = useTranslation();

    const getUserLink = (shopId: string) => {
        const shop = find(shops, { id: shopId });
        const item = find(partners, { id: shop?.partner_id });
        if (item) {
            return <UserLink to={`/user/edit/${item.id}`} avatar={item.avatar} name={displayUserName(item)} />;
        }
        return <></>;
    };

    const printItem = async (orderId: string) => {
        const response = await OrderService.printItem(orderId);
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th className="text-center">{t('orderCode')}</th>
                        {!REACT_APP_IS_DESIGNER && <th>{t('shop.single')}</th>}
                        {!REACT_APP_IS_MANUFACTURER && !REACT_APP_IS_DESIGNER && <th>{t('warehouse.single')}</th>}
                        {REACT_APP_IS_ADMIN && <th>{t('partner.single')}</th>}
                        {!isFromCustomer && <>{!REACT_APP_IS_DESIGNER && <th>{t('customer.single')}</th>}</>}
                        {(REACT_APP_IS_ADMIN || REACT_APP_IS_MANUFACTURER) && <th>{t('designer.single')}</th>}
                        {!REACT_APP_IS_DESIGNER && <th className="text-end">{t('totalPrice')}</th>}
                        {!REACT_APP_IS_DESIGNER && <th className="text-center">{t('statusName')}</th>}
                        <th className="text-center">{t('createdTime')}</th>
                        {REACT_APP_IS_DESIGNER && <th className="text-center">{t('order.doneDesign')}</th>}
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: Order, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td className="text-center">
                                {REACT_APP_IS_DESIGNER ? (
                                    <a
                                        href={`https://drive.google.com/drive/folders/${item.gg_folder_id}`}
                                        target="_blank"
                                    >
                                        {item.order_number}
                                    </a>
                                ) : (
                                    <Link to={`/order/view/${item.id}`}>{item.order_number}</Link>
                                )}
                                <p>
                                    {item.json.name}{' '}
                                    {item.is_done_designer && <CheckCircle className="text-info" size={14} />}
                                </p>
                            </td>
                            {!REACT_APP_IS_DESIGNER && <td>{getFieldInArrayObject(shops, item.shop_id)}</td>}
                            {!REACT_APP_IS_MANUFACTURER && !REACT_APP_IS_DESIGNER && (
                                <td className={classNames({ 'text-center': !item.warehouse_id })}>
                                    {item.warehouse_id
                                        ? getFieldInArrayObject(warehouses, item.warehouse_id)
                                        : user?.role_id === UserRole.ADMIN && (
                                              <Edit
                                                  size={14}
                                                  className="text-primary cursor-pointer"
                                                  onClick={() => onChooseWarehouse(item.id!)}
                                              />
                                          )}
                                </td>
                            )}
                            {REACT_APP_IS_ADMIN && <td>{getUserLink(item.shop_id)}</td>}
                            {!isFromCustomer && (
                                <>
                                    {!REACT_APP_IS_DESIGNER && (
                                        <td>
                                            <Link to={`/user/edit/${item.customer_id}`}>
                                                {getFieldInArrayObject(customers, item.customer_id)}
                                            </Link>
                                        </td>
                                    )}
                                </>
                            )}
                            {(REACT_APP_IS_ADMIN || REACT_APP_IS_MANUFACTURER) && (
                                <td>{designers?.find((d) => d.id === item.designer_id)?.name}</td>
                            )}
                            {!REACT_APP_IS_DESIGNER && (
                                <td className="text-end">
                                    {item.total_price} {item.currency}
                                </td>
                            )}
                            {!REACT_APP_IS_DESIGNER && (
                                <td className="text-center">{getFieldHtml(OrderStatusNames, item.status_id, t)}</td>
                            )}
                            <td className="text-center">
                                {formatDateTime(item.order_created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                            </td>
                            {REACT_APP_IS_DESIGNER && (
                                <td className="text-center">
                                    {!item.is_done_designer && (
                                        <a title="Done Design" href="javascript:void(0)">
                                            <CheckSquare
                                                size={14}
                                                className="text-primary cursor-pointer"
                                                onClick={() => onDoneDesigner(item.id!)}
                                            />
                                        </a>
                                    )}
                                    <a title="Download" href="javascript:void(0)">
                                        <Download
                                            size={14}
                                            className="text-primary cursor-pointer ms-2"
                                            onClick={() => printItem(item.id!)}
                                        />
                                    </a>
                                </td>
                            )}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
