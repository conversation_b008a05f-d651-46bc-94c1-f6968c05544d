import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, REACT_APP_IS_PARTNER } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import CountryService from 'services/CountryService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import { useAuthStore } from 'stores/authStore';
import { ItemStatus } from 'types/common/Item';
import { SearchShop } from 'types/Shop';
import { SearchUser, SearchUserParam, UserRole, UserRoleNames } from 'types/User';
import { getFieldInArrayObject, showToast } from 'utils/common';
import ListUser from '../components/ListUser';
import SearchUserForm from '../components/SearchUserForm';

export default function UserList() {
    const user = useAuthStore((state) => state.user);
    const { type } = useParams();
    const { queryParams, setQueryParams } = useQueryParams<SearchUserParam>();
    const paramConfig: SearchUserParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            country_id__i: queryParams.country_id__i,
            status_id__i: queryParams.status_id__i,
            shop_id__i: queryParams.shop_id__i,
            verified_email: queryParams.verified_email,
        },
        isUndefined
    );

    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState('');
    const navigate = useNavigate();
    const { t } = useTranslation();

    const roleId = +getFieldInArrayObject(UserRoleNames, type ?? '', 'id', '0', 'name');
    if (!roleId) {
        navigate('/not-found');
    }
    paramConfig.role_id__i = roleId.toString();

    const { data: countries } = useQuery({
        queryKey: [QUERY_KEY.COUNTRIES],
        queryFn: () => CountryService.getList(),
    });

    const { data: shops } = useQuery({
        enabled: roleId === UserRole.CUSTOMER && !!user,
        queryKey: [
            QUERY_KEY.SHOPS,
            { status_id__i: ItemStatus.ACTIVE.toString(), partner_id__i: REACT_APP_IS_PARTNER ? user?.id : undefined },
        ],
        queryFn: ({ queryKey }) => ShopService.getList(queryKey[1] as SearchShop),
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!roleId && !!user,
        queryKey: [QUERY_KEY.USERS, paramConfig, user],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser, user!.id),
        placeholderData: keepPreviousData,
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => UserService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    return (
        <>
            <Helmet>
                <title>{t(`${type}.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.multiple`)}
                contextMenu={[
                    {
                        text: t(`${type}.add`),
                        to: `/user/add/${type}`,
                        icon: 'PLUS',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchUserForm
                        isLoading={isLoading || isRefetching}
                        roleId={roleId}
                        countries={countries ?? []}
                        shops={shops?.items ?? []}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListUser
                                items={data.items}
                                type={type}
                                countries={countries ?? []}
                                shops={shops?.items ?? []}
                                paging={data.pagination}
                                handleDelete={handleDelete}
                            />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
