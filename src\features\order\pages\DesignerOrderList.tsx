import { keepPreviousData, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import OrderService from 'services/OrderService';
import { useAuthStore } from 'stores/authStore';
import { SearchOrder, SearchOrderParam } from 'types/Order';
import { showToast } from 'utils/common';
import ListOrder from '../components/ListOrder';
import SearchOrderForm from '../components/SearchOrderForm';

export default function DesignerOrderList() {
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);

    const { queryParams, setQueryParams } = useQueryParams<SearchOrderParam>();
    const paramConfig: SearchOrderParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            is_done_designer: queryParams.is_done_designer,
        },
        isUndefined
    );

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.ORDERS, paramConfig, user, false],
        queryFn: ({ queryKey }) => OrderService.getList(queryKey[1] as SearchOrder, user!.id, false),
        placeholderData: keepPreviousData,
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDoneDesigner = async (id: string) => {
        const response = await OrderService.updateDoneDesigner([id]);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    return (
        <>
            <Helmet>
                <title>{t('order.multiple')}</title>
            </Helmet>
            <ContentHeader title={t(`order.multiple`)} />
            <div className="content-body">
                <div className="col-12">
                    <SearchOrderForm
                        isLoading={isLoading || isRefetching}
                        customerId="1"
                        customers={[]}
                        partners={[]}
                        shops={[]}
                        warehouses={[]}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListOrder
                                items={data.items}
                                shops={[]}
                                warehouses={[]}
                                customers={[]}
                                partners={[]}
                                paging={data.pagination}
                                onChooseWarehouse={() => {}}
                                onDoneDesigner={handleDoneDesigner}
                            />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
