import ContentHeader from 'components/partials/ContentHeader';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { TransferType, TransferTypeNames } from 'types/TransferGood';
import { getFieldInArrayObject } from 'utils/common';
import AddTransferGoodForm from '../components/AddTransferGoodForm';

export default function TransferGoodAdd() {
    const { type } = useParams();
    const [typeId, setTypeId] = useState(0);
    const navigate = useNavigate();
    const { t } = useTranslation();

    useEffect(() => {
        const tId = +getFieldInArrayObject(TransferTypeNames, type ?? '', 'id', '0', 'name');
        if (tId === TransferType.RECEIVED) setTypeId(tId);
        else navigate('/not-found');
    }, [type]);

    return (
        <>
            <Helmet>
                <title>{t(`${type}.add`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.add`)}
                breadcrumbs={[
                    {
                        text: t(`${type}.multiple`),
                        to: `/transfer/list/${type}`,
                    },
                    {
                        text: t(`${type}.add`),
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <AddTransferGoodForm typeId={typeId} />
                </div>
            </div>
        </>
    );
}
