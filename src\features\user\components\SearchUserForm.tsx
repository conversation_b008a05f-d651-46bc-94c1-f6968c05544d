import SearchForm from 'components/partials/SearchForm';
import { filter } from 'lodash';
import { useTranslation } from 'react-i18next';
import Country from 'types/Country';
import Shop from 'types/Shop';
import { UserRole } from 'types/User';
import { ItemParamModel, ItemStatusNames, YesNoChoices } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { convertConstantToSelectOptions, convertObjectToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
    roleId: number | undefined;
    countries: Country[];
    shops: Shop[];
}

export default function SearchUserForm({ isLoading, roleId, countries, shops }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        { name: 'search_text', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12', show: true },
        {
            name: 'status_id__i',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
            },
            show: roleId !== UserRole.CUSTOMER,
        },
        {
            name: 'country_id__i',
            type: 'select',
            label: t('country'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(countries as ItemParamModel[], true),
            },
            show: true,
        },
        {
            name: 'shop_id__i',
            type: 'select',
            label: t('shop.single'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(shops as ItemParamModel[], true),
            },
            show: false, // roleId === UserRole.CUSTOMER,
        },
        {
            name: 'verified_email',
            type: 'select',
            label: t('verifiedEmail'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
            },
            show: roleId === UserRole.CUSTOMER,
        },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
