import classNames from 'classnames';
import UserLink from 'components/partials/UserLink';
import { REACT_APP_MAIN_PARTNER_ID, REACT_APP_MAIN_SUPPLIER_ID } from 'constants/common';
import { includes, join, split } from 'lodash';
import { CheckCircle, Eye, Star, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Country from 'types/Country';
import Shop from 'types/Shop';
import User, { displayUserName } from 'types/User';
import { Paging } from 'types/common';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import { genTableIndex, getFieldHtml, getFieldInArrayObject } from 'utils/common';

interface IProps {
    items: User[];
    type: string | undefined;
    countries: Country[];
    shops: Shop[];
    paging: Paging;
    handleDelete: (id: string) => void;
}

export default function ListUser({ items, type, countries, shops, paging, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();

    const getAddress = (address: string) => {
        const parts = split(address, ';').filter((part) => !!part);
        return join(parts, ', ');
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th>{t('fullName')}</th>
                        {includes(['supplier', 'partner'], type) && <th className="text-center">{t('code')}</th>}
                        <th>{t('phoneNumber')}</th>
                        <th>{t('email')}</th>
                        <th>{t('address')}</th>
                        <th>{t('country')}</th>
                        {type === 'customer' && <th>{t('shop.single')}</th>}
                        {type !== 'customer' && (
                            <>
                                <th className="text-center">{t('statusName')}</th>
                                <th className="thAction1"></th>
                            </>
                        )}
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: User, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>
                                <UserLink
                                    to={`/user/edit/${item.id}`}
                                    avatar={item.avatar}
                                    name={
                                        includes(['supplier', 'partner'], type)
                                            ? item.first_name
                                            : displayUserName(item)
                                    }
                                />
                                {((type === 'partner' && item.id === REACT_APP_MAIN_PARTNER_ID) ||
                                    (type === 'supplier' && item.id === REACT_APP_MAIN_SUPPLIER_ID)) && (
                                    <Star className="text-warning ms-50" size={14} />
                                )}
                            </td>
                            {includes(['supplier', 'partner'], type) && (
                                <td className="text-center">{item.last_name}</td>
                            )}
                            <td>{item.phone_number}</td>
                            <td>
                                {type === 'customer' && (
                                    <CheckCircle
                                        size={14}
                                        className={classNames('me-50', {
                                            'text-primary': item.verified_email,
                                            'text-danger': !item.verified_email,
                                        })}
                                    />
                                )}
                                <a href={`mailto:${item.email}`} target="_blank">
                                    {item.email}
                                </a>
                            </td>
                            <td>{getAddress(item.address)}</td>
                            <td>{getFieldInArrayObject(countries, item.country_id)}</td>
                            {type === 'customer' && (
                                <td>{item.shop_id && getFieldInArrayObject(shops, item.shop_id)}</td>
                            )}
                            {type !== 'customer' && (
                                <>
                                    <td className="text-center">{getFieldHtml(ItemStatusNames, item.status_id, t)}</td>
                                    <td className="text-center">
                                        {type === 'designer' && (
                                            <Link
                                                to={`/designer/holiday-schedule?designer_id__i=${item.id}&mode=view`}
                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                title="View Holiday Schedules"
                                                target="_blank"
                                            >
                                                <Eye size={14} />
                                            </Link>
                                        )}
                                        {item.status_id !== ItemStatus.ACTIVE && (
                                            <button
                                                type="button"
                                                title={t('delete')}
                                                className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                onClick={() => handleDelete(item.id!)}
                                            >
                                                <Trash2 size={14} />
                                            </button>
                                        )}
                                    </td>
                                </>
                            )}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
