import { BaseModel, BaseSearch } from './common';
import { ItemStatus } from './common/Item';

export interface SearchNotification extends BaseSearch {
    user_id?: string;
    status_id?: number;
}

export enum NotificationItemType {
    ORDER = 1,
    TRANSFER_GOOD = 2,
}

export default interface Notification extends BaseModel {
    user_id: number;
    title: string;
    content: string;
    status_id: ItemStatus;
    item_id: string;
    item_type_id: NotificationItemType;
}
