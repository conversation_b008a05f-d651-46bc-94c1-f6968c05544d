import { REACT_APP_IS_DESIGNER, REACT_APP_IS_MANUFACTURER, REACT_APP_IS_PARTNER } from 'constants/common';
import { isArray, join } from 'lodash';
import { ApiResponse, DataListResponse } from 'types/common';
import { ItemCount } from 'types/common/Item';
import Order, { OrderProductBarcode, SearchOrder } from 'types/Order';
import Http from './http.class';
import { SSIDelivery, SSIPrintLabel, SSITracking } from '../types/Starshipit';

const http = new Http().instance;

const OrderService = {
    async getList(searchParams: SearchOrder, userId?: string, isNoWarehouse?: boolean) {
        const params = { ...searchParams };
        if (isNoWarehouse) params.warehouse_id__n = '1';
        if (REACT_APP_IS_MANUFACTURER && userId) {
            params['warehouse.manufacturer_id'] = userId;
        }
        if (REACT_APP_IS_PARTNER && userId) {
            params['shop.partner_id__i'] = userId;
        } else if (params.partner_id__i) {
            params['shop.partner_id__i'] = isArray(params.partner_id__i)
                ? join(params.partner_id__i, ',')
                : params.partner_id__i.toString();
            delete params.partner_id__i;
        }
        if (REACT_APP_IS_DESIGNER && userId) {
            params.designer_id__i = userId;
        }
        const { data } = await http.get<ApiResponse<DataListResponse<Order>>>('/order', { params });
        if (data.success) return data.data;
    },

    async countNoWarehouse() {
        const { data } = await http.get<ApiResponse<ItemCount>>('/order/count-no-warehouse');
        if (data.success) return data.data.count;
        return 0;
    },

    async getItem(id: string) {
        const { data } = await http.get<ApiResponse<Order>>(`/order/${id}`);
        if (data.success) return data.data;
    },

    async printItem(id: string) {
        const { data } = await http.get<ApiResponse<string>>(`/order/${id}/print`);
        return data;
    },

    async updateWarehouse(id: string, warehouseId: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/order/${id}/warehouse`, { warehouse_id: warehouseId });
        return data;
    },

    async getByProductBarcode(barcode: string, isAllProduct = 1) {
        const params = isAllProduct === 1 ? { isAllProduct } : {};
        const { data } = await http.get<ApiResponse<OrderProductBarcode>>(`/order/barcode/${barcode}`, { params });
        return data;
    },

    async resendShipping(id: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/order/${id}/resend-shipping`);
        return data;
    },

    async updateDoneDesigner(order_ids: string[]) {
        const { data } = await http.patch<ApiResponse<{}>>(`/order/complete-designs`, { order_ids });
        return data;
    },

    async getByCode(code: string) {
        const { data } = await http.get<ApiResponse<Order>>(`/order/code/${code}`);
        return data;
    },

    async getDeliveryServices(id: string) {
        const { data } = await http.get<ApiResponse<SSIDelivery>>(`/order/${id}/ssi-shipping-service`);
        return data;
    },

    async printLabel(id: string, carrier: string, serviceCode: string) {
        const { data } = await http.post<ApiResponse<SSIPrintLabel>>(`/order/${id}/ssi-order-shipment`, {
            carrier,
            carrier_service_code: serviceCode,
        });
        return data;
    },

    async getTracks(id: string) {
        const { data } = await http.post<ApiResponse<SSITracking>>(`/order/${id}/ssi-track`);
        return data;
    },

    async transferPrint(
        barcode: string,
        body: {
            product: {
                image: string;
                name: string;
                code: string;
                size: string;
                color: string;
                barcode: string;
                barcodeImg: string;
            };
        }
    ) {
        const { data } = await http.post<ApiResponse<string>>(`/order/${barcode}/print-transfer`, body);
        return data;
    },
};

export default OrderService;
