import { Edit, Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import DesignerSchedule from 'types/DesignerSchedule';
import { FORMAT_DATE, formatDateTime } from 'utils/date';

interface IProps {
    items: DesignerSchedule[];
    handleEdit: (id: string) => void;
    handleDelete: (id: string) => void;
    mode: string | null;
}

export default function ListDesignerSchedule({ items, handleEdit, handleDelete, mode }: Readonly<IProps>) {
    const { t } = useTranslation();
    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th className="text-center">{t('startDate')}</th>
                        <th className="text-center">{t('endDate')}</th>
                        <th className="thAction3"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: DesignerSchedule, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{index + 1}</td>
                            <td className="text-center">
                                {formatDateTime(item.start_date, FORMAT_DATE.SHOW_ONLY_DATE)}
                            </td>
                            <td className="text-center">{formatDateTime(item.end_date, FORMAT_DATE.SHOW_ONLY_DATE)}</td>
                            <td>
                                {mode && mode === 'view' ? (
                                    ''
                                ) : (
                                    <>
                                        <button
                                            type="button"
                                            title={t('edit')}
                                            className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                            onClick={() => handleEdit(item.id!)}
                                        >
                                            <Edit size={14} />
                                        </button>
                                        <button
                                            type="button"
                                            title={t('delete')}
                                            className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                            onClick={() => handleDelete(item.id!)}
                                        >
                                            <Trash2 size={14} />
                                        </button>
                                    </>
                                )}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
