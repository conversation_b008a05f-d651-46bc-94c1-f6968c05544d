import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import {
    PAGINATION,
    QUERY_KEY,
    REACT_APP_IS_ADMIN,
    REACT_APP_MAIN_PARTNER_ID,
    VITE_REACT_APP_MAIN_SHOP_ID,
} from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { useShopProductForm } from 'hooks/useShopProductForm';
import { isEmpty, isUndefined, omitBy } from 'lodash';
import { useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import ProductService from 'services/ProductService';
import UserService from 'services/UserService';
import WarehouseService from 'services/WarehouseService';
import { SearchProduct, SearchProductParam } from 'types/Product';
import { SearchUser, UserRole } from 'types/User';
import { ItemStatus } from 'types/common/Item';
import { showToast } from 'utils/common';
import ListProduct from '../components/ListProduct';
import SearchProductForm from '../components/SearchProductForm';
import { SearchWarehouse } from '../../../types/Warehouse';
import DesignTemplateService from '../../../services/DesignTemplateService';
import { SearchTemplateParam } from '../../../types/DesignTemplate';
import { useAuthStore } from '../../../stores/authStore';
import ShopService from '../../../services/ShopService';
import { SearchShop } from '../../../types/Shop';
import ModalShopAssignment from '../components/ModalShopAssignment';

interface IProps {
    stockWarning: boolean;
    isShopProductRoute?: boolean;
}

export default function ProductList({ stockWarning, isShopProductRoute = false }: Readonly<IProps>) {
    const { id, domain } = useParams();
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);
    const [showSync, setShowSync] = useState(false);
    const [showShopAssignment, setShowShopAssignment] = useState(false);
    const [selectedProductId, setSelectedProductId] = useState('');
    const { queryParams, setQueryParams } = useQueryParams<SearchProductParam>();
    const [initShopIds, setInitShopIds] = useState<string[]>([]);

    // Get shop_id and domain based on route type
    const shopId = isShopProductRoute ? queryParams.shop_id : id;
    const shopDomain = isShopProductRoute ? queryParams.domain : domain;

    const paramConfig: SearchProductParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            id__i: queryParams.id__i,
            search_text: queryParams.search_text,
            shop_id: shopId,
            status__i: queryParams.status__i,
            warehouse_id: queryParams.warehouse_id,
            vendor: queryParams.vendor,
            product_type__i: queryParams.product_type__i,
            sku: queryParams.sku,
            is_supplier_product: queryParams.is_supplier_product ?? '1',
            'children.preset': queryParams['children.preset'],
            'children.stocks.warehouse_id': queryParams['children.stocks.warehouse_id'],
            include_children: queryParams.include_children,
        },
        isUndefined
    );

    const { data: warehouses } = useQuery({
        enabled: !!shopId,
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
    });

    const { data: suppliers } = useQuery({
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.SUPPLIER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data: templates } = useQuery({
        enabled: queryParams.is_supplier_product === '0' && !!user,
        queryKey: [
            QUERY_KEY.DESIGN_TEMPLATES,
            { status_id: ItemStatus.ACTIVE.toString(), created_by__i: REACT_APP_IS_ADMIN ? undefined : user!.id },
        ],
        queryFn: ({ queryKey }) => DesignTemplateService.getList(queryKey[1] as SearchTemplateParam),
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: (!!shopId && !isEmpty(warehouses)) || !!paramConfig['children.stocks.warehouse_id'],
        queryKey: [QUERY_KEY.PRODUCTS, paramConfig, stockWarning, warehouses],
        queryFn: ({ queryKey }) => ProductService.getList(queryKey[1] as SearchProduct, stockWarning, warehouses ?? []),
        placeholderData: keepPreviousData,
    });

    const { data: rootProducts } = useQuery({
        enabled: !!shopId && !isEmpty(warehouses) && !stockWarning,
        queryKey: [
            QUERY_KEY.PRODUCTS,
            {
                shop_id: VITE_REACT_APP_MAIN_SHOP_ID,
                status__i: 'active',
                is_supplier_product: '1',
                include_children: '0',
            },
            stockWarning,
            warehouses,
        ],
        queryFn: ({ queryKey }) => ProductService.getList(queryKey[1] as SearchProduct, false, []),
        placeholderData: keepPreviousData,
    });

    const syncMutation = useMutation({
        mutationFn: (id: string) => ProductService.syncProducts(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowSync(false);
                    refetch();
                }
            }
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleOpenShopAssignment = (productId: string) => {
        setSelectedProductId(productId);
        setShowShopAssignment(true);
    };

    const handleShopAssignmentUpdate = async (selectedShopIds: string[]) => {
        if (!selectedProductId) return;
        const assignProductToShopsRes = await ProductService.assignProductToShop(selectedProductId, selectedShopIds);
        showToast(assignProductToShopsRes.success, assignProductToShopsRes.messages);
        refetch();
        setShowShopAssignment(false);
        setSelectedProductId('');
    };

    const handleCloseShopAssignment = () => {
        setShowShopAssignment(false);
        setSelectedProductId('');
    };

    const syncProducts = async () => {
        if (id) {
            syncMutation.mutate(id);
        }
    };

    const actionMenu = () => {
        syncMutation.reset();
        setShowSync(true);
    };

    const { data: partnerDatas } = useQuery({
        enabled: REACT_APP_IS_ADMIN && isShopProductRoute,
        queryKey: [
            QUERY_KEY.USER,
            { role_id__i: UserRole.PARTNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data: shopDatas, isRefetching: isRefetchingShops } = useQuery({
        enabled: !!user && isShopProductRoute && !!queryParams.partner_id,
        queryKey: [
            QUERY_KEY.SHOPS,
            {
                status_id__i: ItemStatus.ACTIVE.toString(),
                ...(isShopProductRoute && queryParams.partner_id && { partner_id__i: queryParams.partner_id }),
            },
        ],
        queryFn: ({ queryKey }) => ShopService.getList(queryKey[1] as SearchShop),
        placeholderData: keepPreviousData,
    });

    // Create empty shop data when no partner is selected in shopProduct route
    const effectiveShopDatas =
        isShopProductRoute && !queryParams.partner_id
            ? { items: [], pagination: { count_item: 0, total_page: 0, current_page: 1, limit: 10 } }
            : shopDatas;

    // Use custom hook for shop product form logic
    useShopProductForm({
        shops: effectiveShopDatas?.items ?? [],
        isShopProductRoute,
    });

    const { data: allPartnerDatas } = useQuery({
        enabled: !isShopProductRoute,
        queryKey: [
            QUERY_KEY.USER,
            { role_id__i: UserRole.PARTNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data: allShopDatas } = useQuery({
        enabled: !isShopProductRoute,
        queryKey: [
            QUERY_KEY.SHOPS,
            {
                status_id__i: ItemStatus.ACTIVE.toString(),
            },
        ],
        queryFn: ({ queryKey }) => ShopService.getList(queryKey[1] as SearchShop),
        placeholderData: keepPreviousData,
    });
    const allPartnersFiltered = useMemo(
        () => allPartnerDatas?.items.filter((item) => item.id !== REACT_APP_MAIN_PARTNER_ID) ?? [],
        [allPartnerDatas]
    );
    const allShops = useMemo(() => allShopDatas?.items ?? [], [allShopDatas]);

    return (
        <>
            <Helmet>
                <title>{t(stockWarning ? 'product.stockWarning' : 'product.multiple')}</title>
            </Helmet>
            <ContentHeader
                title={t(stockWarning ? 'product.stockWarning' : 'product.multiple')}
                // contextMenu={
                //     stockWarning
                //         ? []
                //         : [
                //               {
                //                   text: t('product.sync'),
                //                   icon: 'PLUS',
                //                   fnCallBack: { actionMenu },
                //               },
                //           ]
                // }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchProductForm
                        isLoading={isLoading || isRefetching}
                        stockWarning={stockWarning}
                        warehouses={warehouses ?? []}
                        suppliers={suppliers?.items ?? []}
                        hiddenWarehouse={!!paramConfig['children.stocks.warehouse_id']}
                        isShopProductRoute={isShopProductRoute}
                        partners={partnerDatas?.items ?? []}
                        shops={effectiveShopDatas?.items ?? []}
                        isLoadingShops={isRefetchingShops}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {isShopProductRoute && !shopId && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListProduct
                                items={[]}
                                stockWarning={stockWarning}
                                warehouses={warehouses ?? []}
                                warehouseId={queryParams.warehouse_id}
                                domain={shopDomain}
                                isSupplierProduct={paramConfig.is_supplier_product === '1'}
                                templates={templates?.items ?? []}
                                rootProducts={rootProducts?.items ?? []}
                                paging={{ count_item: 0, total_page: 0, current_page: 1, limit: 10 }}
                                refetch={() => refetch()}
                            />
                        </div>
                    )}
                    {data && !isLoading && !isRefetching && (!isShopProductRoute || !!shopId) && (
                        <div className="card">
                            <ListProduct
                                items={data.items}
                                stockWarning={stockWarning}
                                warehouses={warehouses ?? []}
                                warehouseId={queryParams.warehouse_id}
                                domain={shopDomain}
                                isSupplierProduct={paramConfig.is_supplier_product === '1'}
                                templates={templates?.items ?? []}
                                rootProducts={rootProducts?.items ?? []}
                                paging={data.pagination}
                                refetch={() => refetch()}
                                onOpenShopAssignment={handleOpenShopAssignment}
                                setInitShopIds={setInitShopIds}
                                isShopProductRoute={isShopProductRoute}
                            />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showSync}
                        text={t('product.syncConfirm')}
                        btnDisabled={syncMutation.isPending}
                        changeShow={(s: boolean) => setShowSync(s)}
                        submitAction={syncProducts}
                    />
                    <ModalShopAssignment
                        show={showShopAssignment}
                        onClose={handleCloseShopAssignment}
                        allShops={allShops}
                        allPartnersFiltered={allPartnersFiltered}
                        onUpdate={handleShopAssignmentUpdate}
                        initShopIds={initShopIds}
                    />
                </div>
            </div>
        </>
    );
}
