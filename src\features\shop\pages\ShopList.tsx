import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, REACT_APP_IS_ADMIN, REACT_APP_IS_PARTNER } from 'constants/common';
import ListShop from 'features/shop/components/ListShop';
import ModalShopUpdate from 'features/shop/components/ModalShopUpdate';
import SearchShopForm from 'features/shop/components/SearchShopForm';
import useQueryParams from 'hooks/useQueryParams';
import { find, includes, isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import CountryService from 'services/CountryService';
import LocationService from 'services/LocationService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import { useAuthStore } from 'stores/authStore';
import { ShopLocation } from 'types/Location';
import { SearchShop, SearchShopParam, UpdateShop } from 'types/Shop';
import { SearchUser, UserRole } from 'types/User';
import { showToast } from 'utils/common';
import ModalShopLocations from '../components/ModalShopLocations';

export default function ShopList() {
    const { t } = useTranslation();
    const [itemId, setItemId] = useState('');
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const [showLocation, setShowLocation] = useState(false);
    const [shopLocations, setShopLocations] = useState<ShopLocation[]>([]);
    const user = useAuthStore((state) => state.user);

    const { queryParams, setQueryParams } = useQueryParams<SearchShopParam>();
    const paramConfig: SearchShopParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            country_id__i: queryParams.country_id__i,
            status_id__i: queryParams.status_id__i,
            partner_id__i: queryParams.partner_id__i,
        },
        isUndefined
    );

    const { data: countries } = useQuery({
        queryKey: [QUERY_KEY.COUNTRIES],
        queryFn: () => CountryService.getList(),
    });

    const { data: partners } = useQuery({
        enabled: REACT_APP_IS_ADMIN,
        queryKey: [QUERY_KEY.USER, { role_id__i: UserRole.PARTNER.toString() }],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.SHOPS, paramConfig, user],
        queryFn: ({ queryKey }) => ShopService.getList(queryKey[1] as SearchShop, user!.id),
        placeholderData: keepPreviousData,
    });

    const updateMutation = useMutation({
        mutationFn: (param: UpdateShop) => ShopService.update(param, param.id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowUpdate(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => ShopService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const updateItem = (body: UpdateShop) => updateMutation.mutate(body);

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const handleEdit = (id: string) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const showLocations = async (id: string) => {
        if (!find(shopLocations, { id })) {
            const response = await LocationService.getList(id);
            const items = [...shopLocations];
            items.push({ id, locations: response ?? [] });
            setShopLocations(items);
        }
        setItemId(id);
        setShowLocation(true);
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const actionMenu = () => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId('');
    };

    return (
        <>
            <Helmet>
                <title>{t(`shop.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`shop.multiple`)}
                contextMenu={
                    includes([UserRole.ADMIN, UserRole.PARTNER], user?.role_id)
                        ? [
                              {
                                  text: t('shop.add'),
                                  icon: 'PLUS',
                                  fnCallBack: { actionMenu },
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchShopForm
                        isLoading={isLoading || isRefetching}
                        countries={countries ?? []}
                        partners={partners?.items ?? []}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListShop
                                    items={data.items}
                                    countries={countries ?? []}
                                    partners={partners?.items ?? []}
                                    paging={data.pagination}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                    showLocations={showLocations}
                                    currentUser={user}
                                />
                                <PaginationTable
                                    countItem={data.pagination.count_item}
                                    totalPage={data.pagination.total_page}
                                    currentPage={data.pagination.current_page}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalShopUpdate
                                show={showUpdate}
                                shop={find(data.items, { id: itemId })}
                                partners={REACT_APP_IS_PARTNER ? [user!] : partners?.items ?? []}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                                currentUser={user}
                            />
                            <ModalShopLocations
                                show={showLocation}
                                shopName={find(data.items, { id: itemId })?.name ?? ''}
                                locations={find(shopLocations, { id: itemId })?.locations ?? []}
                                changeShow={(s: boolean) => setShowLocation(s)}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
