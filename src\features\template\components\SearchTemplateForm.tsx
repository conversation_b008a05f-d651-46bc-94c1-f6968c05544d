import { useTranslation } from 'react-i18next';
import SearchForm from 'components/partials/SearchForm';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';
import { ItemStatusNames } from 'types/common/Item';
import User, { displayUserOnSelect } from '../../../types/User';
import { SearchField } from '../../../types/common/Search';
import { REACT_APP_IS_ADMIN } from '../../../constants/common';
import { filter } from 'lodash';

interface IProps {
    isLoading: boolean;
    partners: User[];
}

export default function SearchTemplateForm({ isLoading, partners }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        {
            name: 'search_text',
            type: 'text',
            label: t('keywords'),
            wrapClassName: 'col-md-4 col-12',
            show: true,
        },
        {
            name: 'status_id',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
            },
            show: true,
        },
        {
            name: 'created_by__i',
            type: 'select',
            label: t('partner.single'),
            wrapClassName: 'col-md-8 col-12',
            options: {
                multiple: true,
                choices: partners.map((item) => ({
                    value: item.id!,
                    label: displayUserOnSelect(item),
                })),
            },
            show: REACT_APP_IS_ADMIN,
        },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
