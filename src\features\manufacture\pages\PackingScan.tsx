import classNames from 'classnames';
import ContentHeader from 'components/partials/ContentHeader';
import InputQuantity from 'components/partials/InputQuantity';
import ProductColumnInfo from 'components/partials/ProductColumnInfo';
import UpdateButton from 'components/partials/UpdateButton';
import ScanZone from 'features/transfer/components/ScanZone';
import { filter, find, findIndex, includes, isEmpty, map } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import OrderProductService from 'services/OrderProductService';
import OrderService from 'services/OrderService';
import ProductService from 'services/ProductService';
import { OrderProduct, OrderProductBarcode, OrderStatus } from 'types/Order';
import { OrderPacking } from 'types/OrderProduct';
import { TransferGoodBarcodeProduct } from 'types/TransferGood';
import { downloadPdf, showToast } from 'utils/common';
import { Printer } from 'react-feather';

export default function PackingScan() {
    const { t } = useTranslation();
    const [products, setProducts] = useState<TransferGoodBarcodeProduct[]>([]);
    //const [packProducts, setPackProducts] = useState<TransferGoodBarcodeProduct[]>([]);
    const [orders, setOrders] = useState<OrderProductBarcode[]>([]);
    const [orderIds, setOrderIds] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    //const [show, setShow] = useState(false);

    const getProducts = async (
        ps: OrderProduct[],
        orderId: string,
        code: string,
        items: TransferGoodBarcodeProduct[]
    ) => {
        const response = await ProductService.getListChildren(
            ps.map((item) => item.product_child_id),
            orderId
        );
        if (response.success) {
            response.data.items.forEach((p) => {
                const item = find(ps, { product_child_id: p.product_child_id });
                if (item) {
                    p.order_id = orderId;
                    p.transfer_good_product_id = item.id!;
                    p.quantity = item.quantity;
                    p.scan_quantity = p.barcode.code === code ? 1 : 0;
                    items.push(p);
                }
            });
            setProducts(items);
        } else showToast(false, response.messages);
    };

    const onScan = async (code: string, cloneProducts?: TransferGoodBarcodeProduct[]) => {
        const items = [...(cloneProducts ?? [])];
        const index = findIndex(items, (item) => item.barcode.code === code);
        if (index > -1) {
            items[index].scan_quantity++;
            setProducts(items);
        } else {
            const response = await OrderService.getByProductBarcode(code);
            if (response.success && !isEmpty(response.data.products)) {
                if (response.data.status_id === OrderStatus.WAIT_PACK) {
                    getProducts(response.data.products, response.data.id!, code, items);
                    const cloneOrders = [...orders];
                    cloneOrders.push({ ...response.data, finishPacking: false });
                    setOrders(cloneOrders);
                } else showToast(false, [t('order.notProduced')]);
            } else showToast(false, [t('error.barcode')]);
        }
    };

    const onChangeQuantity = (index: number, quantity: number) => {
        const items = [...products];
        items[index].scan_quantity = quantity;
        setProducts(items);
    };

    const checkPacking = () => {
        const fullProducts = filter(products, (item) => item.quantity === item.scan_quantity);
        const missProducts = filter(products, (item) => item.quantity !== item.scan_quantity);
        const pProducts = filter(fullProducts, (item) => !includes(map(missProducts, 'order_id'), item.order_id));
        if (isEmpty(pProducts)) {
            showToast(false, [t('error.quantity')]);
        } else {
            //setPackProducts(pProducts);
            //setShow(true);
            finishPacking(pProducts);
        }
    };

    const finishPacking = async (packProducts: TransferGoodBarcodeProduct[]) => {
        const packingOrders: OrderPacking[] = [];
        packProducts.forEach((p) => {
            const index = findIndex(packingOrders, { orderId: p.order_id });
            if (index > -1) {
                packingOrders[index].products.push({
                    order_product_id: p.transfer_good_product_id,
                    packed_quantity: p.scan_quantity,
                });
            } else {
                packingOrders.push({
                    orderId: p.order_id!,
                    products: [{ order_product_id: p.transfer_good_product_id, packed_quantity: p.scan_quantity }],
                });
            }
        });
        let countSuccess = 0,
            successMessages: string[] = [],
            errorMessages: string[] = [];
        const errorOrderIds: string[] = [];
        const cloneOrders = [...orders];
        setIsLoading(true);
        for (const item of packingOrders) {
            const response = await OrderProductService.finishPacking(item.orderId, item.products);
            if (response.success) {
                countSuccess++;
                successMessages = response.messages;
                const index = findIndex(cloneOrders, { id: item.orderId });
                if (index > -1) {
                    cloneOrders[index].finishPacking = true;
                }
            } else {
                errorOrderIds.push(item.orderId);
                errorMessages = response.messages;
            }
        }
        setIsLoading(false);
        setOrders(cloneOrders);
        //setShow(false);
        const ps = filter([...products], (item) => !includes(map(packProducts, 'order_id'), item.order_id));
        if (countSuccess === packingOrders.length) {
            //setPackProducts([]);
            setProducts(ps);
            showToast(true, successMessages);
        } else {
            const items = filter([...products], (item) => includes(errorOrderIds, item.order_id));
            setProducts([...ps, ...items]);
            showToast(false, errorMessages);
        }
    };

    const printOrder = async (orderId: string, orderNumber: string) => {
        const carrier = 'Sendle',
            serviceCode = 'STANDARD-PICKUP';
        const result = await OrderService.printLabel(orderId, carrier, serviceCode);
        if (result.success && !isEmpty(result.data.labels)) {
            try {
                downloadPdf(result.data.labels[0], `${orderNumber}.pdf`);
                setOrderIds([...orderIds, orderId]);
            } catch {
                showToast(false, [t('error.common')]);
            }
        } else {
            showToast(false, [t('error.common')]);
        }
    };

    return (
        <>
            <Helmet>
                <title>{t('scan.packing')}</title>
            </Helmet>
            <ContentHeader title={t('scan.packing')} />
            <div className="content-body">
                <div className="col-12">
                    <ScanZone products={products} onScan={onScan} />
                    <div className="card">
                        <div className="table-responsive">
                            <table className="table">
                                <thead>
                                    <tr>
                                        <th className="text-center">{t('no.')}</th>
                                        <th className="text-center">{t('orderCode')}</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {orders
                                        .filter((item) => !includes(orderIds, item.id))
                                        .map((item, index) => (
                                            <tr key={item.id}>
                                                <td className="text-center">{index + 1}</td>
                                                <td className="text-center">{item.order_number}</td>
                                                <td className="text-center">
                                                    {item.finishPacking && (
                                                        <Printer
                                                            size={14}
                                                            className="cursor-pointer text-danger"
                                                            onClick={() => printOrder(item.id!, item.order_number)}
                                                        />
                                                    )}
                                                </td>
                                            </tr>
                                        ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div className="row">
                        <div className="col-12">
                            <div className="card">
                                <div className="table-responsive">
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th className="text-center">{t('quantity')}</th>
                                                <th className="text-center">{t('code')}</th>
                                                <th>{t('style')}</th>
                                                <th className="text-center">{t('barcode')}</th>
                                                <th className="text-center">{t('scan.quantity')}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {products.map((item, index) => (
                                                <tr
                                                    key={item.barcode.code}
                                                    className={classNames({
                                                        'table-danger': item.scan_quantity < item.quantity,
                                                        'table-success': item.scan_quantity === item.quantity,
                                                        'table-warning': item.scan_quantity > item.quantity,
                                                    })}
                                                >
                                                    <td className="text-center">{item.quantity}</td>
                                                    <ProductColumnInfo item={item} />
                                                    <td className="text-center">
                                                        <InputQuantity
                                                            quantity={item.scan_quantity}
                                                            adjustQuantity={(quantity: number) =>
                                                                onChangeQuantity(index, quantity)
                                                            }
                                                        />
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {!isEmpty(products) && (
                            <div className="col-12">
                                <UpdateButton
                                    btnText={t('manufacturer.finishPacking')}
                                    isLoading={isLoading}
                                    btnClass={['mt-1', 'ms-1']}
                                    onSubmit={checkPacking}
                                />
                                {/*<UpdateButton*/}
                                {/*    btnText={t('manufacturer.printLabel')}*/}
                                {/*    isLoading={isLoading}*/}
                                {/*    btnClass={['mt-1']}*/}
                                {/*    onSubmit={handlePrintHTML}*/}
                                {/*/>*/}
                            </div>
                        )}
                    </div>
                </div>
            </div>
            {/*<ModalConfirm*/}
            {/*    show={show}*/}
            {/*    text={t('confirm.finishPacking')}*/}
            {/*    btnDisabled={isLoading}*/}
            {/*    changeShow={(s: boolean) => setShow(s)}*/}
            {/*    submitAction={finishPacking}*/}
            {/*/>*/}
        </>
    );
}
