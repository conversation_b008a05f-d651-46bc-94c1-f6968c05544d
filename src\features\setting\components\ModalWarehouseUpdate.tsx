import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { optionModelDefault } from 'constants/common';
import { find } from 'lodash';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select, { SingleValue } from 'react-select';
import { ItemStatus, ItemStatusNames, SelectOptionModel } from 'types/common/Item';
import Country from 'types/Country';
import User from 'types/User';
import Warehouse from 'types/Warehouse';
import { getSelectStyle, selectItem, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';

interface IProps {
    show: boolean;
    warehouse: Warehouse | undefined;
    countries: Country[];
    manufacturers: User[];
    isLoading: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: Partial<Warehouse>) => void;
}

export default function ModalWarehouseUpdate({
    show,
    warehouse,
    countries,
    manufacturers,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [countryValue, setCountryValue] = useState<SelectOptionModel>(optionModelDefault);
    const [manufacturerValue, setManufacturerValue] = useState<SelectOptionModel>(optionModelDefault);
    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            manufacturer_id: yup.string().required(t('error.required')).trim(),
            country_id: yup.string().required(t('error.required')).trim(),
            address: yup.string().required(t('error.required')).trim(),
            lat: yup.string().required(t('error.required')).trim(),
            lng: yup.string().required(t('error.required')).trim(),
            quantity_minimum: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .min(0, t('error.min_0')),
        })
        .required();
    const {
        register,
        handleSubmit,
        reset,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm<Warehouse>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (warehouse && show) {
            const country = find(countries, { id: warehouse.country_id });
            if (country) {
                setCountryValue({ value: country.id!, label: country.name });
            }
            const manufacturer = find(manufacturers, { id: warehouse.manufacturer_id });
            if (manufacturer) {
                setManufacturerValue({ value: manufacturer.id!, label: manufacturer.first_name });
            }
            reset(warehouse);
        } else {
            reset({
                name: '',
                manufacturer_id: '',
                country_id: '',
                address: '',
                status_id: ItemStatus.ACTIVE,
                quantity_minimum: 0,
                lat: '',
                lng: '',
            });
        }
    }, [warehouse, countries, manufacturers, show]);

    const onChangeManufacturer = (option: SingleValue<SelectOptionModel>) => {
        setManufacturerValue(option ?? optionModelDefault);
        setValue('manufacturer_id', option?.value ?? '');
        if (option?.value) clearErrors('manufacturer_id');
    };

    const onChangeCountry = (option: SingleValue<SelectOptionModel>) => {
        setCountryValue(option ?? optionModelDefault);
        setValue('country_id', option?.value ?? '');
        if (option?.value) clearErrors('country_id');
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t(warehouse ? 'warehouse.edit' : 'warehouse.add')}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(submitAction)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('name')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('name')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.name?.message),
                                            })}
                                        />
                                        <span className="error">{errors.name?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('manufacturer.single')} <span className="error">*</span>
                                        </label>
                                        <Select
                                            options={manufacturers.map((item) => ({
                                                value: item.id!,
                                                label: item.first_name,
                                            }))}
                                            onChange={onChangeManufacturer}
                                            value={manufacturerValue}
                                            styles={{
                                                control: (baseStyles, state) =>
                                                    getSelectStyle(
                                                        baseStyles,
                                                        state,
                                                        !!errors.manufacturer_id?.message
                                                    ),
                                            }}
                                        />
                                        <span className="error">{errors.manufacturer_id?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('country')} <span className="error">*</span>
                                        </label>
                                        <Select
                                            options={countries.map((item) => ({
                                                value: item.id!,
                                                label: item.name,
                                            }))}
                                            onChange={onChangeCountry}
                                            value={countryValue}
                                            styles={{
                                                control: (baseStyles, state) =>
                                                    getSelectStyle(baseStyles, state, !!errors.country_id?.message),
                                            }}
                                        />
                                        <span className="error">{errors.country_id?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">{t('statusName')}</label>
                                        <select {...register('status_id')} className="form-select">
                                            {selectItem(ItemStatusNames, t, true)}
                                        </select>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('quantityWarning')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('quantity_minimum')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.quantity_minimum?.message),
                                            })}
                                        />
                                        <span className="error">{errors.quantity_minimum?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('address')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('address')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.address?.message),
                                            })}
                                        />
                                        <span className="error">{errors.address?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('lat')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('lat')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.lat?.message),
                                            })}
                                        />
                                        <span className="error">{errors.lat?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('lng')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('lng')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.lng?.message),
                                            })}
                                        />
                                        <span className="error">{errors.lng?.message}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={t(warehouse ? 'update' : 'add')}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
