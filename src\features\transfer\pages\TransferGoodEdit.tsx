import { useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Navigate, useParams } from 'react-router-dom';
import TransferGoodService from 'services/TransferGoodService';
import { TransferTypeNames } from 'types/TransferGood';
import { getFieldInArrayObject } from 'utils/common';
import UpdateTransferGoodForm from '../components/UpdateTransferGoodForm';

export default function TransferGoodEdit() {
    const { id } = useParams();
    const { t } = useTranslation();
    const [type, setType] = useState('receive');

    const getItem = async (tId: string) => {
        const response = await TransferGoodService.getByBarcode(tId);
        if (response.success) {
            setType(getFieldInArrayObject(TransferTypeNames, response.data.type_id, 'name', 'receive'));
            return response.data;
        }
        return null;
    };

    const {
        data: transferGood,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: [QUERY_KEY.TRANSFER_GOOD, id],
        queryFn: ({ queryKey }) => getItem(queryKey[1] ?? ''),
        enabled: !!id,
    });

    if (!isLoading && !transferGood) return <Navigate to="/not-found" />;

    return (
        <>
            <Helmet>
                <title>{t(`${type}.edit`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.edit`)}
                breadcrumbs={[
                    {
                        text: t(`${type}.multiple`),
                        to: `/transfer/list/${type}`,
                    },
                    {
                        text: transferGood ? transferGood.barcode.code : t(`${type}.edit`),
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {isLoading && <Spinner />}
                    {!isLoading && transferGood && (
                        <UpdateTransferGoodForm transferGood={transferGood} refetch={refetch} />
                    )}
                </div>
            </div>
        </>
    );
}
