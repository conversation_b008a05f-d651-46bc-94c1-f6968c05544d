import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isEmpty, isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import TransferGoodService from 'services/TransferGoodService';
import UserService from 'services/UserService';
import WarehouseService from 'services/WarehouseService';
import { useAuthStore } from 'stores/authStore';
import { ItemStatus } from 'types/common/Item';
import { SearchTransferGood, SearchTransferGoodParam, TransferType, TransferTypeNames } from 'types/TransferGood';
import { SearchUser, UserRole } from 'types/User';
import Warehouse, { SearchWarehouse } from 'types/Warehouse';
import { getFieldInArrayObject, showToast } from 'utils/common';
import ListTransferGood from '../components/ListTransferGood';
import SearchTransferGoodForm from '../components/SearchTransferGoodForm';

export default function TransferGoodList() {
    const { type } = useParams();
    const { queryParams, setQueryParams } = useQueryParams<SearchTransferGoodParam>();
    const paramConfig: SearchTransferGoodParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            warehouse_id__i: queryParams.warehouse_id__i,
            supplier_id__i: queryParams.supplier_id__i,
            status_id__i: queryParams.status_id__i,
            is_auto: queryParams.is_auto,
        },
        isUndefined
    );

    const [showDelete, setShowDelete] = useState(false);
    const [itemId, setItemId] = useState('');
    const navigate = useNavigate();
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);

    const typeId = +getFieldInArrayObject(TransferTypeNames, type ?? '', 'id', '0', 'name');
    if (!typeId) {
        navigate('/not-found');
    }
    paramConfig.type_id = typeId.toString();

    const { data: warehouses } = useQuery({
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
    });

    const { data: suppliers } = useQuery({
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.SUPPLIER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
        enabled: typeId === TransferType.RECEIVED,
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        queryKey: [QUERY_KEY.TRANSFER_GOODS, paramConfig, warehouses, user],
        queryFn: ({ queryKey }) =>
            TransferGoodService.getList(
                queryKey[1] as SearchTransferGood,
                (queryKey[2] ?? []) as Warehouse[],
                user!.id
            ),
        enabled: !!typeId && !!user && !isEmpty(warehouses),
        placeholderData: keepPreviousData,
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => TransferGoodService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    return (
        <>
            <Helmet>
                <title>{t(`${type}.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.multiple`)}
                contextMenu={
                    typeId === TransferType.RECEIVED
                        ? [
                              {
                                  text: t(`${type}.add`),
                                  to: `/transfer/add/${type}`,
                                  icon: 'PLUS',
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchTransferGoodForm
                        isLoading={isLoading || isRefetching}
                        typeId={typeId}
                        warehouses={warehouses ?? []}
                        suppliers={suppliers?.items ?? []}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListTransferGood
                                items={data.items}
                                type={type}
                                warehouses={warehouses ?? []}
                                suppliers={suppliers?.items ?? []}
                                paging={data.pagination}
                                handleDelete={handleDelete}
                            />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
