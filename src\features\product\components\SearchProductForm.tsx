import SearchForm from 'components/partials/SearchForm';
import { REACT_APP_IS_ADMIN } from 'constants/common';
import { filter, find } from 'lodash';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { ItemParamModel, YesNoChoices } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { ProductStatusNames, ProductTypeNames } from 'types/Product';
import Shop from 'types/Shop';
import User, { displayUserName } from 'types/User';
import Warehouse from 'types/Warehouse';
import { convertConstantToSelectOptions, convertObjectToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
    stockWarning: boolean;
    warehouses: Warehouse[];
    suppliers: User[];
    hiddenWarehouse?: boolean;
    isShopProductRoute?: boolean;
    partners?: User[];
    shops?: Shop[];
    isLoadingShops?: boolean;
}

export default function SearchProductForm({
    isLoading,
    stockWarning,
    warehouses,
    suppliers,
    hiddenWarehouse = false,
    isShopProductRoute = false,
    partners = [],
    shops = [],
    isLoadingShops = false,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        {
            name: 'partner_id',
            type: 'select',
            label: t('partner.single'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertObjectToSelectOptions(
                    partners.map((item) => ({ id: item.id!, name: displayUserName(item) })),
                    true
                ),
            },
            autoSubmit: true,
            show: isShopProductRoute,
        },
        {
            name: 'shop_id',
            type: 'select',
            label: t('shop.single'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertObjectToSelectOptions(
                    shops.map((item) => ({ id: item.id!, name: item.name })),
                    true
                ),
            },
            show: isShopProductRoute,
        },
        { name: 'search_text', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12', show: true },
        {
            name: 'children.preset',
            type: 'text',
            label: t('design.preset'),
            wrapClassName: 'col-md-4 col-12',
            show: true,
        },
        //{ name: 'id__i', type: 'text', label: 'id', wrapClassName: 'col-md-4 col-12', show: !stockWarning },
        {
            name: 'product_type__i',
            type: 'select',
            label: t('type'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(ProductTypeNames, true),
            },
            show: !stockWarning,
        },
        {
            name: 'vendor',
            type: 'select',
            label: t('vendor'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertObjectToSelectOptions(
                    suppliers.map((item) => ({ id: item.last_name ?? item.first_name, name: item.first_name }))
                ),
            },
            show: REACT_APP_IS_ADMIN,
        },
        {
            name: 'status__i',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(ProductStatusNames, true, t),
            },
            show: true,
        },
        {
            name: 'is_supplier_product',
            type: 'select',
            label: t('product.isSupplier'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
            },
            show: true,
        },
        {
            name: 'warehouse_id',
            type: 'select',
            label: t('warehouse.single'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertObjectToSelectOptions(warehouses as ItemParamModel[], true),
            },
            show: REACT_APP_IS_ADMIN && !hiddenWarehouse,
        },
        {
            name: 'include_children',
            type: 'select',
            label: t('product.showChildProduct'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
            },
            show: true,
        },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
