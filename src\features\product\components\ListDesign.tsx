import { AreaSelector, IArea } from '@bmunozg/react-image-area';
import { concat, findIndex, includes, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { Eye, Save } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Fragment } from 'react/jsx-runtime';
import ProductService from 'services/ProductService';
import { Paging } from 'types/common';
import { DesignPosition, PlacementNames, ProductDesign } from 'types/Product';
import { genTableIndex, getFieldHtml, showToast } from 'utils/common';

interface IProps {
    items: ProductDesign[];
    paging: Paging;
    refetch: () => void;
}

type IDesign = ProductDesign & { areas: IArea[]; ids: string[] };
interface AreaDesign {
    area: IArea;
    editArea: IArea;
    ids: string[];
}

export default function ListDesign({ items, paging, refetch }: Readonly<IProps>) {
    const { t } = useTranslation();

    const [designs, setDesigns] = useState<IDesign[]>([]);
    const [indexDesigns, setIndexDesigns] = useState<number[]>([]);
    const [positions, setPositions] = useState<AreaDesign[]>([]);
    const [afterAreas, setAfterAreas] = useState<IArea[]>([]);
    const [typeName, setTypeName] = useState<string>('');

    useEffect(() => {
        const getIndex = (tmpPositions: AreaDesign[], area: IArea) => {
            let retVal = -1;
            tmpPositions.forEach((pos, index) => {
                if (
                    +pos.area.x === +area.x &&
                    +pos.area.y === +area.y &&
                    +pos.area.width === +area.width &&
                    +pos.area.height === +area.height
                ) {
                    retVal = index;
                    return false;
                }
            });
            return retVal;
        };

        const tmp: IDesign[] = [];
        const tmpPositions: AreaDesign[] = [];
        if (!isEmpty(items)) {
            items.forEach((item) => {
                const area: IArea = {
                    unit: 'px',
                    x: +(item.position.x ?? 0),
                    y: +(item.position.y ?? 0),
                    width: +(item.position.w ?? 100),
                    height: +(item.position.h ?? 100),
                };
                const index = findIndex(tmp, {
                    product_id: item.product_id,
                    placement_id: item.placement_id,
                    color: item.color,
                });
                if (index > -1) {
                    tmp[index].areas.push(area);
                    tmp[index].ids.push(item.id!);
                } else {
                    tmp.push({
                        ...item,
                        areas: [area],
                        ids: [item.id!],
                    });
                }
                const designIndex = getIndex(tmpPositions, area);
                if (designIndex > -1) {
                    tmpPositions[designIndex].ids.push(item.id!);
                } else {
                    tmpPositions.push({
                        area,
                        editArea: { ...area },
                        ids: [item.id!],
                    });
                }
            });
        }
        setDesigns(tmp);
        setPositions(tmpPositions);
    }, [items]);

    const toggleDesign = (value: number, checked: boolean) => {
        const tmp = [...indexDesigns];
        if (checked) {
            tmp.push(value);
            setIndexDesigns(tmp);
        } else {
            setIndexDesigns(tmp.filter((i) => i !== value));
        }
    };

    const onChangeArea = (index: number, areas: IArea[]) => {
        const tmp = [...designs];
        tmp[index].areas = areas;
        setDesigns(tmp);
        setAfterAreas(areas);
    };

    const changeArea = (value: number, index: number, attr: string) => {
        const tmp = [...positions];
        //@ts-ignore
        tmp[index].editArea[attr] = value / 10;
        setPositions(tmp);
    };

    const viewDesign = (index: number) => {
        const tmp = [...designs];
        tmp.forEach((design, idx1) => {
            design.areas.forEach((area, idx2) => {
                if (
                    area.x === positions[index].area.x &&
                    area.y === positions[index].area.y &&
                    area.width === positions[index].area.width &&
                    area.height === positions[index].area.height
                ) {
                    tmp[idx1].areas[idx2] = positions[index].editArea;
                }
            });
        });
        setDesigns(tmp);
    };

    const updateDesign = async (index: number) => {
        const position: DesignPosition = {
            x: positions[index].editArea.x,
            y: positions[index].editArea.y,
            w: positions[index].editArea.width,
            h: positions[index].editArea.height,
        };
        const response = await ProductService.updateDesignSizes({
            position,
            ids: positions[index].ids,
        });
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    const updateDesignOk = async () => {
        if (isEmpty(indexDesigns)) {
            showToast(false, [t('error.chooseProduct')]);
        }
        let ids: string[] = [];
        indexDesigns.forEach((index) => {
            ids = concat(ids, designs[index].ids);
        });
        const response = await ProductService.changeDesignOk(true, ids);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    const deleteDesigns = async () => {
        if (isEmpty(indexDesigns)) {
            showToast(false, [t('error.chooseProduct')]);
        }
        let ids: string[] = [];
        indexDesigns.forEach((index) => {
            ids = concat(ids, designs[index].ids);
        });
        const response = await ProductService.deleteDesigns(ids);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    const chooseAllDesigns = () => {
        const ids = designs.map((_, index) => index);
        setIndexDesigns(ids);
    };

    const updateAllDesignOk = async () => {
        const ids = items.map((item) => item.id!);
        const response = await ProductService.changeDesignOk(true, ids);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    const deleteAllDesigns = async () => {
        const ids = items.map((item) => item.id!);
        const response = await ProductService.deleteDesigns(ids);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    const saveProductWithNewType = async () => {
        if (!typeName) {
            showToast(false, [t('error.required')]);
        }
        if (isEmpty(indexDesigns)) {
            showToast(false, [t('error.chooseProduct')]);
        }
        let ids: string[] = [];
        indexDesigns.forEach((index) => {
            ids = concat(ids, designs[index].ids);
        });
        const response = await ProductService.saveProductToNewType(typeName, ids);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    return (
        <>
            <div className="table-responsive">
                <table className="table">
                    <thead>
                        <tr>
                            <th className="text-center">{t('no.')}</th>
                            <th className="text-center">{t('size')}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {positions.map((item, index) => (
                            <tr key={index}>
                                <td className="text-center">{index + 1}</td>
                                <td className="text-center">{JSON.stringify(item.area)}</td>
                                <td>
                                    x:{' '}
                                    <input
                                        type="text"
                                        value={item.editArea.x * 10}
                                        onChange={(e) => changeArea(+e.target.value, index, 'x')}
                                        className="thAction2 me-50"
                                    />
                                    y:{' '}
                                    <input
                                        type="text"
                                        value={item.editArea.y * 10}
                                        onChange={(e) => changeArea(+e.target.value, index, 'y')}
                                        className="thAction2 me-50"
                                    />
                                    w:{' '}
                                    <input
                                        type="text"
                                        value={item.editArea.width * 10}
                                        onChange={(e) => changeArea(+e.target.value, index, 'width')}
                                        className="thAction2 me-50"
                                    />
                                    h:{' '}
                                    <input
                                        type="text"
                                        value={item.editArea.height * 10}
                                        onChange={(e) => changeArea(+e.target.value, index, 'height')}
                                        className="thAction2 me-50"
                                    />
                                    <button
                                        type="button"
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect me-50"
                                        onClick={() => viewDesign(index)}
                                    >
                                        <Eye size={14} />
                                    </button>
                                    <button
                                        type="button"
                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                        onClick={() => updateDesign(index)}
                                    >
                                        <Save size={14} />
                                    </button>
                                </td>
                            </tr>
                        ))}
                        {afterAreas.map((item, index) => (
                            <tr key={index} className="table-danger">
                                <td className="text-center">{index + 1}</td>
                                <td colSpan={2} className="text-center">
                                    {JSON.stringify(item)}
                                </td>
                            </tr>
                        ))}
                        <tr>
                            <td className="text-center">Choose {indexDesigns.length} items</td>
                            <td className="text-center">
                                <button
                                    className="btn btn-danger waves-effect waves-float waves-light btn-sm me-1"
                                    type="button"
                                    onClick={chooseAllDesigns}
                                >
                                    Select All
                                </button>
                                <button
                                    className="btn btn-primary waves-effect waves-float waves-light btn-sm me-1"
                                    type="button"
                                    disabled={isEmpty(indexDesigns)}
                                    onClick={updateDesignOk}
                                >
                                    {t('design.done')}
                                </button>
                                <button
                                    className="btn btn-primary waves-effect waves-float waves-light btn-sm me-1"
                                    type="button"
                                    onClick={updateAllDesignOk}
                                >
                                    {t('design.doneAll')}
                                </button>
                                <button
                                    className="btn btn-danger waves-effect waves-float waves-light btn-sm me-1"
                                    type="button"
                                    disabled={isEmpty(indexDesigns)}
                                    onClick={deleteDesigns}
                                >
                                    Delete
                                </button>
                                <button
                                    className="btn btn-danger waves-effect waves-float waves-light btn-sm"
                                    type="button"
                                    onClick={deleteAllDesigns}
                                >
                                    Delete All
                                </button>
                            </td>
                            <td>
                                <div className="input-group">
                                    <input
                                        type="text"
                                        className="form-control me-1"
                                        placeholder="Code"
                                        value={typeName}
                                        onChange={(e) => setTypeName(e.target.value)}
                                    />
                                    <button
                                        className="btn btn-outline-primary waves-effect"
                                        type="button"
                                        onClick={saveProductWithNewType}
                                    >
                                        {t('update')}
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div className="table-responsive">
                <table className="table">
                    <thead>
                        <tr>
                            {/* <th className="text-center">{t('no.')}</th> */}
                            <th className="text-center">{t('design.file')}</th>
                            {/* <th className="text-center">{t('color')}</th>
                            <th className="text-center">{t('design.placement')}</th>
                            <th className="text-center">{t('size')}</th>
                            <th className="text-center">{t('code')}</th> */}
                        </tr>
                    </thead>
                    <tbody>
                        {designs.map((item: IDesign, index: number) => (
                            <Fragment key={item.id}>
                                <tr className="table-success">
                                    {/* <td className="text-center">
                                        {genTableIndex(index, paging.limit, paging.current_page)}
                                    </td> */}
                                    <td className="text-center">
                                        <input
                                            className="form-check-input"
                                            type="checkbox"
                                            value={index}
                                            id={`cb_${index}`}
                                            checked={includes(indexDesigns, index)}
                                            onChange={(e) => toggleDesign(+e.target.value, e.target.checked)}
                                        />
                                        <label htmlFor={`cb_${index}`}>
                                            {genTableIndex(index, paging.limit, paging.current_page)}. {item.product_id}{' '}
                                            - {getFieldHtml(PlacementNames, item.placement_id, t)} - {item.color} -{' '}
                                            {item.size} - {item.code} - {item.areas.length} placements
                                        </label>
                                    </td>
                                </tr>
                                <tr>
                                    {/* <td className="text-center">
                                        <button
                                            type="button"
                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                            onClick={() => updateDesign(index)}
                                        >
                                            <Save size={14} />
                                        </button>
                                    </td> */}
                                    <td className="text-center">
                                        {item.file?.file_url && (
                                            <AreaSelector
                                                maxAreas={2}
                                                wrapperStyle={{
                                                    border: '2px solid black',
                                                }}
                                                areas={item.areas}
                                                onChange={(areas: IArea[]) => onChangeArea(index, areas)}
                                            >
                                                <img src={item.file?.file_url} />
                                            </AreaSelector>
                                        )}
                                    </td>
                                    {/* <td className="text-center">{item.color}</td>
                                <td className="text-center">{getFieldHtml(PlacementNames, item.placement_id, t)}</td>
                                <td className="text-center">{item.size}</td>
                                <td className="text-center">{item.code}</td> */}
                                </tr>
                            </Fragment>
                        ))}
                    </tbody>
                </table>
            </div>
        </>
    );
}
