import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ItemStatus, ItemStatusNames } from 'types/common/Item';
import DesignTemplate from 'types/DesignTemplate';
import { selectItem, toggleModalOpen, showToast } from 'utils/common';
import * as yup from 'yup';
import { Upload } from 'react-feather';
import FileService from 'services/FileService';
import {
    handleFileUpload,
    cleanupPreviewUrls,
    PreviewFile as UtilPreviewFile,
    DEFAULT_FILE_CONFIG,
} from 'utils/fileUpload';
import './TemplateModal.css';
import DesignTemplateService from 'services/DesignTemplateService';

interface DesignTemplateModalProps {
    show: boolean;
    itemId?: string;
    isLoading: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: DesignTemplate) => void;
}

type PreviewFile = UtilPreviewFile;

export default function TemplateFormModal({
    show,
    itemId,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<DesignTemplateModalProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const [previewFiles, setPreviewFiles] = useState<PreviewFile[]>([]);
    const [isUploading, setIsUploading] = useState(false);

    const schema = yup
        .object({
            name: yup.string().required(t('error.required')).trim(),
            ...(itemId && {
                status_id: yup.number().required(t('error.required')).typeError(t('error.number')),
            }),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<DesignTemplate>({
        resolver: yupResolver(schema),
    });

    const isOldFile = (file: PreviewFile): boolean => file.file.size === 0;

    useEffect(() => {
        if (itemId && show) {
            DesignTemplateService.getById(itemId).then((data) => {
                if (data) {
                    reset(data);
                    if (Array.isArray(data.file_urls) && Array.isArray(data.file_ids)) {
                        const oldFiles: PreviewFile[] = data.file_urls.map((url, idx) => ({
                            id: data.file_ids[idx] || `old_${idx}`,
                            file: new File([], url.split('/').pop() ?? 'image'),
                            preview: url,
                            uploadedId: data.file_ids[idx],
                        }));
                        setPreviewFiles(oldFiles);
                    }
                }
            });
        } else {
            reset({
                name: '',
                status_id: ItemStatus.ACTIVE,
                file_ids: [],
            });
            const newFiles = previewFiles.filter((file) => !isOldFile(file));
            cleanupPreviewUrls(newFiles);
            setPreviewFiles([]);
        }
    }, [itemId, show, reset]);

    const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        const result = await handleFileUpload(files, DEFAULT_FILE_CONFIG, t, previewFiles, false);

        if (result.success && result.files.length > 0) {
            setPreviewFiles((prev) => [...prev, ...result.files]);
        }

        if (event.target) {
            event.target.value = '';
        }
    };

    const handleDeleteFile = (id: string) => {
        const fileToDelete = previewFiles.find((file) => file.id === id);
        if (fileToDelete) {
            if (!isOldFile(fileToDelete)) {
                cleanupPreviewUrls([fileToDelete]);
            }
            setPreviewFiles((prev) => prev.filter((file) => file.id !== id));
        }
    };

    const handleCloseModal = () => {
        const newFiles = previewFiles.filter((file) => !isOldFile(file));
        cleanupPreviewUrls(newFiles);
        changeShow(false);
    };

    const uploadFiles = async (files: PreviewFile[]): Promise<string[]> => {
        const uploadPromises = files.map(async (file) => {
            if (isOldFile(file) && file.uploadedId) {
                return file.uploadedId;
            }
            if (!isOldFile(file) && file.file) {
                const result = await FileService.upload(file.file);
                return result.data.id;
            }
            return file.uploadedId ?? '';
        });

        const results = await Promise.all(uploadPromises);
        return results.filter((id) => id !== '');
    };

    const onSubmit = async (data: DesignTemplate) => {
        try {
            setIsUploading(true);
            const fileIds = await uploadFiles(previewFiles);
            submitAction({
                ...data,
                file_ids: fileIds,
            });
            const newFiles = previewFiles.filter((file) => !isOldFile(file));
            cleanupPreviewUrls(newFiles);
        } catch (error) {
            showToast(false, [t('error.upload')]);
        } finally {
            setIsUploading(false);
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t(itemId ? 'template.edit' : 'template.add')}</h5>
                            <button type="button" className="btn-close" onClick={handleCloseModal} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    {itemId ? (
                                        <div className="col-12 mb-1">
                                            <div className="row">
                                                <div className="col-8">
                                                    <label className="form-label">
                                                        {t('name')} <span className="error">*</span>
                                                    </label>
                                                    <input
                                                        {...register('name')}
                                                        type="text"
                                                        className={classNames('form-control', {
                                                            'is-invalid': Boolean(errors.name?.message),
                                                        })}
                                                    />
                                                    <span className="error">{errors.name?.message}</span>
                                                </div>
                                                <div className="col-4">
                                                    <label className="form-label">{t('statusName')}</label>
                                                    <select {...register('status_id')} className="form-select">
                                                        {selectItem(ItemStatusNames, t, true)}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="col-12 mb-1">
                                            <label className="form-label">
                                                {t('name')} <span className="error">*</span>
                                            </label>
                                            <input
                                                {...register('name')}
                                                type="text"
                                                className={classNames('form-control', {
                                                    'is-invalid': Boolean(errors.name?.message),
                                                })}
                                            />
                                            <span className="error">{errors.name?.message}</span>
                                        </div>
                                    )}
                                    <div className="col-12 mb-1">
                                        <label className="form-label me-2">{t('photo')}</label>
                                        {previewFiles.length === 0 && (
                                            <div className="upload-hint-container">
                                                <small>{t('uploadHint')}</small>
                                            </div>
                                        )}
                                        <div className="file-upload-container">
                                            <label
                                                htmlFor="upload-photo"
                                                className="btn btn-outline-primary btn-sm waves-effect"
                                            >
                                                <Upload size={14} className="me-2" />
                                                <span>{t('uploadPhoto')}</span>
                                                <input
                                                    type="file"
                                                    id="upload-photo"
                                                    hidden
                                                    accept="image/*"
                                                    multiple
                                                    onChange={handleFileChange}
                                                />
                                            </label>
                                            {previewFiles.length > 0 && (
                                                <div className="selected-files-info">
                                                    <small className="text-muted">
                                                        {t('selectedFiles', { count: previewFiles.length })}
                                                    </small>
                                                </div>
                                            )}
                                            {previewFiles.map((file) => (
                                                <div key={file.id} className="file-preview-container">
                                                    <img
                                                        src={file.preview}
                                                        alt={file.file?.name || 'image'}
                                                        loading="lazy"
                                                        className="file-preview-image"
                                                    />
                                                    <button
                                                        type="button"
                                                        className="file-delete-button"
                                                        onClick={() => handleDeleteFile(file.id)}
                                                        title={t('deleteFile')}
                                                    >
                                                        ×
                                                    </button>
                                                    <div className="file-name-overlay">
                                                        {file.file?.name && file.file.name.length > 15
                                                            ? `${file.file.name.substring(0, 12)}...`
                                                            : file.file?.name || 'image'}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={t(itemId ? 'update' : 'add')}
                                    isLoading={isLoading || isUploading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
