export interface ItemParam {
    id: number;
    name: string;
    className?: string;
    ids?: number[];
}

export interface ItemParamModel {
    id: string;
    name: string;
    className?: string;
}

export interface ItemCount extends ItemParam {
    count: number;
}

export interface ItemLink {
    id?: number;
    text: string;
    to?: string;
    icon?: 'PLUS' | 'DOWNLOAD' | 'SEND';
    fnCallBack?: {
        actionMenu: (id: number) => void;
    };
}

export interface ItemId {
    id: number | string;
}

export interface ItemIds {
    ids: number[] | string[];
}

export interface ItemFile {
    id: string;
    name: string;
    url: string;
    mineType?: string;
    fileSize?: number;
}

export interface SelectOption {
    value: number;
    label: string;
}

export interface SelectOptionModel {
    value: string;
    label: string;
}

export interface Barcode {
    code: string;
    image: string;
}

export interface DesignItem {
    placement: 'front' | 'back' | 'long_sleeve' | 'inner_neck' | 'left_sleeve' | 'right_sleeve';
    type: 'container' | 'design' | 'layer';
    url: string;
    properties?: {
        width?: number;
        height?: number;
        id?: string;
    };
    designId: string;
    layerId?: string;
}

export interface ProductBarcode {
    product_child_id: string;
    code: string;
    style: {
        image?: string;
        title: string;
        code: string;
        size: string;
        color: string;
    };
    barcode: Barcode;
    design?: DesignItem[];
}

export enum TrueFalse {
    FALSE = 0,
    TRUE = 1,
}

export enum Language {
    EN = 'en',
}

export enum ItemStatus {
    PENDING = 1,
    ACTIVE = 2,
    INACTIVE = 3,
}

export enum ItemParamType {
    GROUP = 'group',
    OTP = 'otp',
}

export const ItemStatusNames: ItemParam[] = [
    { id: ItemStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-warning' },
    { id: ItemStatus.ACTIVE, name: 'active', className: 'badge badge-glow bg-success' },
    { id: ItemStatus.INACTIVE, name: 'inactive', className: 'badge badge-glow bg-danger' },
];

export const YesNoChoices: ItemParam[] = [
    { id: TrueFalse.FALSE, name: 'no', className: 'badge badge-glow bg-danger' },
    { id: TrueFalse.TRUE, name: 'yes', className: 'badge badge-glow bg-success' },
];
