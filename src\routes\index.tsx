import { NotFound, PrivateRoute } from 'components/commons';
import React from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

const AuthPage = React.lazy(() => import('features/auth/pages/AuthPage'));
const LoginPage = React.lazy(() => import('features/auth/pages/Login'));
const ForgotPage = React.lazy(() => import('features/auth/pages/ForgotPass'));
const ChangePassPage = React.lazy(() => import('features/auth/pages/ChangePass'));

const Dashboard = React.lazy(() => import('features/dashboard/pages/Dashboard'));
const UserList = React.lazy(() => import('features/user/pages/UserList'));
const UserAdd = React.lazy(() => import('features/user/pages/UserAdd'));
const UserEdit = React.lazy(() => import('features/user/pages/UserEdit'));
const UserProfile = React.lazy(() => import('features/user/pages/UserProfile'));
const ActionList = React.lazy(() => import('features/setting/pages/ActionList'));
const GroupList = React.lazy(() => import('features/setting/pages/GroupList'));
const ShopList = React.lazy(() => import('features/shop/pages/ShopList'));
const WarehouseList = React.lazy(() => import('features/setting/pages/WarehouseList'));
const ProductList = React.lazy(() => import('features/product/pages/ProductList'));
const DesignList = React.lazy(() => import('features/product/pages/DesignList'));
const PartnerInvoiceList = React.lazy(() => import('features/invoice/pages/PartnerInvoiceList'));
const PartnerInvoiceView = React.lazy(() => import('features/invoice/pages/PartnerInvoiceView'));
const TransferGoodList = React.lazy(() => import('features/transfer/pages/TransferGoodList'));
const TransferGoodAdd = React.lazy(() => import('features/transfer/pages/TransferGoodAdd'));
const TransferGoodEdit = React.lazy(() => import('features/transfer/pages/TransferGoodEdit'));
const TransferGoodScan = React.lazy(() => import('features/transfer/pages/TransferGoodScan'));
const OrderList = React.lazy(() => import('features/order/pages/OrderList'));
const DesignerOrderList = React.lazy(() => import('features/order/pages/DesignerOrderList'));
const OrderView = React.lazy(() => import('features/order/pages/OrderView'));
const OrderFind = React.lazy(() => import('features/order/pages/OrderFind'));
const RequestOrderList = React.lazy(() => import('features/order/pages/RequestOrderList'));
const DesignTemplateList = React.lazy(() => import('features/template/pages/DesignTemplateList'));

const ManufactureProduct = React.lazy(() => import('features/manufacture/pages/ManufactureProduct'));
const ManufactureScan = React.lazy(() => import('features/manufacture/pages/ManufactureScan'));
const PackingScan = React.lazy(() => import('features/manufacture/pages/PackingScan'));
const WeightList = React.lazy(() => import('features/weight/pages/WeightList'));
const DesignerScheduleList = React.lazy(() => import('features/designer/pages/DesignerScheduleList'));

export default function RouterView() {
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/" element={<AuthPage />}>
                    <Route index element={<LoginPage />} />
                    <Route path="forgot" element={<ForgotPage />} />
                    <Route path="change-password" element={<ChangePassPage />} />
                </Route>
                <Route
                    path="/dashboard"
                    element={
                        <PrivateRoute>
                            <Dashboard />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/list/:type"
                    element={
                        <PrivateRoute>
                            <UserList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/add/:type"
                    element={
                        <PrivateRoute>
                            <UserAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/edit/:id"
                    element={
                        <PrivateRoute>
                            <UserEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/profile"
                    element={
                        <PrivateRoute>
                            <UserProfile />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/action"
                    element={
                        <PrivateRoute>
                            <ActionList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/group"
                    element={
                        <PrivateRoute>
                            <GroupList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/shop"
                    element={
                        <PrivateRoute>
                            <ShopList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/warehouse"
                    element={
                        <PrivateRoute>
                            <WarehouseList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/product/:id/:domain"
                    element={
                        <PrivateRoute>
                            <ProductList stockWarning={false} />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/stockWarning/:id/:domain"
                    element={
                        <PrivateRoute>
                            <ProductList stockWarning={true} />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/shopProduct"
                    element={
                        <PrivateRoute>
                            <ProductList stockWarning={false} isShopProductRoute={true} />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/design"
                    element={
                        <PrivateRoute>
                            <DesignList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/partnerInvoice"
                    element={
                        <PrivateRoute>
                            <PartnerInvoiceList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/partnerInvoice/view/:id"
                    element={
                        <PrivateRoute>
                            <PartnerInvoiceView />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template"
                    element={
                        <PrivateRoute>
                            <DesignTemplateList />
                        </PrivateRoute>
                    }
                />

                <Route
                    path="/transfer/list/:type"
                    element={
                        <PrivateRoute>
                            <TransferGoodList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/transfer/add/:type"
                    element={
                        <PrivateRoute>
                            <TransferGoodAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/transfer/scan"
                    element={
                        <PrivateRoute>
                            <TransferGoodScan />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/transfer/edit/:id"
                    element={
                        <PrivateRoute>
                            <TransferGoodEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/designerOrder"
                    element={
                        <PrivateRoute>
                            <DesignerOrderList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/order"
                    element={
                        <PrivateRoute>
                            <OrderList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/order/view/:id"
                    element={
                        <PrivateRoute>
                            <OrderView />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/findOrder"
                    element={
                        <PrivateRoute>
                            <OrderFind />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/requestOrder"
                    element={
                        <PrivateRoute>
                            <RequestOrderList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/manufacture/product/:type"
                    element={
                        <PrivateRoute>
                            <ManufactureProduct />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/manufacture/scan/:type"
                    element={
                        <PrivateRoute>
                            <ManufactureScan />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/packing/scan"
                    element={
                        <PrivateRoute>
                            <PackingScan />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/weight"
                    element={
                        <PrivateRoute>
                            <WeightList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/designerSchedule"
                    element={
                        <PrivateRoute>
                            <DesignerScheduleList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/designer/holiday-schedule"
                    element={
                        <PrivateRoute>
                            <DesignerScheduleList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/product"
                    element={
                        <PrivateRoute>
                            <ProductList stockWarning={false} />
                        </PrivateRoute>
                    }
                ></Route>
                <Route path="/not-found" element={<NotFound />}></Route>
                <Route path="*" element={<NotFound />}></Route>
            </Routes>
        </BrowserRouter>
    );
}
