import { ApiResponse } from 'types/common';
import { ItemFile } from 'types/common/Item';
import Http from './http.class';

const http = new Http('multipart/form-data').instance;
const httpApi = new Http().instance;

const FileService = {
    async upload(fileObj: File, type: string = '') {
        const formData = new FormData();
        formData.append('file', fileObj);
        const { data } = await http.post<ApiResponse<ItemFile>>(
            type === '' ? '/file/upload' : `/file/upload?type=${type}`,
            formData
        );
        return data;
    },
    async exportPdf(html: string, withProduct: boolean) {
        const { data } = await httpApi.post<ApiResponse<string>>('/file/export-pdf', { html, withProduct });
        return data;
    },
    async getFileUrlsByIds(file_ids: string[]): Promise<string[]> {
        const { data } = await http.get<ApiResponse<string[]>>(`/file/urls-by-ids?file_ids=${file_ids.join(',')}`);
        if (data.success) return data.data;
        return [];
    },
};

export default FileService;
