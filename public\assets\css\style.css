html .content.app-content {
    min-height: calc(100vh - 50px);
}
.dropdown-menu.show {
    position: absolute;
    inset: 0px auto auto 0px;
    margin: 0px;
}
.main-menu .shadow-bottom.onScrollMenu {
    display: block;
}
.main-menu .navbar-header {
    height: 75px;
}
.main-menu .navbar-header .navbar-brand .brand-logo img {
    max-width: 189px;
}
.MuiTabs-scroller.MuiTabs-fixed {
    max-width: 100%;
    overflow: auto !important;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar {
    width: 4px;
    height: 8px;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar:hover {
    width: 4px;
    height: 8px;
}

.MuiTabs-scroller.MuiTabs-fixed::-webkit-scrollbar-thumb {
    border-radius: 10px;
    width: 4px;
    height: 8px;
    background-color: #ccc;
}

.bg-warning .card-body {
    padding: 0.5rem 0.5rem;
}

.cursor-pointer {
    cursor: pointer;
}

span.error {
    color: #ea5455;
}

.input-group-text-readOnly {
    background-color: #efefef;
}

.no-border-lr {
    border-left: none !important;
    border-right: none !important;
}

.badge-balance {
    margin: 0 0.5rem;
}
.checkbox-btn {
    margin-right: 5px;
}
.btn-send-group {
    display: flex;
    flex-direction: column;
}
button.color-primary:hover {
    background-color: rgba(0, 0, 0, 0.04);
}
.thAction1 {
    width: 50px;
}
.thAction2 {
    width: 100px;
}
.thAction3 {
    width: 130px;
}
.thWidth150 {
    width: 150px;
}
.products-dropdown {
    width: calc(100% - 30px);
    position: absolute;
    z-index: 999;
}
.products-dropdown .card-footer {
    padding-bottom: 0;
}
.text-heading {
    --bs-text-opacity: 1;
    color: #444050 !important;
}
.w-px-150 {
    width: 150px !important;
}
.image-product {
    max-height: 50px;
    width: 50px;
}
.border-none {
    border-style: none;
}
.bootstrap-touchspin.input-group {
    margin: 0 auto;
}
.icon-remove-scan {
    position: absolute;
    right: -15px;
    top: -15px;
}
.form-check-input:disabled {
    background-color: #efefef;
    border-color: #efefef;
}
.order-image-position {
    height: 200px;
}
.order-image-position-sleeve {
    height: 100px;
}
.form-check-input:disabled:checked {
    background-color: #7367f0 !important;
    border-color: #7367f0 !important;
}
.h-50px {
    height: 50px;
}
.lh-30px {
    line-height: 30px;
}

@media only screen and (max-width: 1366px) {
    .order-image-position {
        height: 100px;
    }
}
