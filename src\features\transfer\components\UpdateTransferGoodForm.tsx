import { QueryObserverResult, RefetchOptions, useMutation, useQuery } from '@tanstack/react-query';
import ChooseProducts from 'components/partials/ChooseProducts';
import ModalConfirm from 'components/partials/ModalConfirm';
import UpdateButton from 'components/partials/UpdateButton';
import { QUERY_KEY } from 'constants/common';
import { find, findIndex, isEmpty, some, sumBy } from 'lodash';
import { useEffect, useState } from 'react';
import { XCircle } from 'react-feather';
import { useTranslation } from 'react-i18next';
import ProductService from 'services/ProductService';
import ShopService from 'services/ShopService';
import TransferGoodService from 'services/TransferGoodService';
import { useAuthStore } from 'stores/authStore';
import { ProductChoose } from 'types/Product';
import { TransferGoodBarcode, TransferGoodBarcodeProduct, TransferStatus } from 'types/TransferGood';
import { showToast } from 'utils/common';
import ListTransferGoodProduct from './ListTransferGoodProduct';
import ScanZone from './ScanZone';
import TransferGoodAction from './TransferGoodAction';
import TransferGoodInfo from './TransferGoodInfo';
import ListTransferGoodOrderProduct from './ListTransferGoodOrderProduct';
import FileService from '../../../services/FileService';
import OrderService from '../../../services/OrderService';

interface IProps {
    transferGood: TransferGoodBarcode;
    refetch: (options?: RefetchOptions) => Promise<QueryObserverResult<TransferGoodBarcode | null, Error>>;
    isScan?: boolean;
    onRemove?: () => void;
}

export default function UpdateTransferGoodForm({ transferGood, refetch, isScan, onRemove }: Readonly<IProps>) {
    const [products, setProducts] = useState<TransferGoodBarcodeProduct[]>([]);
    const [editable, setEditable] = useState(false);
    const [show, setShow] = useState(false);
    const [itemId, setItemId] = useState('');
    const [isFinish, setIsFinish] = useState(false);
    //const [productChildIds, setProductChildIds] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const user = useAuthStore((state) => state.user);
    const { t } = useTranslation();

    useEffect(() => {
        setProducts(transferGood.products);
        //setProductChildIds(transferGood.products.map((item) => item.product_child_id));
        setEditable(transferGood.status_id === TransferStatus.PENDING && !transferGood.is_auto);
    }, [transferGood]);

    const onScan = async (code: string, it?: TransferGoodBarcodeProduct[]) => {
        let index = -1;
        const items = [...(it ?? products)];
        items.forEach((item, i) => {
            if (item.barcode.code === code) {
                index = i;
                return false;
            }
        });
        if (index > -1) {
            items[index].scan_quantity++;
            setProducts(items);
        } else {
            const response = await ProductService.getBySku(code);
            if (response.success) {
                items.push({ ...response.data, transfer_good_product_id: '', quantity: 0, scan_quantity: 1 });
                setProducts(items);
            } else showToast(response.success, response.messages);
        }
    };

    const adjustScanQuantity = (index: number, quantity: number, isScanQuantity: boolean) => {
        const items = [...products];
        if (isScanQuantity) items[index].scan_quantity = quantity;
        else items[index].quantity = quantity;
        setProducts(items);
    };

    const { data: shops } = useQuery({
        enabled: !!user && editable,
        queryKey: [QUERY_KEY.SHOPS, user?.id],
        queryFn: ({ queryKey }) => ShopService.getOwns(queryKey[1] ?? ''),
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => TransferGoodService.deleteProduct(transferGood.id!, id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShow(false);
                    const items = [...products].filter((item) => item.transfer_good_product_id !== itemId);
                    setProducts(items);
                    setItemId('');
                }
            }
        },
    });

    const handleDelete = (id: string, index: number) => {
        setIsFinish(false);
        if (id) {
            deleteMutation.reset();
            setItemId(id);
            setShow(true);
        } else {
            const items = [...products].filter((_, idx) => idx !== index);
            setProducts(items);
        }
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const onChooseProducts = async (ids: string[], _: ProductChoose[]) => {
        //setProductChildIds(ids);
        const items = [...products];
        const productIds: string[] = [];
        ids.forEach((id) => {
            const index = findIndex(items, { product_child_id: id });
            if (index > -1) items[index].quantity++;
            else productIds.push(id);
        });
        if (!isEmpty(productIds)) {
            const response = await ProductService.getListChildren(productIds);
            if (response.success) {
                response.data.items.forEach((p) => {
                    p.transfer_good_product_id = '';
                    p.quantity = 1;
                    p.scan_quantity = 0;
                    items.push(p);
                });
            } else showToast(false, response.messages);
        }
        setProducts(items);
    };

    const addProducts = async () => {
        const items = products
            .filter((item) => !item.transfer_good_product_id)
            .map((item) => ({
                quantity: item.quantity,
                product_child_id: item.product_child_id,
            }));
        if (some(items, (item) => item.quantity <= 0)) {
            showToast(false, [t('error.quantity')]);
            return;
        }
        setIsLoading(true);
        const response = await TransferGoodService.addProducts(transferGood.id!, items);
        setIsLoading(false);
        showToast(response.success, response.messages);
        if (response.success) refetch();
    };

    const finishScan = async () => {
        const items = products
            .filter((item) => item.scan_quantity > 0)
            .map((item) => ({
                scan_quantity: item.scan_quantity,
                product_child_id: item.product_child_id,
            }));
        setIsLoading(true);
        const response = await TransferGoodService.finish(transferGood.id!, items);
        setIsLoading(true);
        showToast(response.success, response.messages);
        if (response.success) {
            setShow(false);
            refetch();
        }
    };

    const printHeroBarcode = async (item: TransferGoodBarcodeProduct) => {
        const response = await OrderService.transferPrint(item.barcode.code, {
            product: {
                image: item.style?.image ?? '',
                name: item.style.title,
                code: item.code,
                size: item.style.size,
                color: item.style.color,
                barcode: item.barcode.code,
                barcodeImg: item.barcode.image,
            },
        });
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
    };

    const onPrint = (code: string) => {
        const item = find(transferGood.orderProducts, (_item) => _item.barcode.code === code);
        if (item) printHeroBarcode(item);
        else showToast(false, [t('error.barcode')]);
    };

    return (
        <>
            {isScan && transferGood.status_id === TransferStatus.TRANSFERRING && (
                <ScanZone onScan={onScan} products={products} />
            )}
            <div className="row">
                <div className="col-xl-9 col-md-8 col-12">
                    <div className="card">
                        <div className="card-body position-relative">
                            {isScan && onRemove && (
                                <XCircle
                                    size={28}
                                    className="icon-remove-scan text-danger cursor-pointer"
                                    onClick={onRemove}
                                />
                            )}
                            <TransferGoodInfo
                                transferGood={transferGood}
                                productCount={sumBy(products, (item) => item.quantity)}
                            />
                        </div>
                        <div className="row me-0 ms-0">
                            {editable && !isScan && (
                                <ChooseProducts
                                    shops={shops?.items ?? []}
                                    vendor={transferGood.supplier.last_name}
                                    productChildIds={[]}
                                    onChoose={onChooseProducts}
                                />
                            )}
                            <ListTransferGoodProduct
                                id={transferGood.id!}
                                statusId={transferGood.status_id}
                                isScan={isScan}
                                items={products}
                                editable={editable}
                                adjustScanQuantity={adjustScanQuantity}
                                handleDelete={handleDelete}
                            />
                            {editable && !isScan && some(products, (item) => !item.transfer_good_product_id) && (
                                <div className="col-12 pb-1">
                                    <UpdateButton
                                        btnText={t('update')}
                                        isLoading={isLoading}
                                        btnClass={['mt-1']}
                                        onSubmit={addProducts}
                                    />
                                </div>
                            )}
                            {isScan &&
                                transferGood.status_id === TransferStatus.TRANSFERRING &&
                                some(products, (item) => item.scan_quantity > 0) && (
                                    <div className="col-12 pb-1">
                                        <UpdateButton
                                            btnText={t('constants.finish')}
                                            isLoading={isLoading}
                                            btnClass={['mt-1']}
                                            onSubmit={() => {
                                                setIsFinish(true);
                                                setShow(true);
                                            }}
                                        />
                                    </div>
                                )}
                        </div>
                    </div>
                </div>
                <div className="col-xl-3 col-md-4 col-12 invoice-actions mt-md-0 mt-2">
                    <TransferGoodAction transferGood={transferGood} isScan={isScan} refetch={refetch} />
                </div>
                {transferGood.is_auto && (
                    <div className="col-12 pb-1">
                        <ScanZone onScan={onPrint} hideInput={true} />
                        <div className="card">
                            <div className="card-body">
                                <p className="card-text mt-1">{t('order.products')}</p>
                                <ListTransferGoodOrderProduct
                                    items={transferGood.orderProducts ?? []}
                                    onPrint={printHeroBarcode}
                                />
                            </div>
                        </div>
                    </div>
                )}
            </div>
            <ModalConfirm
                show={show}
                text={
                    isFinish
                        ? t('confirm.status', {
                              data: t('constants.finish'),
                          })
                        : t('confirm.delete')
                }
                btnDisabled={isFinish ? isLoading : deleteMutation.isPending}
                changeShow={(s: boolean) => setShow(s)}
                submitAction={() => (isFinish ? finishScan() : deleteItem())}
            />
        </>
    );
}
