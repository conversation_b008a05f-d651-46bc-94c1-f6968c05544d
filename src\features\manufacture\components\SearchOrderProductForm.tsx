import SearchForm from 'components/partials/SearchForm';
import { filter, includes } from 'lodash';
import { useTranslation } from 'react-i18next';
import { YesNoChoices } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { OrderStatus, OrderStatusNames } from 'types/Order';
import { OrderProductStatus } from 'types/OrderProduct';
import { convertConstantToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
    typeId: OrderProductStatus;
}

export default function SearchOrderProductForm({ isLoading, typeId }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        { name: 'search_text', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12', show: true },
        //{ name: 'productChild_sku', type: 'text', label: t('sku'), wrapClassName: 'col-md-4 col-12' },
        {
            name: 'order_status_id__i',
            type: 'select',
            label: t('order.status'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(
                    convertConstantToSelectOptions(
                        OrderStatusNames.filter((item) =>
                            includes(
                                [
                                    OrderStatus.PENDING,
                                    OrderStatus.REQUESTED,
                                    OrderStatus.ENOUGH_STOCK,
                                    OrderStatus.MANUFACTURING,
                                ],
                                item.id
                            )
                        ),
                        t,
                        true
                    )
                ),
            },
            show: typeId === OrderProductStatus.PENDING,
        },
        {
            name: 'quantity_draft_full',
            type: 'select',
            label: t('constants.enough_stock'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
            },
            show: typeId === OrderProductStatus.PENDING,
        },
        // {
        //     name: 'designed_quantity_full',
        //     type: 'select',
        //     label: t('design.completed'),
        //     wrapClassName: 'col-md-4 col-12',
        //     options: {
        //         multiple: false,
        //         choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
        //     },
        //     show: typeId === OrderProductStatus.MANUFACTURING,
        // },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
