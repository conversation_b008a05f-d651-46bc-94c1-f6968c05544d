import { ApiResponse, DataListResponse } from 'types/common';
import {
    CountOrderByStatusParam,
    OrderCountParam,
    OrderCountStatusResponse,
    ProductsSoldList,
    ProductsSoldParam,
    TotalPriceList,
    TotalPriceParam,
} from 'types/Dashboard';
import Http from './http.class';

const http = new Http().instance;

const DashboardService = {
    async countOrder(params: OrderCountParam) {
        const { data } = await http.get<ApiResponse<number>>(`/dashboard/order-count`, { params });
        return data;
    },

    async getProductSold(params: ProductsSoldParam) {
        if (params['orderProducts.order.order_created_at__le']) {
            params['orderProducts.order.order_created_at__le'] = new Date(
                params['orderProducts.order.order_created_at__le']
            ).toISOString();
        }
        if (params['orderProducts.order.order_created_at__ge']) {
            params['orderProducts.order.order_created_at__ge'] = new Date(
                params['orderProducts.order.order_created_at__ge']
            ).toISOString();
        }
        const { data } = await http.get<ApiResponse<DataListResponse<ProductsSoldList>>>(`/dashboard/products-sold`, {
            params,
        });
        if (data.success) return data.data;
    },

    async getTotalPrice(params: TotalPriceParam) {
        const { data } = await http.get<ApiResponse<TotalPriceList[]>>(`/dashboard/total-price`, {
            params,
        });
        if (data.success) return data.data;
        return [];
    },

    async countOrderByStatus(params: CountOrderByStatusParam) {
        const { data } = await http.get<ApiResponse<OrderCountStatusResponse[]>>(`/dashboard/order-status-count`, {
            params,
        });
        if (data.success) return data.data[0];
    },
};

export default DashboardService;
