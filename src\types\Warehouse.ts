import { BaseModel, BaseSearch } from './common';

export interface SearchWarehouse extends BaseSearch {
    manufacturer_id?: string;
    status_id?: number;
    out_of_stock?: boolean;
}

export type SearchWarehouseParam = {
    [key in keyof SearchWarehouse]: string;
};

export default interface Warehouse extends BaseModel {
    name: string;
    manufacturer_id: string;
    country_id: string;
    address: string;
    status_id: number;
    quantity_minimum: number;
    lat: string;
    lng: string;
}

export interface WarehouseWithManufacturer {
    id: string;
    first_name: string;
    last_name: string;
    phone_number: string;
    email: string;
    password: string;
    avatar_id: string;
    role_id: number;
    status_id: number;
    gender_id: number;
    birthday: string | null;
    address: string;
    verified_email: boolean;
    shop_id: string;
    customer_id: string | null;
    country_id: string;
    json: ManufacturerAddressJson;
    two_factor_enabled: boolean;
    two_factor_secret: string | null;
}

export interface ManufacturerAddressJson {
    add1: string;
    add2: string;
    city: string;
    state: string;
    company: string;
    postcode: string;
}
