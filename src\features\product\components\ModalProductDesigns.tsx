import { yupResolver } from '@hookform/resolvers/yup';
import { useMutation, useQuery } from '@tanstack/react-query';
import classNames from 'classnames';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { uniq } from 'lodash';
import { useCallback, useEffect, useLayoutEffect, useState } from 'react';
import { Save, Star, Trash2, Upload } from 'react-feather';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select from 'react-select';
import ProductService from 'services/ProductService';
import { ItemParamModel, SelectOptionModel } from 'types/common/Item';
import { Placement, PlacementNames, ProductDesign } from 'types/Product';
import { getFieldInArrayObject, isValidImageFile, selectItem, showToast, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import FileService from '../../../services/FileService';
import './ModalProductDesign.css';

interface IProps {
    show: boolean;
    productId: string;
    productName: string;
    colors: ItemParamModel[];
    changeShow: (s: boolean) => void;
}

export default function ModalProductDesigns({ show, productId, productName, colors, changeShow }: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [itemId, setItemId] = useState('');
    const [fileImage, setFileImage] = useState<File>();
    const [image, setImage] = useState('');
    const [showDelete, setShowDelete] = useState(false);
    const [fileId, setFileId] = useState('');
    const [x, setX] = useState('');
    const [y, setY] = useState('');
    const [w, setW] = useState('');
    const [h, setH] = useState('');
    const [codeUpdated, setCodeUpdated] = useState('');

    const schema = yup
        .object({
            color: yup.string().required(t('error.required')).trim(),
            position: yup
                .object({
                    x: yup.number().typeError(t('error.number')).required(t('error.required')).min(0, t('error.min_0')),
                    y: yup.number().typeError(t('error.number')).required(t('error.required')).min(0, t('error.min_0')),
                    w: yup
                        .number()
                        .typeError(t('error.number'))
                        .required(t('error.required'))
                        .moreThan(0, t('error.greaterThan_0')),
                    h: yup
                        .number()
                        .typeError(t('error.number'))
                        .required(t('error.required'))
                        .moreThan(0, t('error.greaterThan_0')),
                })
                .required(t('error.required')),
        })
        .required();
    const {
        register,
        handleSubmit,
        reset,
        formState: { isSubmitting, errors },
        watch,
        control,
        setValue,
    } = useForm<ProductDesign>({
        resolver: yupResolver(schema),
    });

    const watchColor = watch('color');
    const watchPlacement = watch('placement_id');
    const watchCode = watch('code');

    useEffect(() => {
        if (show) {
            setItemId('');
            setFileImage(undefined);
            setImage('');
            reset({
                placement_id: Placement.FRONT,
                file_id: '',
                product_child_ids: [],
                color: '',
                position: {
                    x: 0,
                    y: 0,
                    w: 0,
                    h: 0,
                },
            });
        }
    }, [show]);

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!productId && show,
        queryKey: [QUERY_KEY.DESIGNS, productId],
        queryFn: ({ queryKey }) => ProductService.getDesigns(queryKey[1]!),
    });
    useEffect(() => {
        if (data && data.length > 0) {
            const item = data.find((item) => Boolean(item.code));
            if (item) {
                setCodeUpdated(item.code);
                if (!watchCode) {
                    setValue('code', item.code, { shouldDirty: false });
                }
            }
        }
    }, [data]);

    const { data: dataDesignCodes } = useQuery({
        queryKey: [QUERY_KEY.DESIGN_CODES],
        queryFn: () => ProductService.getDesignCodes(),
    });
    const designCodes: SelectOptionModel[] =
        dataDesignCodes
            ?.filter((item) => item !== null)
            .map((item) => ({
                value: item,
                label: item,
            })) ?? [];

    const deleteMutation = useMutation({
        mutationFn: (id: string) => ProductService.deleteDesign(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const onChooseFile = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const file = event.target.files[0];
            if (!isValidImageFile(file)) {
                showToast(false, [t('error.chooseImage')]);
                return;
            }
            setFileImage(file);
            setImage(URL.createObjectURL(file));
        }
    };

    const updatePosition = async (designId: string) => {
        if (x && y && w && h) {
            const response = await ProductService.updateDesignSizes({
                position: { x: +x, y: +y, w: +w, h: +h },
                ids: [designId],
                code: codeUpdated,
            });
            showToast(response.success, response.messages);
            //if (response.success) refetch();
        } else showToast(false, ['Error']);
    };

    const onSubmit = async (param: ProductDesign) => {
        param.file_id = fileId;
        if (!fileId) {
            if (!fileImage) {
                showToast(false, [t('design.fileRequired')]);
                return;
            }
            const results = await FileService.upload(fileImage);
            if (!results.success) {
                showToast(false, results.messages);
                return;
            }
            param.file_id = results.data.id;
        }
        param.product_child_ids = colors.filter((item) => item.name === param.color).map((item) => item.id);
        const response = await ProductService.addDesign(productId, param);
        showToast(response.success, response.messages);
        if (response.success) {
            setFileImage(undefined);
            setImage('');
            reset({
                placement_id: Placement.FRONT,
                file_id: '',
                product_child_ids: [],
                color: response.data.code,
                position: {
                    x: 0,
                    y: 0,
                    w: 0,
                    h: 0,
                },
            });
            refetch();
        }
    };

    const getDesignFile = useCallback(() => {
        const designItem = data?.find(
            (item: ProductDesign) => item.color === watchColor && Number(item.placement_id) === Number(watchPlacement)
        );
        if (designItem?.file) {
            setImage(designItem.file.file_url);
            setFileId(designItem.file.id);
        } else {
            setImage('');
            setFileId('');
        }
    }, [data, watchColor, watchPlacement]);

    useEffect(() => {
        getDesignFile();
    }, [getDesignFile, data, watchColor, watchPlacement]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t('design.of', { data: productName })}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <div className="modal-body">
                            {(isLoading || isRefetching) && <Spinner />}
                            {data && !isLoading && !isRefetching && (
                                <form onSubmit={handleSubmit(onSubmit)}>
                                    <div className="table-responsive design-table-container">
                                        <table className="table design-table">
                                            <thead>
                                                <tr>
                                                    <th className="text-center">{t('no.')}</th>
                                                    <th className="text-center">{t('color')}</th>
                                                    <th className="text-center">{t('design.placement')}</th>
                                                    <th className="text-center">{t('code')}</th>
                                                    <th className="text-center">{t('design.file')}</th>
                                                    <th className="text-center">x</th>
                                                    <th className="text-center">y</th>
                                                    <th className="text-center">{t('width')}</th>
                                                    <th className="text-center">{t('height')}</th>
                                                    <th className="thAction3"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td colSpan={3}></td>
                                                    <td className="text-center">
                                                        <Select
                                                            name="codeUpdated"
                                                            options={designCodes}
                                                            onChange={(val) => {
                                                                setCodeUpdated(val?.value ?? '');
                                                            }}
                                                            value={designCodes.find(
                                                                (item) => item.value === codeUpdated
                                                            )}
                                                            isClearable
                                                            menuPlacement="auto"
                                                            menuPosition="fixed"
                                                        />
                                                    </td>
                                                    <td></td>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            value={x}
                                                            onChange={(e) => setX(e.target.value.trim())}
                                                        />
                                                    </td>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            value={y}
                                                            onChange={(e) => setY(e.target.value.trim())}
                                                        />
                                                    </td>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            value={w}
                                                            onChange={(e) => setW(e.target.value.trim())}
                                                        />
                                                    </td>
                                                    <td>
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            value={h}
                                                            onChange={(e) => setH(e.target.value.trim())}
                                                        />
                                                    </td>
                                                    <td></td>
                                                </tr>
                                                {data.map((item: ProductDesign, index: number) => (
                                                    <tr key={item.id}>
                                                        <td className="text-center">
                                                            {index + 1}{' '}
                                                            {item.is_design_ok && (
                                                                <Star className="text-warning" size={14} />
                                                            )}
                                                        </td>
                                                        <td className="text-center">{item.color}</td>
                                                        <td className="text-center">
                                                            {t(
                                                                `constants.${getFieldInArrayObject(
                                                                    PlacementNames,
                                                                    item.placement_id
                                                                )}`
                                                            )}
                                                        </td>
                                                        <td className="text-center">{item.code}</td>
                                                        <td className="text-center">
                                                            {item.file?.file_url && (
                                                                <img
                                                                    src={item.file?.file_url}
                                                                    className="uploadedAvatar rounded me-50"
                                                                    alt=""
                                                                    height={100}
                                                                    width={100}
                                                                />
                                                            )}
                                                        </td>
                                                        <td className="text-center">{item.position.x}</td>
                                                        <td className="text-center">{item.position.y}</td>
                                                        <td className="text-center">{item.position.w}</td>
                                                        <td className="text-center">{item.position.h}</td>
                                                        <td className="text-center">
                                                            <button
                                                                type="button"
                                                                title={t('update')}
                                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                onClick={() => updatePosition(item.id!)}
                                                            >
                                                                <Save size={14} />
                                                            </button>
                                                            <button
                                                                type="button"
                                                                title={t('delete')}
                                                                className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                                onClick={() => handleDelete(item.id!)}
                                                            >
                                                                <Trash2 size={14} />
                                                            </button>
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td className="text-center">{data.length + 1}</td>
                                                    <td className="text-center">
                                                        <select
                                                            {...register('color')}
                                                            className={classNames('form-select', {
                                                                'is-invalid': Boolean(errors.color?.message),
                                                            })}
                                                        >
                                                            <option value=""></option>
                                                            {uniq(colors.map((cItem) => cItem.name)).map((cItem) => (
                                                                <option key={cItem} value={cItem}>
                                                                    {cItem}
                                                                </option>
                                                            ))}
                                                        </select>
                                                    </td>
                                                    <td className="text-center">
                                                        <select {...register('placement_id')} className="form-select">
                                                            {selectItem(PlacementNames, t, true)}
                                                        </select>
                                                    </td>
                                                    <td className="text-center">
                                                        <Controller
                                                            control={control}
                                                            name="code"
                                                            defaultValue={watchCode}
                                                            render={({ field }) => {
                                                                const defaultValue = designCodes.find(
                                                                    (option) => option.value === watchCode
                                                                );
                                                                return (
                                                                    <Select
                                                                        {...field}
                                                                        options={designCodes}
                                                                        onChange={(val) =>
                                                                            field.onChange(val?.value ?? '')
                                                                        }
                                                                        value={defaultValue}
                                                                        isClearable
                                                                        menuPlacement="auto"
                                                                        menuPosition="fixed"
                                                                    />
                                                                );
                                                            }}
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        {image && (
                                                            <img
                                                                src={image}
                                                                className="uploadedAvatar rounded me-50"
                                                                alt=""
                                                                height={100}
                                                                width={100}
                                                            />
                                                        )}
                                                        <label
                                                            htmlFor="image-upload"
                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect cursor-pointer"
                                                        >
                                                            <Upload size={14} />
                                                        </label>
                                                        <input
                                                            onChange={onChooseFile}
                                                            type="file"
                                                            id="image-upload"
                                                            hidden
                                                            accept="image/*"
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <input
                                                            {...register('position.x')}
                                                            type="text"
                                                            className={classNames('form-control', {
                                                                'is-invalid': Boolean(errors.position?.x?.message),
                                                            })}
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <input
                                                            {...register('position.y')}
                                                            type="text"
                                                            className={classNames('form-control', {
                                                                'is-invalid': Boolean(errors.position?.y?.message),
                                                            })}
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <input
                                                            {...register('position.w')}
                                                            type="text"
                                                            className={classNames('form-control', {
                                                                'is-invalid': Boolean(errors.position?.w?.message),
                                                            })}
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <input
                                                            {...register('position.h')}
                                                            type="text"
                                                            className={classNames('form-control', {
                                                                'is-invalid': Boolean(errors.position?.h?.message),
                                                            })}
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <button
                                                            type="submit"
                                                            title={t('update')}
                                                            className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                            disabled={isSubmitting}
                                                        >
                                                            <Save size={14} />
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </form>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
            <ModalConfirm
                show={showDelete}
                text={t('confirm.delete')}
                btnDisabled={deleteMutation.isPending}
                changeShow={(s: boolean) => setShowDelete(s)}
                submitAction={deleteItem}
            />
        </>
    );
}
