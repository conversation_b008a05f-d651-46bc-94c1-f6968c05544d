import { useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, REACT_APP_IS_ADMIN } from 'constants/common';
import { find, isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import CountryService from 'services/CountryService';
import UserService from 'services/UserService';
import WarehouseService from 'services/WarehouseService';
import { useAuthStore } from 'stores/authStore';
import { SearchUser, UserRole } from 'types/User';
import Warehouse, { SearchWarehouse, SearchWarehouseParam } from 'types/Warehouse';
import { ItemStatus } from 'types/common/Item';
import { showToast } from 'utils/common';
import ListWarehouse from '../components/ListWarehouse';
import ModalWarehouseUpdate from '../components/ModalWarehouseUpdate';
import useQueryParams from '../../../hooks/useQueryParams';
import SearchWarehouseForm from '../components/SearchWarehouseForm';

export default function WarehouseList() {
    const { t } = useTranslation();
    const [itemId, setItemId] = useState('');
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const user = useAuthStore((state) => state.user);

    const { queryParams, setQueryParams } = useQueryParams<SearchWarehouseParam>();
    const paramConfig: SearchWarehouseParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            status_id: queryParams.status_id,
        },
        isUndefined
    );

    const { data: countries } = useQuery({
        queryKey: [QUERY_KEY.COUNTRIES],
        queryFn: () => CountryService.getList(),
    });

    const { data: manufacturers } = useQuery({
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.MANUFACTURER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.WAREHOUSES, paramConfig, user],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse, user!.id),
    });

    const updateMutation = useMutation({
        mutationFn: (param: Partial<Warehouse>) => WarehouseService.update(param, param.id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowUpdate(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => WarehouseService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const updateItem = (body: Partial<Warehouse>) => updateMutation.mutate(body);

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const handleEdit = (id: string) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const actionMenu = () => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId('');
    };

    return (
        <>
            <Helmet>
                <title>{t('warehouse.multiple')}</title>
            </Helmet>
            <ContentHeader
                title={t('warehouse.multiple')}
                contextMenu={
                    REACT_APP_IS_ADMIN
                        ? [
                              {
                                  text: t('warehouse.add'),
                                  icon: 'PLUS',
                                  fnCallBack: { actionMenu },
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchWarehouseForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListWarehouse
                                    items={data}
                                    countries={countries ?? []}
                                    manufacturers={manufacturers?.items ?? []}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                />
                            </div>
                            <ModalWarehouseUpdate
                                show={showUpdate}
                                warehouse={find(data, { id: itemId })}
                                countries={countries ?? []}
                                manufacturers={manufacturers?.items ?? []}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
