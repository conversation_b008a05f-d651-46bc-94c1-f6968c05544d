import { keepPreviousData, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import OrderProductService from 'services/OrderProductService';
import { useAuthStore } from 'stores/authStore';
import { OrderProductStatusNames } from 'types/Order';
import { SearchOrderProduct, SearchOrderProductParam } from 'types/OrderProduct';
import { SearchProductParam } from 'types/Product';
import { getFieldInArrayObject } from 'utils/common';
import ListOrderProduct from '../components/ListOrderProduct';
import SearchOrderProductForm from '../components/SearchOrderProductForm';

export default function ManufactureProduct() {
    const { type } = useParams();
    const [typeId, setTypeId] = useState(0);
    const navigate = useNavigate();
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);

    const { queryParams, setQueryParams } = useQueryParams<SearchOrderProductParam>();
    const paramConfig: SearchProductParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            quantity_draft_full: queryParams.quantity_draft_full,
            designed_quantity_full: queryParams.designed_quantity_full,
            //productChild_sku: queryParams.productChild_sku,
            order_status_id__i: queryParams.order_status_id__i,
        },
        isUndefined
    );

    useEffect(() => {
        const tId = +getFieldInArrayObject(OrderProductStatusNames, type ?? '', 'id', '0', 'name');
        if (tId > 0) setTypeId(tId);
        else navigate('/not-found');
    }, [type]);

    const { data, isLoading, isRefetching } = useQuery({
        enabled: !!user && typeId > 0,
        queryKey: [QUERY_KEY.ORDER_PRODUCTS, paramConfig, user, typeId],
        queryFn: ({ queryKey }) =>
            OrderProductService.getList(queryKey[1] as SearchOrderProduct, user!.id!, +queryKey[3]!),
        placeholderData: keepPreviousData,
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>{t('product.multiple')}</title>
            </Helmet>
            <ContentHeader title={t('product.multiple')} />
            <div className="content-body">
                <div className="col-12">
                    <SearchOrderProductForm isLoading={isLoading || isRefetching} typeId={typeId} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListOrderProduct items={data.items} />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
