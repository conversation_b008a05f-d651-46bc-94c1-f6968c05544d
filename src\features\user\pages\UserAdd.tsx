import ContentHeader from 'components/partials/ContentHeader';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { UserRoleNames } from 'types/User';
import { getFieldInArrayObject } from 'utils/common';
import UpdateUserForm from '../components/UpdateUserForm';

export default function UserAdd() {
    const { type } = useParams();
    const [roleId, setRoleId] = useState(0);
    const navigate = useNavigate();
    const { t } = useTranslation();

    useEffect(() => {
        const rId = +getFieldInArrayObject(UserRoleNames, type ?? '', 'id', '0', 'name');
        if (rId > 0) setRoleId(rId);
        else navigate('/not-found');
    }, [type]);

    return (
        <>
            <Helmet>
                <title>{t(`${type}.add`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.add`)}
                breadcrumbs={[
                    {
                        text: t(`${type}.multiple`),
                        to: `/user/list/${type}`,
                    },
                    {
                        text: t(`${type}.add`),
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    <UpdateUserForm id="" roleId={roleId} />
                </div>
            </div>
        </>
    );
}
