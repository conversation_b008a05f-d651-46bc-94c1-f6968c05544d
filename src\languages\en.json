{"siteName": "Print Drop Express", "error": {"common": "An error occurred during execution", "required": "This field cannot be left blank", "passwordLength": "Password must be at least 6 characters", "passwordNotSame": "Passwords are not the same", "email": "Invalid email", "number": "This field must be numeric", "min_0": "The smallest value is 0", "greaterThan_0": "The value need to greater than 0", "min_1": "The smallest value is 1", "max_99": "The largest value is 99", "chooseProduct": "Please choose at least one product", "chooseImage": "Invalid file, please select another image file", "barcode": "Invalid barcode", "quantity": "Invalid quantity", "invalid_file_type": "Only images (JPEG, PNG, GIF, WebP) are allowed", "file_too_large": "File too large, maximum 5MB", "file_name_too_long": "File name too long", "too_many_files": "Too many files selected, maximum 10 files", "file_read_failed": "Cannot read file", "file_corrupted": "File may be corrupted", "file_read_error": "Error reading file", "upload_failed": "Failed to process files", "upload": "Upload failed", "file_empty": "File is empty"}, "success": {"files_uploaded": "Successfully selected {{count}} file(s)"}, "constants": {"pending": "Pending", "active": "Active", "inactive": "In Active", "unknown": "Unknown", "male": "Male", "female": "Female", "other": "Other", "yes": "Yes", "no": "No", "archived": "Archived", "draft": "Draft", "front": "Front", "back": "Back", "long_sleeve": "Long Sleeve", "inner_neck": "Inner Neck", "left_sleeve": "Left Sleeve", "right_sleeve": "Right Sleeve", "approved": "Approved", "sent": "<PERSON><PERSON>", "transferring": "Transferring", "finish": "Finish", "rejected": "Rejected", "cancelled": "Cancelled", "requested": "Requested", "enough_stock": "Enough Stock", "manufacturing": "Manufacturing", "wait_pack": "Wait Pack", "packed": "Packed", "pending_partner": "Pending Partner", "transporting": "Transporting", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "side": "Side"}, "loginAdminDesc": "Log in to the administration system", "loginPartnerDesc": "Log in to the partner system", "loginManufacturerDesc": "Log in to the manufacturer system", "loginDesignerDesc": "Log in to the designer system", "phoneNumber": "Phone Number", "password": "Password", "forgotPassword": "Forgot Password", "login": "<PERSON><PERSON>", "forgotPasswordDesc": "Please enter Email to retrieve your password", "backToLogin": "Back to Login", "email": "Email", "tel": "Tel", "send": "Send", "security": "Security", "enable": "Enable", "disable": "Disable", "changePassword": "Change Password", "resetPassword": "Reset Password", "resetPasswordDesc": "Re-enter the new password to access the system", "oldPassword": "Old Password", "newPassword": "New password", "rePassword": "Re-enter the password", "website": "Website", "comment": "Comment", "ipAddress": "IP Address", "notifications": "Notifications", "readAll": "Read All", "notFound": "Not Found", "notFoundDesc": "The requested resource was not found on the server", "backToDashboard": "Back to Dashboard", "agree": "Agree", "add": "Add", "edit": "Edit", "delete": "Delete", "update": "Update", "totalRecords": "Total Records", "profile": "Profile", "logout": "Logout", "dashboards": "Dashboards", "no.": "No.", "displayOrder": "Display Order", "actions": "Actions", "actionName": "Action Name", "url": "URL", "parent": "Parent", "icon": "Icon", "fullName": "Full Name", "firstName": "First Name", "lastName": "Last Name", "country": "Country", "address": "Address", "statusName": "Status", "genderName": "Gender", "birthday": "Birthday", "verifiedEmail": "Verified email", "search": "Search", "reset": "Reset", "choose": "<PERSON><PERSON>", "keywords": "Keywords", "allowImageFormats": "Allowed formats: png, jpg, jpeg, gif, webp, bmp", "baseInformation": "Base Information", "uploadAvatar": "Upload Avatar", "uploadImage": "Upload Image", "permissions": "Permissions", "createdUser": "Created User", "createdTime": "Created Time", "sentTime": "Sent Time", "name": "Name", "domain": "Domain", "currency": "<PERSON><PERSON><PERSON><PERSON>", "code": "Code", "itemCode": "Item Code", "sku": "SKU", "type": "Type", "vendor": "<PERSON><PERSON><PERSON>", "quantityWarning": "Quantity Warning", "shopOwner": "Shop Owner", "accessToken": "Access Token", "webhookSecret": "Webhook Secret", "orderCode": "Order Code", "totalCost": "Total Cost", "totalPrice": "Total Price", "price": "Price", "quantity": "Quantity", "virtualQuantity": "Virtual Quantity", "missing": "Missing", "file": "File", "createAuto": "Created auto", "style": "Style", "barcode": "Barcode", "all": "All", "printJobSheet": "Print Job Sheet", "color": "Color", "size": "Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "centre": "Centre", "crossCheck": "Cross Check", "item": "<PERSON><PERSON>", "totalItem": "Total Item", "poNumber": "PO Number", "date": "Date", "startDate": "From Date", "endDate": "To Date", "details": "Details", "year": "Year", "transfer": "Transfer", "properties": "Properties", "confirmText": "Confirm", "lat": "Latitude", "lng": "Longitude", "confirm": {"delete": "Do you really want to delete this object?", "status": "Do you really want change status to {{data}}?", "createTransfer": "Do you really want to create transfer?", "startManufacturing": "Do you really want to start manufacturing?", "finishManufacture": "Do you really want to finish manufacture?", "finishPacking": "Do you really want to finish packing?"}, "scan": {"single": "<PERSON><PERSON>", "transfer": "Transfer Scan", "packing": "Packing Scan", "tipTitle": "Let's click in here to scan barcode", "tipDescription": "You will click successfully if the box is blue", "quantity": "Quantity <PERSON>an"}, "twoStepsVerification": {"title": "Two-steps verification", "desc": "Keep your account secure with authentication step", "authenticatorApps": "Authenticator Apps", "authenticatorAppDesc1": "Get codes from an app like Google Authenticator, Microsoft Authenticator, Authy or 1Password", "authenticatorAppDesc2": "Using an authenticator app like Google Authenticator, Microsoft Authenticator, Authy, or 1Password, scan the QR code. It will generate a 6 digit code for you to enter below", "authenticatorAppDesc3": "If you having trouble using the QR code, select manual entry on your app", "enterAuthenticationCode": "Enter authentication code"}, "otp": {"multiple": "OTP", "single": "OTP", "add": "OTP", "edit": "OTP"}, "group": {"multiple": "Groups", "single": "Group Name", "add": "Add Group", "edit": "Edit Group"}, "admin": {"single": "Administrator", "multiple": "Administrators", "add": "Add Administrator", "edit": "Edit Administrator"}, "partner": {"multiple": "Partners", "single": "Partner", "add": "Add Partner", "edit": "Edit Partner", "price": "Partner Price", "percent": "Partner Percent", "cost": "Partner Cost", "invalidPercentCost": "Partner percent or Partner cost is invalid", "invalidPrice": "Partner price is invalid"}, "customer": {"multiple": "Customers", "single": "Customer", "add": "Add Customer", "edit": "Edit Customer", "details": "Customer Details"}, "supplier": {"multiple": "Suppliers", "single": "Supplier", "add": "Add Supplier", "edit": "Edit Supplier", "debt": "Supplier Debt", "alertDebt": "Please search by Warehouse and Supplier Debt to create transfer"}, "manufacturer": {"multiple": "Manufacturers", "single": "Manufacturer", "add": "Add Manufacturer", "edit": "Edit Manufacturer", "status": "Manufacturer Status", "start": "Start Manufacturing", "finish": "Finish Manufacture", "finishPacking": "Finish Packaging", "productionNumber": "Production Number", "contactDetails": "Contact Details", "customerShippingAddress": "Customer shipping address", "dueDay": "Due day", "orderItem": "Order item", "company_name": "Company Name", "address_1": "Address Line 1", "address_2": "Address Line 2", "city": "City", "state": "State", "postcode": "Postcode", "printLabel": "Print Label"}, "template": {"multiple": "Design Templates", "single": "Design Template", "add": "Add Design Template", "edit": "Edit Design Template", "templates": "Templates", "detail": "Template Detail", "of": "Templates of {{data}}"}, "designer": {"multiple": "Designers", "single": "Designer", "add": "Add Designer", "edit": "Edit Designer", "wait": "Wait Designer"}, "shop": {"multiple": "Shops", "single": "Shop", "add": "Add Shop", "edit": "Edit Shop"}, "location": {"multiple": "Locations", "single": "Location", "of": "Locations of {{data}}"}, "warehouse": {"multiple": "Warehouses", "single": "Warehouse", "add": "Add Warehouse", "edit": "Edit Warehouse", "update": "Update Warehouse", "without": "Without Warehouses", "productsOutOfStock": "Warehouses with products that are about to run out of stock are {{data}}"}, "product": {"multiple": "Products", "single": "Product", "views": "View Products", "sync": "Sync Product", "syncConfirm": "Do you really want to sync products from this shop?", "quantityOf": "Quantity of {{data}}", "newQuantity": "New quantity", "stockWarning": "Stocks warning", "cost": "Products Cost", "assignTemplate": "Assign <PERSON><PERSON><PERSON>", "isSupplier": "Is Supplier Product", "root": "Root Product", "showChildProduct": "Show Child Product", "updateStatus": "Update Product Status"}, "design": {"multiple": "Designs", "single": "Design", "of": "Designs of {{data}}", "placement": "Placement", "file": "<PERSON>", "fileRequired": "Please choose design file", "colorRequired": "Please choose color", "placementExist": "Placement is existed", "quantity": "Designed quantity", "completed": "Completed design", "sleeve": "Sleeve", "left": "Left", "right": "Right", "digital": "Digital", "digitalAI": "Digital AI", "sizeAndPosition": "Position", "done": "Done Design", "doneAll": "Done All Design", "preset": "Preset", "presetRequired": "Please input preset only 1 character", "cost": "Design Cost", "assign": "Assign to shops"}, "designerSchedule": {"multiple": "Designer Holiday Schedules", "single": "Designer Holiday Schedule", "add": "Add Designer Holiday Schedule", "edit": "Edit Designer Holiday Schedule"}, "partnerInvoice": {"multiple": "Partner Invoices", "single": "Partner Invoice", "code": "Invoice Code", "payment": "Payment", "paymentTime": "Payment Time", "paymentSuccess": "Payment Success"}, "order": {"multiple": "Orders", "single": "Order", "status": "Order Status", "billingAddress": "Billing Address", "shipping": "Shipping", "shippingAddress": "Shipping Address", "shippingCost": "Shipping Cost", "paymentGateway": "Payment Gateway", "date": "Order date", "note": "Order Notes", "summaries": "Order Summaries", "viewMap": "View Map", "count": "Order Count", "resendShip": "Resend Ship", "isDesignerDone": "Is Designer Done", "doneDesign": "Designer Done", "find": "Find Order", "products": "Order Products", "notProduced": "The order has products that have not yet been produced", "carrier": "Carrier"}, "requestOrder": {"multiple": "Request Orders", "itemPerOrder": "Item Per Order"}, "receive": {"multiple": "Receive Products", "single": "Receive Product", "add": "Add Receive Product", "edit": "View Receive Product", "create": "Create Receive Product"}, "delivery": {"multiple": "Delivery Products", "add": "Add Delivery Product", "edit": "View Delivery Product", "create": "Create Delivery Product"}, "productWeight": {"multiple": "Product Weights", "single": "Product Weight", "add": "Add Product Weight", "edit": "Edit Product Weight", "weight": "Weight (gram)", "sizeError": "Width and Height is required and need to greater than 0"}, "partnerOrderQuantity": "Partner's Order Quantity", "salesRevenue": "Sales Revenue", "goodsSold": " Quantity of Goods Sold", "countOrderByStatus": "Order's Status", "total_order": "Total Order", "total_revenue": "Total Revenue", "createdBy": "Created By", "totalImages": "Total Images", "updatedTime": "Updated Time", "photo": "Photo", "uploadPhoto": "Upload Photo", "noImageSelected": "No image selected", "selectedFiles": "Selected {{count}} file(s)", "selectedImages": "Selected {{count}} image(s)", "uploadHint": "Select images to upload (JPEG, PNG, GIF, WebP)", "deleteFile": "Delete file", "save": "Save"}