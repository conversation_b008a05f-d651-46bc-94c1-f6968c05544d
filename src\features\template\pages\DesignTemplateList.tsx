import ContentHeader from 'components/partials/ContentHeader';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import SearchTemplateForm from '../components/SearchTemplateForm';
import Spinner from 'components/partials/Spinner';
import { useQuery } from '@tanstack/react-query';
import DesignTemplate, { SearchTemplateParam } from 'types/DesignTemplate';
import { isUndefined, omitBy } from 'lodash';
import { PAGINATION, QUERY_KEY, REACT_APP_IS_ADMIN, REACT_APP_IS_PARTNER } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { useAuthStore } from 'stores/authStore';
import DesignTemplateService from 'services/DesignTemplateService';
import ModalConfirm from 'components/partials/ModalConfirm';
import DesignTemplateList from '../components/DesignTemplateList';
import PaginationTable from 'components/partials/PaginationTable';
import TemplateFormModal from '../components/TemplateFormModal';
import ModalTemplateDetail from '../components/ModalTemplateDetail';
import { useDesignTemplateActions } from '../hooks/useDesignTemplateActions';
import { useDesignTemplateModal } from '../hooks/useDesignTemplateModal';
import { SearchUser, UserRole } from '../../../types/User';
import UserService from '../../../services/UserService';

export default function TemplateDesignList() {
    const { t } = useTranslation();
    const { queryParams, setQueryParams } = useQueryParams<SearchTemplateParam>();
    const user = useAuthStore((state) => state.user);

    const {
        modalState,
        viewModalState,
        openCreateModal,
        openEditModal,
        openDeleteModal,
        openViewModal,
        closeModal,
        closeViewModal,
        isEditMode,
        isDeleteMode,
    } = useDesignTemplateModal();

    const { data: partners } = useQuery({
        enabled: REACT_APP_IS_ADMIN,
        queryKey: [QUERY_KEY.USER, { role_id__i: UserRole.PARTNER.toString() }],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const paramConfig: SearchTemplateParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            status_id: queryParams.status_id,
            created_by__i: REACT_APP_IS_ADMIN ? queryParams.created_by__i : user!.id,
        },
        isUndefined
    );

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.DESIGN_TEMPLATES, paramConfig, user],
        queryFn: ({ queryKey }) => DesignTemplateService.getList(queryKey[1] as SearchTemplateParam),
    });

    const handleSuccess = () => {
        closeModal();
        refetch();
    };

    const {
        handleSubmit,
        handleDelete,
        resetMutations,
        isLoading: isMutationLoading,
        deleteMutation,
    } = useDesignTemplateActions({ onSuccess: handleSuccess });

    const handleEditClick = (id: string) => {
        resetMutations();
        openEditModal(id);
    };

    const handleDeleteClick = (id: string) => {
        resetMutations();
        openDeleteModal(id);
    };

    const handleCreateClick = () => {
        resetMutations();
        openCreateModal();
    };

    const handleViewClick = (id: string) => {
        openViewModal(id);
    };

    const handleModalToggle = (show: boolean) => {
        if (!show) {
            closeModal();
            resetMutations();
        }
    };

    const handleFormSubmit = (formData: Partial<DesignTemplate>) => {
        handleSubmit(formData, isEditMode);
    };

    const handleDeleteConfirm = () => {
        if (modalState.itemId) {
            handleDelete(modalState.itemId);
        }
    };

    const onChangePage = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({
            ...queryParams,
            page: page.toString(),
        });
    };

    const isLoaderVisible = isLoading || isRefetching;
    const isDataVisible = data && !isLoaderVisible;

    return (
        <>
            <Helmet>
                <title>{t('template.multiple')}</title>
            </Helmet>

            <ContentHeader
                title={t('template.multiple')}
                contextMenu={
                    REACT_APP_IS_PARTNER
                        ? [
                              {
                                  text: t('template.add'),
                                  icon: 'PLUS',
                                  fnCallBack: { actionMenu: handleCreateClick },
                              },
                          ]
                        : []
                }
            />
            <div className="content-body">
                <div className="col-12">
                    <SearchTemplateForm partners={partners?.items ?? []} isLoading={isLoading} />
                </div>
                {isLoaderVisible && <Spinner />}
                {isDataVisible && (
                    <div className="card">
                        <DesignTemplateList
                            data={data.items}
                            partners={partners?.items ?? []}
                            handleView={handleViewClick}
                            handleEdit={handleEditClick}
                            handleDelete={handleDeleteClick}
                        />
                        <PaginationTable
                            countItem={data.pagination.count_item}
                            totalPage={data.pagination.total_page}
                            currentPage={data.pagination.current_page}
                            handlePageChange={onChangePage}
                        />
                        <ModalTemplateDetail
                            show={viewModalState.isOpen}
                            itemId={viewModalState.itemId}
                            onClose={closeViewModal}
                        />
                    </div>
                )}

                <TemplateFormModal
                    show={modalState.isOpen && !isDeleteMode}
                    itemId={modalState.itemId}
                    isLoading={isMutationLoading}
                    changeShow={handleModalToggle}
                    submitAction={handleFormSubmit}
                />

                <ModalConfirm
                    show={isDeleteMode}
                    text={t('confirm.delete')}
                    btnDisabled={deleteMutation.isPending}
                    changeShow={handleModalToggle}
                    submitAction={handleDeleteConfirm}
                />
            </div>
        </>
    );
}
