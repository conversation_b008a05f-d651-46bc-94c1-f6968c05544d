import { ApiResponse, DataListResponse } from 'types/common';
import Http from './http.class';
import PartnerInvoice, { SearchPartnerInvoice } from '../types/PartnerInvoice';
import { ItemStatus } from '../types/common/Item';
import { REACT_APP_IS_PARTNER } from '../constants/common';

const http = new Http().instance;

const PartnerInvoiceService = {
    async getList(searchParams: SearchPartnerInvoice, partnerId?: string) {
        const params = { ...searchParams };
        if (REACT_APP_IS_PARTNER && partnerId) params.partner_id__i = partnerId;
        const { data } = await http.get<ApiResponse<DataListResponse<PartnerInvoice>>>('/partner-invoice', { params });
        if (data.success) return data.data;
    },

    async getItem(id: string) {
        const { data } = await http.get<ApiResponse<PartnerInvoice>>(`/partner-invoice/${id}`);
        if (data.success) return data.data;
    },

    async updateStatus(id: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/partner-invoice/${id}/status`, {
            status_id: ItemStatus.ACTIVE,
        });
        return data;
    },

    async createPaymentIntent(amount: number, currency: string = 'aud') {
        const { data } = await http.post<ApiResponse<{ clientSecret: string }>>('/payments/create-payment-intent', {
            amount,
            currency,
        });
        return data;
    },
};

export default PartnerInvoiceService;
