import { REACT_APP_IS_PARTNER } from 'constants/common';
import User, { ChangePassword, LoginData, SearchUser, TwoFAConfigType, Verify2FA } from 'types/User';
import { ApiResponse, DataListResponse } from 'types/common';
import Http from './http.class';

const http = new Http().instance;

const UserService = {
    async login(param: LoginData) {
        const { data } = await http.post<ApiResponse<User>>('/auth/login', param);
        return data;
    },

    async logout() {
        const { data } = await http.get<ApiResponse<User>>('/auth/logout');
        return data;
    },

    async register(param: Partial<User>) {
        const { data } = await http.post<ApiResponse<User>>('/auth/register', param);
        return data;
    },

    async getProfile() {
        const { data } = await http.get<ApiResponse<User>>('/auth/profile');
        return data;
    },

    async updateProfile(param: Partial<User>) {
        const { data } = await http.patch<ApiResponse<User>>('/auth', param);
        return data;
    },

    async forgotPassword(email: string, roleId: number) {
        const { data } = await http.post<ApiResponse<{}>>('/auth/forgot-password', { email, role_id: roleId });
        return data;
    },

    async resetPassword(param: ChangePassword) {
        const { data } = await http.post<ApiResponse<{}>>('/auth/reset-password', param);
        return data;
    },

    async changePassword(param: ChangePassword) {
        const { data } = await http.post<ApiResponse<{}>>('/auth/change-password', param);
        return data;
    },

    async changeUserPassword(id: string, param: ChangePassword) {
        const { data } = await http.patch<ApiResponse<{}>>(`/user/${id}/change-pass`, param);
        return data;
    },

    async getItem(id: string) {
        const { data } = await http.get<ApiResponse<User>>(`/user/${id}`);
        return data;
    },

    async getList(params: SearchUser, partnerId?: string) {
        if (REACT_APP_IS_PARTNER && partnerId) params['shop.partner_id'] = partnerId;
        const { data } = await http.get<ApiResponse<DataListResponse<User>>>('/user', { params });
        if (data.success) return data.data;
    },

    async update(param: Partial<User>, id: string = '') {
        const { data } = id
            ? await http.patch<ApiResponse<User>>(`/user/${id}`, param)
            : await http.post<ApiResponse<User>>('/user', param);
        return data;
    },

    async deleteItem(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/user/${id}`);
        return data;
    },

    async getGroupIds(id: string) {
        const { data } = await http.get<ApiResponse<string[]>>(`/user/${id}/groups`);
        if (data.success) return data.data;
        return [];
    },

    async updateGroups(id: string, ids: string[]) {
        const { data } = await http.put<ApiResponse<string[]>>(`/user/${id}/groups`, { group_ids: ids });
        return data;
    },

    async getActionIds(id: string) {
        const { data } = await http.get<ApiResponse<string[]>>(`/user/${id}/actions`);
        if (data.success) return data.data;
        return [];
    },

    async getConfig2FA() {
        const { data } = await http.get<
            ApiResponse<{
                two_factor_secret: string;
                two_factor_qr_code: string;
            }>
        >(`/auth/2fa`);
        if (data.success) return data.data;
        return null;
    },

    async setTwoFAConfig(body: TwoFAConfigType) {
        const { data } = await http.post<ApiResponse<{}>>(`/auth/change-2fa`, body);
        return data;
    },

    async verify2FA(body: Verify2FA) {
        const { data } = await http.post<ApiResponse<{}>>(`/auth/verify-2fa`, body);
        return data;
    },
};

export default UserService;
