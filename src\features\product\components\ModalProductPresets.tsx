import classNames from 'classnames';
import { useEffect, useLayoutEffect, useState } from 'react';
import { Save } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { ProductChild } from 'types/Product';
import { showToast, toggleModalOpen } from 'utils/common';
import { isEmpty } from 'lodash';
import ProductService from '../../../services/ProductService';
import FormatNumber from '../../../components/partials/FormatNumber';

interface IProps {
    show: boolean;
    productId: string;
    productName: string;
    productChilds: ProductChild[];
    changeShow: (s: boolean) => void;
}

export default function ModalProductPresets({
    show,
    productId,
    productName,
    productChilds,
    changeShow,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [items, setItems] = useState<ProductChild[]>([]);

    useEffect(() => setItems(productChilds), [productChilds]);

    const setPreset = (value: string, index: number) => {
        const it = [...items];
        it[index].preset = value;
        setItems(it);
    };

    const setPrice = (value: number, index: number) => {
        const it = [...items];
        it[index].partner_price = value;
        setItems(it);
    };

    const updatePreset = async (index: number) => {
        const preset = items[index].preset.trim();
        const price = items[index].partner_price ?? 0;
        if (isEmpty(preset)) {
            showToast(false, [t('design.presetRequired')]);
            return;
        }
        if (preset.length > 1) {
            showToast(false, [t('design.presetRequired')]);
            return;
        }
        if (price < 0) {
            showToast(false, [t('partner.invalidPrice')]);
            return;
        }
        const response1 = await ProductService.updatePreset(preset, items[index].id);
        const response2 = await ProductService.updatePartnerPrice(price, items[index].id);
        if (!response1.success || !response2.success) {
            showToast(false, [t('error.common')]);
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{productName}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <div className="modal-body">
                            <div className="table-responsive">
                                <table className="table">
                                    <thead>
                                        <tr>
                                            <th className="text-center">{t('no.')}</th>
                                            <th>{t('name')}</th>
                                            <th>{t('sku')}</th>
                                            <th className="text-center">{t('design.preset')}</th>
                                            <th className="text-center">{t('partner.price')}</th>
                                            <th className="thAction3"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {productChilds.map((item: ProductChild, index: number) => (
                                            <tr key={item.id}>
                                                <td className="text-center">{index + 1}</td>
                                                <td>{item.title}</td>
                                                <td>{item.sku}</td>
                                                <td className="text-center">
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        value={item.preset}
                                                        onChange={(e) => setPreset(e.target.value, index)}
                                                    />
                                                </td>
                                                <td className="text-center">
                                                    <FormatNumber
                                                        value={item.partner_price ?? 0}
                                                        isInput={true}
                                                        onValueChange={(value: number) => setPrice(value, index)}
                                                    />
                                                </td>
                                                <td className="text-center">
                                                    <button
                                                        type="button"
                                                        title={t('update')}
                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                        onClick={() => updatePreset(index)}
                                                    >
                                                        <Save size={14} />
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
