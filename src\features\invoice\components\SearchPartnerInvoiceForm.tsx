import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { ItemStatusNames } from 'types/common/Item';
import { convertConstantToSelectOptions, convertObjectToSelectOptions, convertSelectToStringKey } from 'utils/common';
import User, { displayUserName } from '../../../types/User';
import { SearchField } from '../../../types/common/Search';
import { filter } from 'lodash';
import { REACT_APP_IS_ADMIN } from '../../../constants/common';

interface IProps {
    isLoading: boolean;
    partners: User[];
}

export default function SearchPartnerInvoiceForm({ isLoading, partners }: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        { name: 'search_text', type: 'text', label: t('keywords'), wrapClassName: 'col-md-4 col-12', show: true },
        {
            name: 'partner_id__i',
            type: 'select',
            label: t('partner.single'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(
                    partners.map((item) => ({ id: item.id!, name: displayUserName(item) })),
                    true
                ),
            },
            show: REACT_APP_IS_ADMIN,
        },
        {
            name: 'status_id__i',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(ItemStatusNames, t, true)),
            },
            show: true,
        },
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
