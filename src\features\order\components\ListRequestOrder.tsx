import { useTranslation } from 'react-i18next';
import { Paging } from 'types/common';
import RequestOrder from 'types/RequestOrder';
import { genTableIndex } from 'utils/common';

interface IProps {
    items: RequestOrder[];
    paging: Paging;
}

export default function ListRequestOrder({ items, paging }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th>{t('name')}</th>
                        <th>{t('email')}</th>
                        <th>{t('phoneNumber')}</th>
                        <th>{t('website')}</th>
                        <th>{t('requestOrder.itemPerOrder')}</th>
                        <th>{t('comment')}</th>
                        <th>{t('ipAddress')}</th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: RequestOrder, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{genTableIndex(index, paging.limit, paging.current_page)}</td>
                            <td>{item.name}</td>
                            <td>
                                <a href={`mailto:${item.email}`} target="_blank">
                                    {item.email}
                                </a>
                            </td>
                            <td>{item.phone}</td>
                            <td>{item.website}</td>
                            <td>{item.item_per_month}</td>
                            <td>{item.comment}</td>
                            <td>{item.ip_address}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
