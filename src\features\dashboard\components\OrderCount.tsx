import { useQuery } from '@tanstack/react-query';
import ResetButton from 'components/partials/ResetButton';
import Spinner from 'components/partials/Spinner';
import UpdateButton from 'components/partials/UpdateButton';
import { QUERY_KEY } from 'constants/common';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import DashboardService from 'services/DashboardService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import { OrderStatusNames } from 'types/Order';
import User, { UserRole } from 'types/User';
import { showToast } from 'utils/common';
import { validateDateRange } from 'utils/date';
import { ItemStatus } from '../../../types/common/Item';

function getPrimaryColumn() {
    return {
        [UserRole.ADMIN]: 'partner.single',
        [UserRole.PARTNER]: 'shop.single',
        [UserRole.MANUFACTURER]: 'order.status',
    };
}

type PrimaryColumnKeys = keyof ReturnType<typeof getPrimaryColumn>;

interface IProps {
    user: User;
}

interface OrderCountQueryParams {
    startDate?: string;
    endDate?: string;
}

interface OrderCountProps {
    name: string;
    orderCount: number;
}

export default function OrderCount({ user }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [searchParams, setSearchParams] = useState<OrderCountQueryParams>({});

    const fetchOrders = async (params: OrderCountQueryParams): Promise<OrderCountProps[]> => {
        const { startDate, endDate } = params;
        let orders: OrderCountProps[] = [];

        // Admin
        if (user.role_id === UserRole.ADMIN) {
            const partners = await UserService.getList({
                status_id__i: ItemStatus.ACTIVE.toString(),
                role_id__i: UserRole.PARTNER.toString(),
            });
            if (partners) {
                const partnerOrders = await Promise.all(
                    partners.items.map(async (partner: User) => {
                        const orderCount = await DashboardService.countOrder({
                            'shop.partner_id': partner.id,
                            order_created_at__ge: startDate ? new Date(startDate).toISOString() : undefined,
                            order_created_at__le: endDate ? new Date(endDate).toISOString() : undefined,
                        });
                        return { name: `${partner.first_name} ${partner.last_name}`, orderCount: orderCount.data };
                    })
                );
                orders = orders.concat(partnerOrders);
            }
        }

        // Partner
        if (user.role_id === UserRole.PARTNER) {
            const shops = await ShopService.getList({ partner_id__i: user.id });
            if (shops) {
                const shopOrders = await Promise.all(
                    shops.items.map(async (shop) => {
                        const orderCount = await DashboardService.countOrder({
                            shop_id: shop.id,
                            order_created_at__ge: startDate,
                            order_created_at__le: endDate,
                        });
                        return { name: shop.name, orderCount: orderCount.data };
                    })
                );
                orders = orders.concat(shopOrders);
            }
        }

        // Manufacturer
        if (user.role_id === UserRole.MANUFACTURER) {
            const statusOrders = await Promise.all(
                OrderStatusNames.map(async (status) => {
                    const orderCount = await DashboardService.countOrder({
                        'warehouse.manufacturer_id': user.id,
                        status_id__i: status.id,
                        order_created_at__ge: startDate,
                        order_created_at__le: endDate,
                    });
                    return { name: t(status.name), orderCount: orderCount.data };
                })
            );
            orders = orders.concat(statusOrders);
        }

        return orders;
    };

    const {
        data: orderData,
        isLoading,
        isRefetching,
    } = useQuery({
        queryKey: [QUERY_KEY.DASHBOARD_ORDER_COUNT, searchParams],
        queryFn: ({ queryKey }) => fetchOrders(queryKey[1] as OrderCountQueryParams),
    });

    const handleSearch = () => {
        const errorMessage = validateDateRange(startDate, endDate);
        if (errorMessage) {
            showToast(false, [errorMessage]);
            return;
        }
        setSearchParams({ startDate, endDate });
    };

    const handleReset = () => {
        setStartDate('');
        setEndDate('');
        setSearchParams({});
    };

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{t('partnerOrderQuantity')}</h4>
            </div>
            <div className="card-body">
                <div className="row">
                    <div className="col-12 col-md-4 mb-1">
                        <label className="form-label">{t('startDate')}</label>
                        <input
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                            className="form-control"
                        />
                    </div>
                    <div className="col-12 col-md-4 mb-1">
                        <label className="form-label">{t('endDate')}</label>
                        <input
                            type="date"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                            className="form-control"
                        />
                    </div>
                    <div className="col-md-4 col-12" style={{ paddingTop: '23px' }}>
                        <UpdateButton
                            btnText={t('search')}
                            isLoading={isLoading}
                            hasDivWrap={false}
                            btnClass={['me-1']}
                            onSubmit={handleSearch}
                        />
                        <ResetButton btnText={t('reset')} isLoading={isLoading} handleReset={handleReset} />
                    </div>
                </div>
            </div>
            {(isLoading || isRefetching) && <Spinner />}
            {orderData && !isLoading && !isRefetching && (
                <div className="table-responsive">
                    <table className="table">
                        <thead>
                            <tr>
                                <th className="text-center">{t('no.')}</th>
                                <th>{t(`${getPrimaryColumn()[user.role_id as PrimaryColumnKeys]}`)}</th>
                                <th className="text-center">
                                    {t(user.role_id === UserRole.MANUFACTURER ? 'quantity' : 'order.count')}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {orderData.map((order, index) => (
                                <tr key={index}>
                                    <td className="text-center">{index + 1}</td>
                                    <td>
                                        {user.role_id === UserRole.MANUFACTURER
                                            ? t(`constants.${order.name}`)
                                            : order.name}
                                    </td>
                                    <td className="text-center">{order.orderCount}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
}
