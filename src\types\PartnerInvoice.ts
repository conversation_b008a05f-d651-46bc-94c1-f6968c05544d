import { BaseModel, BaseSearch } from './common';
import { ItemStatus } from './common/Item';
import Order from './Order';

export interface SearchPartnerInvoice extends BaseSearch {
    partner_id__i?: string[] | string;
    status_id__i?: number[] | string;
}

export type SearchPartnerInvoiceParam = {
    [key in keyof SearchPartnerInvoice]: string;
};

export default interface PartnerInvoice extends BaseModel {
    barcode: string;
    partner_id: string;
    order_id: string;
    partner_percent: number;
    partner_cost: number;
    product_cost: number;
    design_cost: number;
    ship_cost: number;
    sum_cost: number;
    products: string;
    status_id: ItemStatus;
    order?: Order;
}
