import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import ListWeight from 'features/weight/components/ListWeight';
import ModalWeightUpdate from 'features/weight/components/ModalWeightUpdate';
import { find } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ProductWeightService from 'services/ProductWeightService';
import ProductWeight from 'types/ProductWeight';
import { showToast } from 'utils/common';

export default function WeightList() {
    const { t } = useTranslation();
    const [itemId, setItemId] = useState('');
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);

    const { data, isLoading, isRefetching, refetch } = useQuery({
        queryKey: [QUERY_KEY.PRODUCT_WEIGHTS],
        queryFn: () => ProductWeightService.getList(),
        placeholderData: keepPreviousData,
    });

    const updateMutation = useMutation({
        mutationFn: (param: ProductWeight) => ProductWeightService.update(param, param.id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowUpdate(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => ProductWeightService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const updateItem = (body: ProductWeight) => {
        if (!body.desc) delete body.desc;
        updateMutation.mutate(body);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const actionMenu = () => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId('');
    };

    const handleEdit = (id: string) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    return (
        <>
            <Helmet>
                <title>{t(`productWeight.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`productWeight.multiple`)}
                contextMenu={[
                    {
                        text: t('productWeight.add'),
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && (
                        <>
                            <div className="card">
                                <ListWeight items={data} handleEdit={handleEdit} handleDelete={handleDelete} />
                            </div>
                            <ModalWeightUpdate
                                show={showUpdate}
                                productWeight={find(data, { id: itemId })}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
