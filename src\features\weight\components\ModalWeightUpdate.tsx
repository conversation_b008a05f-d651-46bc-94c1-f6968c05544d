import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ProductWeight, { PlacementSize } from 'types/ProductWeight';
import { selectItem, showToast, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';
import { Placement, PlacementNames } from '../../../types/Product';
import { findIndex, isEmpty } from 'lodash';
import { PlusCircle, Trash2 } from 'react-feather';

interface IProps {
    show: boolean;
    productWeight: ProductWeight | undefined;
    isLoading: boolean;
    changeShow: (s: boolean) => void;
    submitAction: (data: ProductWeight) => void;
}

export default function ModalWeightUpdate({
    show,
    productWeight,
    isLoading,
    changeShow,
    submitAction,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [sizes, setSizes] = useState<PlacementSize[]>([]);

    const schema = yup
        .object({
            tag: yup.string().required(t('error.required')).trim(),
            weight: yup
                .number()
                .typeError(t('error.number'))
                .required(t('error.required'))
                .moreThan(0, t('error.greaterThan_0')),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<ProductWeight>({
        resolver: yupResolver(schema),
    });

    useEffect(() => {
        if (productWeight && show) {
            reset({
                id: productWeight.id,
                tag: productWeight.tag,
                desc: productWeight.desc,
                weight: productWeight.weight,
            });
            setSizes(JSON.parse(productWeight.desc ?? '[]'));
        } else {
            reset({
                tag: '',
                desc: '',
                weight: 0,
            });
        }
    }, [productWeight, show]);

    const onChangeSize = (index: number, label: string, value: string) => {
        if (isNaN(Number(value))) {
            return;
        }
        const items = [...sizes];
        //@ts-ignore
        items[index][label] = +value;
        setSizes(items);
    };

    const onDelete = (index: number) => {
        const items = [...sizes];
        items.splice(index, 1);
        setSizes(items);
    };

    const addSize = () => {
        const items = [...sizes];
        items.push({ id: Placement.FRONT, width: 0, height: 0 });
        setSizes(items);
    };

    const onSubmit = (data: ProductWeight) => {
        if (isEmpty(sizes)) {
            showToast(false, [t('productWeight.sizeError')]);
            return;
        }
        const index = findIndex(sizes, (s) => s.width === 0 || s.height === 0);
        if (index >= 0) {
            showToast(false, [t('productWeight.sizeError')]);
            return;
        }
        data.desc = JSON.stringify(sizes);
        submitAction(data);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {t(productWeight ? 'productWeight.edit' : 'productWeight.add')}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('name')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('tag')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.tag?.message),
                                            })}
                                        />
                                        <span className="error">{errors.tag?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-6 mb-1">
                                        <label className="form-label">
                                            {t('productWeight.weight')} <span className="error">*</span>
                                        </label>
                                        <input
                                            {...register('weight')}
                                            type="text"
                                            className={classNames('form-control', {
                                                'is-invalid': Boolean(errors.weight?.message),
                                            })}
                                        />
                                        <span className="error">{errors.weight?.message}</span>
                                    </div>
                                </div>
                                <div className="table-responsive">
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th className="text-center">{t('design.placement')}</th>
                                                <th className="text-center">{t('width')} (cm)</th>
                                                <th className="text-center">{t('height')} (cm)</th>
                                                <th className="text-center">
                                                    <button
                                                        type="button"
                                                        title={t('add')}
                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                        onClick={addSize}
                                                    >
                                                        <PlusCircle size={14} />
                                                    </button>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {sizes.map((size, index) => (
                                                <tr key={index}>
                                                    <td className="text-center">
                                                        <select
                                                            className="form-select"
                                                            value={size.id}
                                                            onChange={(e) => onChangeSize(index, 'id', e.target.value)}
                                                        >
                                                            {selectItem(PlacementNames, t, true)}
                                                        </select>
                                                    </td>
                                                    <td className="text-center">
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            value={size.width}
                                                            onChange={(e) =>
                                                                onChangeSize(index, 'width', e.target.value)
                                                            }
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <input
                                                            type="text"
                                                            className="form-control"
                                                            value={size.height}
                                                            onChange={(e) =>
                                                                onChangeSize(index, 'height', e.target.value)
                                                            }
                                                        />
                                                    </td>
                                                    <td className="text-center">
                                                        <button
                                                            type="button"
                                                            title={t('delete')}
                                                            className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                            onClick={() => onDelete(index)}
                                                        >
                                                            <Trash2 size={14} />
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton
                                    btnText={t(productWeight ? 'update' : 'add')}
                                    isLoading={isLoading}
                                    hasDivWrap={false}
                                />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
