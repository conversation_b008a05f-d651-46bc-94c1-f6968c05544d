import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import DesignTemplate from 'types/DesignTemplate';
import DesignTemplateService from 'services/DesignTemplateService';
import { showToast } from 'utils/common';

interface UseDesignTemplateActionsProps {
    onSuccess?: () => void;
    onError?: () => void;
}

export const useDesignTemplateActions = ({ onSuccess, onError }: UseDesignTemplateActionsProps = {}) => {
    const { t } = useTranslation();

    const handleSettled = (data: unknown, error: unknown) => {
        if (error) {
            showToast(false, [t('error.common')]);
            onError?.();
            return;
        }

        if (data && typeof data === 'object' && 'success' in data && 'messages' in data) {
            const result = data as { success: boolean; messages: string[] };
            showToast(result.success, result.messages);
            if (result.success) {
                onSuccess?.();
            }
        }
    };

    const createMutation = useMutation({
        mutationFn: (param: DesignTemplate) => DesignTemplateService.create(param),
        onSettled: handleSettled,
    });

    const updateMutation = useMutation({
        mutationFn: (param: Partial<DesignTemplate>) => DesignTemplateService.update(param, param.id),
        onSettled: handleSettled,
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => DesignTemplateService.delete(id),
        onSettled: handleSettled,
    });

    const handleSubmit = (data: Partial<DesignTemplate>, isEdit: boolean) => {
        if (isEdit) {
            updateMutation.mutate(data);
        } else {
            createMutation.mutate(data as DesignTemplate);
        }
    };

    const handleDelete = (id: string) => {
        if (id) {
            deleteMutation.mutate(id);
        }
    };

    const resetMutations = () => {
        createMutation.reset();
        updateMutation.reset();
        deleteMutation.reset();
    };

    return {
        createMutation,
        updateMutation,
        deleteMutation,
        handleSubmit,
        handleDelete,
        resetMutations,
        isLoading: createMutation.isPending || updateMutation.isPending || deleteMutation.isPending,
    };
};
