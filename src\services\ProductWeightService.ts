import { ApiResponse } from 'types/common';
import Http from './http.class';
import ProductWeight from 'types/ProductWeight';

const http = new Http().instance;

const ProductWeightService = {
    async getList() {
        const { data } = await http.get<ApiResponse<ProductWeight[]>>('/product-weight');
        if (data.success) return data.data;
    },

    async update(param: ProductWeight, id: string = '') {
        delete param.id;
        const { data } = id
            ? await http.put<ApiResponse<ProductWeight>>(`/product-weight/${id}`, param)
            : await http.post<ApiResponse<ProductWeight>>('/product-weight', param);
        return data;
    },

    async deleteItem(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/product-weight/${id}`);
        return data;
    },
};

export default ProductWeightService;
