import { useQuery } from '@tanstack/react-query';
import classNames from 'classnames';
import { optionModelDefault, PAGINATION, QUERY_KEY } from 'constants/common';
import { useOutsideClick } from 'hooks/useOutsideClick';
import { ceil, find, includes, isEmpty } from 'lodash';
import { Fragment, useEffect, useState } from 'react';
import { Disc, Search } from 'react-feather';
import { useTranslation } from 'react-i18next';
import Select, { SingleValue } from 'react-select';
import ProductService from 'services/ProductService';
import { ItemParamModel, SelectOptionModel } from 'types/common/Item';
import Product, { getProductCode, ProductChild, ProductChoose, ProductTypeNames, SearchProduct } from 'types/Product';
import Shop from 'types/Shop';
import { convertObjectToSelectOptions, showToast } from 'utils/common';
import FormatNumber from './FormatNumber';
import PaginationTable from './PaginationTable';
import Spinner from './Spinner';

interface IProps {
    shops: Shop[];
    vendor: string;
    productChildIds: string[];
    onChoose: (ids: string[], products: ProductChoose[]) => void;
}

export default function ChooseProducts({ shops, vendor, productChildIds, onChoose }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [searchParams, setSearchParams] = useState<SearchProduct>({
        status__i: 'active',
        vendor,
        page: 1,
        limit: PAGINATION.limit,
    });
    const [shopValue, setShopValue] = useState<SelectOptionModel>(optionModelDefault);
    const [typeValue, setTypeValue] = useState<SelectOptionModel>(optionModelDefault);
    const [domain, setDomain] = useState('');
    const [ids, setIds] = useState<string[]>([]);
    const [show, setShow] = useState(false);

    const ref = useOutsideClick(() => setShow(false));

    useEffect(() => {
        if (!isEmpty(shops) && vendor) {
            setShopValue({ value: shops[0].id!, label: shops[0].name });
            setSearchParams({ ...searchParams, shop_id: shops[0].id!, vendor });
            setDomain(shops[0].domain);
        }
    }, [shops, vendor]);

    useEffect(() => setIds(productChildIds), [productChildIds]);

    const { data, isLoading } = useQuery({
        queryKey: [QUERY_KEY.PRODUCTS, searchParams],
        queryFn: ({ queryKey }) => ProductService.getList(queryKey[1] as SearchProduct, false, []),
        enabled: !!searchParams.shop_id && !!searchParams.vendor,
    });

    const onChangeShop = (option: SingleValue<SelectOptionModel>) => {
        setShopValue(option ?? optionModelDefault);
        setSearchParams({ ...searchParams, shop_id: option?.value ?? undefined });
        if (option?.value) {
            const item = find(shops, { id: option.value });
            if (item) setDomain(item.domain);
        }
    };

    const onChangeType = (option: SingleValue<SelectOptionModel>) => {
        setTypeValue(option ?? optionModelDefault);
        setSearchParams({ ...searchParams, product_type__i: option?.value ?? undefined });
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setSearchParams({ ...searchParams, page });
    };

    const toggleItem = (id: string, checked: boolean) => {
        const items = [...ids];
        if (checked) {
            if (!includes(items, id)) {
                items.push(id);
                setIds(items);
            }
        } else {
            const index = items.indexOf(id);
            if (index > -1) {
                items.splice(index, 1);
                setIds(items);
            }
        }
    };

    const handleChoose = () => {
        if (isEmpty(ids)) showToast(false, [t('error.choose')]);
        else {
            let child: ProductChild, product: Product;
            const products: ProductChoose[] = ids.map((id) => {
                (data?.items ?? []).forEach((p) => {
                    p.children.forEach((c) => {
                        if (c.id === id) {
                            child = c;
                            product = p;
                            return false;
                        }
                        if (child) return false;
                    });
                });
                return {
                    id,
                    code: getProductCode(child?.sku ?? ''),
                    name: product?.title ?? '',
                    title: child?.title ?? '',
                    sku: child?.sku ?? '',
                    image: child?.image ?? '',
                    quantity: 1,
                };
            });
            onChoose(ids, products);
            setShow(false);
        }
    };

    return (
        <div
            className={classNames('col-12 col-sm-12 mb-1 position-relative', { 'd-none': isEmpty(shops) || !vendor })}
            ref={ref}
        >
            <Search size={20} className="position-absolute" style={{ top: '9px', left: '25px' }} />
            <input
                type="text"
                className="form-control ps-3"
                onChange={(e) => setSearchParams({ ...searchParams, search_text: e.target.value.trim() })}
                onFocus={() => setShow(true)}
            />
            <div className={classNames('products-dropdown border-info', { 'd-none': !show })}>
                <div className="modal-content">
                    <div className="modal-body">
                        <div className="row">
                            <div className="col-12 col-sm-6 mb-1">
                                <label className="form-label">{t('shop.single')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(shops as ItemParamModel[], true)}
                                    onChange={onChangeShop}
                                    value={shopValue}
                                />
                            </div>
                            <div className="col-12 col-sm-6 mb-1">
                                <label className="form-label">{t('type')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(ProductTypeNames, true)}
                                    onChange={onChangeType}
                                    value={typeValue}
                                />
                            </div>
                            {isLoading && <Spinner />}
                            {data && !isLoading && (
                                <div className="card mb-0">
                                    <div className="table-responsive">
                                        <table className="table">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>{t('name')}</th>
                                                    <th>{t('sku')}</th>
                                                    <th>{t('type')}</th>
                                                    <th className="text-end">{t('price')}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {data.items.map((item: Product) => (
                                                    <Fragment key={item.id}>
                                                        <tr>
                                                            <td className="text-center">
                                                                {item.children.length === 1 && (
                                                                    <input
                                                                        className="form-check-input"
                                                                        type="checkbox"
                                                                        id={`cb_${item.children[0].id}`}
                                                                        value={item.children[0].id}
                                                                        disabled={!item.children[0].sku}
                                                                        checked={includes(ids, item.children[0].id)}
                                                                        onChange={(e) =>
                                                                            toggleItem(e.target.value, e.target.checked)
                                                                        }
                                                                    />
                                                                )}
                                                            </td>
                                                            <td>
                                                                <a
                                                                    href={`https://${domain}/products/${item.handle}`}
                                                                    target="_blank"
                                                                >
                                                                    {item.children.length === 1 ? (
                                                                        <>
                                                                            {item.children[0].image ? (
                                                                                <img
                                                                                    src={item.children[0].image}
                                                                                    className="me-75"
                                                                                    height="20"
                                                                                    width="20"
                                                                                    alt=""
                                                                                />
                                                                            ) : (
                                                                                <Disc size={14} className="me-75" />
                                                                            )}
                                                                            <span>
                                                                                {item.title} - {item.children[0].title}
                                                                            </span>
                                                                        </>
                                                                    ) : (
                                                                        item.title
                                                                    )}
                                                                </a>
                                                            </td>
                                                            <td>
                                                                {item.children.length === 1 ? item.children[0].sku : ''}
                                                            </td>
                                                            <td>{item.product_type}</td>
                                                            <td className="text-end">
                                                                {item.children.length === 1 && (
                                                                    <FormatNumber
                                                                        value={ceil(+item.children[0].price, 2)}
                                                                        isInput={false}
                                                                        renderText={(value) => value}
                                                                    />
                                                                )}
                                                            </td>
                                                        </tr>
                                                        {item.children.length > 1 &&
                                                            item.children.map((child) => (
                                                                <tr key={child.id}>
                                                                    <td className="text-center">
                                                                        <input
                                                                            className="form-check-input"
                                                                            type="checkbox"
                                                                            id={`cb_${child.id}`}
                                                                            value={child.id}
                                                                            disabled={!child.sku}
                                                                            checked={includes(ids, child.id)}
                                                                            onChange={(e) =>
                                                                                toggleItem(
                                                                                    e.target.value,
                                                                                    e.target.checked
                                                                                )
                                                                            }
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        {child.image ? (
                                                                            <img
                                                                                src={child.image}
                                                                                className="me-75"
                                                                                height="20"
                                                                                width="20"
                                                                                alt=""
                                                                            />
                                                                        ) : (
                                                                            <Disc size={14} className="me-75" />
                                                                        )}
                                                                        <span>{child.title}</span>
                                                                    </td>
                                                                    <td>{child.sku}</td>
                                                                    <td></td>
                                                                    <td className="text-end">
                                                                        <FormatNumber
                                                                            value={ceil(+child.price, 2)}
                                                                            isInput={false}
                                                                            renderText={(value) => value}
                                                                        />
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                    </Fragment>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                    <PaginationTable
                                        countItem={data.pagination.count_item}
                                        totalPage={data.pagination.total_page}
                                        currentPage={data.pagination.current_page}
                                        handlePageChange={handlePageChange}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="modal-footer">
                        <button
                            type="button"
                            className="btn btn-primary waves-effect waves-float waves-light"
                            onClick={handleChoose}
                        >
                            {t('choose')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}
