import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import DesignTemplate from 'types/DesignTemplate';
import { ItemStatus } from 'types/common/Item';
import { Edit, Eye, Trash } from 'react-feather';
import { FORMAT_DATE, formatDateTime } from 'utils/date';
import User from '../../../types/User';
import { getFieldInArrayObject } from '../../../utils/common';
import { REACT_APP_IS_ADMIN } from '../../../constants/common';

interface DesignTemplateListProps {
    data: DesignTemplate[];
    partners: User[];
    handleView: (id: string) => void;
    handleEdit: (id: string) => void;
    handleDelete: (id: string) => void;
}

const ActionButtons = memo(
    ({
        item,
        onView,
        onEdit,
        onDelete,
    }: {
        item: DesignTemplate;
        onView: (id: string) => void;
        onEdit: (id: string) => void;
        onDelete: (id: string) => void;
    }) => {
        const { t } = useTranslation();

        if (!item.id) return null;

        return (
            <div className="d-flex gap-2">
                <button
                    className="btn btn-link text-info p-0"
                    onClick={() => onView(item.id!)}
                    title={t('view')}
                    type="button"
                >
                    <Eye size={14} />
                </button>
                <button
                    className="btn btn-link text-info p-0"
                    onClick={() => onEdit(item.id!)}
                    title={t('edit')}
                    type="button"
                >
                    <Edit size={14} />
                </button>
                <button
                    className="btn btn-link text-danger p-0"
                    onClick={() => onDelete(item.id!)}
                    title={t('delete')}
                    type="button"
                >
                    <Trash size={14} />
                </button>
            </div>
        );
    }
);

ActionButtons.displayName = 'ActionButtons';

const StatusBadge = memo(({ statusId }: { statusId: number }) => {
    const { t } = useTranslation();

    const getStatusConfig = (status: number) => {
        switch (status) {
            case ItemStatus.PENDING:
                return { className: 'bg-warning', text: t('constants.pending') };
            case ItemStatus.ACTIVE:
                return { className: 'bg-success', text: t('constants.active') };
            case ItemStatus.INACTIVE:
                return { className: 'bg-danger', text: t('constants.inactive') };
            default:
                return { className: 'bg-secondary', text: t('unknown') };
        }
    };

    const { className, text } = getStatusConfig(statusId);

    return <span className={`badge ${className}`}>{text}</span>;
});

StatusBadge.displayName = 'StatusBadge';

const DesignTemplateList = memo(({ data, partners, handleView, handleEdit, handleDelete }: DesignTemplateListProps) => {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>{t('no.')}</th>
                        <th>{t('name')}</th>
                        {REACT_APP_IS_ADMIN && <th>{t('partner.single')}</th>}
                        <th>{t('status')}</th>
                        <th>{t('createdTime')}</th>
                        <th>{t('actions')}</th>
                    </tr>
                </thead>
                <tbody>
                    {data.map((item, index) => (
                        <tr key={item.id}>
                            <td>{index + 1}</td>
                            <td>{item.name}</td>
                            {REACT_APP_IS_ADMIN && <td>{getFieldInArrayObject(partners, item.created_by!)}</td>}
                            <td>
                                <StatusBadge statusId={item.status_id} />
                            </td>
                            <td>{item.created_at && formatDateTime(item.created_at, FORMAT_DATE.SHOW_DATE_MINUTE)}</td>
                            <td>
                                <ActionButtons
                                    item={item}
                                    onView={handleView}
                                    onEdit={handleEdit}
                                    onDelete={handleDelete}
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
});

DesignTemplateList.displayName = 'DesignTemplateList';

export default DesignTemplateList;
