import classNames from 'classnames';
import includes from 'lodash/includes';
import { useState } from 'react';
import { Printer } from 'react-feather';
import { useTranslation } from 'react-i18next';
import Barcode from '../../../components/partials/Barcode';
import FileService from '../../../services/FileService';
import TransferGoodService from '../../../services/TransferGoodService';
import { Barcode as IBarcode } from '../../../types/common/Item';
import { CustomDesign } from '../../../types/Product';
import { showToast } from '../../../utils/common';

interface IProps {
    code: IBarcode;
    customDesign: CustomDesign;
    ggFolderId?: string;
}

export default function ProductOwnerDesignPlacement({ code, customDesign, ggFolderId }: Readonly<IProps>) {
    const [loading, setLoading] = useState(false);
    const { t } = useTranslation();

    const onPrint = async () => {
        setLoading(true);
        const htmlPrint = TransferGoodService.generateProductCard('', '', '', '', '', code.code, code.image, true);
        const response = await FileService.exportPdf(htmlPrint, false);
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
        setLoading(false);
    };

    return (
        <>
            <div className="alert alert-dark rounded-0">
                <div className="alert-body d-flex justify-content-between flex-md-row flex-column">
                    <div className="text-uppercase">{t(`constants.${customDesign.placement}`)}</div>
                </div>
            </div>
            <div className="d-flex justify-content-between flex-md-row flex-column">
                <div className="d-flex">
                    {customDesign.images.map((design, index) => (
                        <div className="demo-inline-spacing ps-1" key={`${design.url}-${index}`}>
                            <div className="m-0">
                                <img className="order-image-position border-1 border-dark" src={design.url} alt="" />
                                <p className="text-center">
                                    {t('size')}: {design.width}cm x {design.height}cm
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
                <div>
                    <p>
                        <strong>{t('design.sizeAndPosition')}:</strong>
                        <Printer
                            size={14}
                            className={classNames(`ms-2`, {
                                'cursor-pointer': !loading,
                                'pointer-events-none': loading,
                            })}
                            onClick={onPrint}
                        />
                    </p>
                    <p>
                        {t('location.single')}:{' '}
                        {includes(['front', 'back'], customDesign.placement) && `${t('centre')} `}
                        {t(`constants.${customDesign.placement}`)}
                    </p>
                    <p>
                        <Barcode image={code.image} code={code.code} />
                    </p>
                </div>
                <div />
                <div />
            </div>
        </>
    );
}
