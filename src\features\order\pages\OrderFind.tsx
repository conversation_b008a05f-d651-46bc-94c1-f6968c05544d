import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import { QUERY_KEY, REACT_APP_IS_MANUFACTURER } from 'constants/common';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import OrderService from 'services/OrderService';
import ProductService from 'services/ProductService';
import ShopService from 'services/ShopService';
import WarehouseService from 'services/WarehouseService';
import { ItemStatus } from 'types/common/Item';
import { showToast } from 'utils/common';
import UserService from '../../../services/UserService';
import { useAuthStore } from '../../../stores/authStore';
import { SearchUser, UserRole } from '../../../types/User';
import { SearchWarehouse } from '../../../types/Warehouse';
import ScanZone from '../../transfer/components/ScanZone';
import ModalUpdateWarehouse from '../components/ModalUpdateWarehouse';
import OrderInfo from '../components/OrderInfo';
import OrderProducts from '../components/OrderProducts';

export default function OrderFind() {
    const [orderCode, setOrderCode] = useState('');
    const [show, setShow] = useState(false);
    const user = useAuthStore((state) => state.user);
    const { t } = useTranslation();

    const { data: orderData, refetch } = useQuery({
        enabled: !!user && !!orderCode,
        queryKey: [orderCode],
        queryFn: () => {
            const encodedOrderCode = encodeURIComponent(orderCode);
            return OrderService.getByCode(encodedOrderCode);
        },
        placeholderData: keepPreviousData,
    });

    useEffect(() => {
        if (orderData && !orderData.success) {
            showToast(false, [t('error.common')]);
        }
    }, [orderData, t]);

    const order = orderData?.success ? orderData.data : undefined;

    const { data: shop } = useQuery({
        queryKey: [QUERY_KEY.SHOP, order?.shop_id],
        queryFn: ({ queryKey }) => ShopService.getItem(queryKey[1] ?? ''),
        enabled: !!order,
    });

    const { data: warehouses } = useQuery({
        enabled: !!order,
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
    });

    const { data: products } = useQuery({
        enabled: !!order,
        queryKey: [
            QUERY_KEY.CHILDREN_PRODUCTS,
            order?.products.map(
                (item) =>
                    //isEmpty(item.designed_product_child_id) ? item.product_child_id : item.designed_product_child_id
                    item.product_child_id
            ),
            order?.id,
        ],
        queryFn: ({ queryKey }) => ProductService.getArrayChildren(queryKey[1] as string[], queryKey[2] as string),
    });

    const { data: designerData } = useQuery({
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.DESIGNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
        placeholderData: keepPreviousData,
    });

    const updateMutation = useMutation({
        mutationFn: (wId: string) => OrderService.updateWarehouse(order!.id!, wId),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShow(false);
                    refetch();
                }
            }
        },
    });

    const handleUpdate = () => {
        if (!REACT_APP_IS_MANUFACTURER) {
            updateMutation.reset();
            setShow(true);
        }
    };

    return (
        <>
            <Helmet>
                <title>{t('order.find')}</title>
            </Helmet>
            <ContentHeader title={`${t('order.find')} ${order ? `${order.order_number} - ${order.json.name}` : ''}`} />
            <div className="content-body">
                <div className="col-12">
                    <ScanZone onScan={setOrderCode} hideScanArea={true} />
                </div>
            </div>
            <div className="flex-grow-1 container-p-y">
                {order && (
                    <div className="row">
                        <OrderProducts
                            order={order}
                            shop={shop}
                            products={products ?? []}
                            warehouses={warehouses ?? []}
                            onChooseWarehouse={handleUpdate}
                            refetch={refetch}
                            designers={designerData?.items ?? []}
                        />
                        <OrderInfo order={order} />
                    </div>
                )}
            </div>
            <ModalUpdateWarehouse
                show={show}
                warehouses={warehouses ?? []}
                isLoading={updateMutation.isPending}
                changeShow={(s: boolean) => setShow(s)}
                submitAction={(wId: string) => updateMutation.mutate(wId)}
            />
        </>
    );
}
