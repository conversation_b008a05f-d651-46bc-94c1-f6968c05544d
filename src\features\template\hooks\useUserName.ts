import { useQuery } from '@tanstack/react-query';
import { QUERY_KEY } from 'constants/common';
import UserService from 'services/UserService';
import { displayUserName } from 'types/User';

interface UseUserNameProps {
    userId?: string;
    enabled?: boolean;
}

export const useUserName = ({ userId, enabled = true }: UseUserNameProps) => {
    const { data: user, isLoading } = useQuery({
        queryKey: [QUERY_KEY.USER, userId],
        queryFn: ({ queryKey }) => UserService.getItem(queryKey[1] as string),
        enabled: !!userId && enabled,
        staleTime: 5 * 60 * 1000,
        select: (response) => (response?.success ? response.data : null),
    });

    const userName = user ? displayUserName(user) : undefined;

    return {
        user,
        userName,
        isLoading,
    };
};
