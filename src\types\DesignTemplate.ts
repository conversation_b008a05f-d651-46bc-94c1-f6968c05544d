import { BaseModel, BaseSearch } from './common';
import { ItemStatus } from './common/Item';

export interface SearchTemplate extends BaseSearch {
    name?: string;
    status_id?: number;
    created_by__i?: string;
}

export type SearchTemplateParam = {
    [key in keyof SearchTemplate]: string;
};

export default interface DesignTemplate extends BaseModel {
    name: string;
    status_id: ItemStatus;
    file_ids: string[];
}
