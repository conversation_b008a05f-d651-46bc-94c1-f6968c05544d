import { Navigate, useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import ContentHeader from '../../../components/partials/ContentHeader';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import { QUERY_KEY, REACT_APP_IS_PARTNER, REACT_APP_STRIPE_PUBLIC_KEY } from '../../../constants/common';
import PaymentForm from '../components/PaymentForm';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import PartnerInvoiceService from '../../../services/PartnerInvoiceService';
import Spinner from '../../../components/partials/Spinner';
import PartnerInvoiceInfo from '../components/PartnerInvoiceInfo';
import { ItemStatus } from '../../../types/common/Item';
import PartnerInvoicePrice from '../components/PartnerInvoicePrice';
import OrderService from '../../../services/OrderService';
import PartnerInvoiceProducts from '../components/PartnerInvoiceProducts';
import ProductService from '../../../services/ProductService';
import UserService from '../../../services/UserService';

const stripePromise = loadStripe(REACT_APP_STRIPE_PUBLIC_KEY);

export default function PartnerInvoiceView() {
    const { id } = useParams();
    const { t } = useTranslation();

    const {
        data: partnerInvoice,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: [QUERY_KEY.PARTNER_INVOICE, id],
        queryFn: ({ queryKey }) => PartnerInvoiceService.getItem(queryKey[1] ?? ''),
        enabled: !!id,
    });

    const { data: order, isLoading: isLoadingOrder } = useQuery({
        queryKey: [QUERY_KEY.ORDER, partnerInvoice?.order_id],
        queryFn: ({ queryKey }) => OrderService.getItem(queryKey[1] ?? ''),
        enabled: !!partnerInvoice,
    });

    const { data: products } = useQuery({
        enabled: !!order,
        queryKey: [QUERY_KEY.CHILDREN_PRODUCTS, order?.products.map((item) => item.product_child_id)],
        queryFn: ({ queryKey }) => ProductService.getArrayChildren(queryKey[1] as string[]),
    });

    const { data: partner } = useQuery({
        queryKey: [QUERY_KEY.USER, partnerInvoice?.partner_id],
        queryFn: ({ queryKey }) => UserService.getItem(queryKey[1] ?? ''),
        enabled: !!partnerInvoice,
    });

    if (isLoading || isLoadingOrder) return <Spinner />;
    if (!isLoading && !partnerInvoice) return <Navigate to="/not-found" />;

    return (
        <>
            <Helmet>
                <title>
                    {t('partnerInvoice.single')} {partnerInvoice?.barcode}
                </title>
            </Helmet>
            <ContentHeader
                title={`${t('partnerInvoice.single')} ${partnerInvoice?.barcode}`}
                breadcrumbs={[
                    {
                        text: t('partnerInvoice.multiple'),
                        to: '/partnerInvoice',
                    },
                    {
                        text: `${t('partnerInvoice.single')} ${partnerInvoice?.barcode}`,
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isLoadingOrder) && <Spinner />}
                    {!isLoading && !isLoadingOrder && partnerInvoice && order && (
                        <div className="row">
                            <div className="col-xl-9 col-md-8 col-12">
                                <div className="card">
                                    <div className="card-body position-relative">
                                        <PartnerInvoiceInfo
                                            partnerInvoice={partnerInvoice}
                                            partner={partner?.data}
                                            productCount={order.products.length}
                                        />
                                    </div>
                                    <div className="row me-0 ms-0">
                                        <PartnerInvoiceProducts
                                            partnerInvoice={partnerInvoice}
                                            order={order}
                                            products={products ?? []}
                                        />
                                    </div>
                                </div>
                            </div>
                            <div className="col-xl-3 col-md-4 col-12 invoice-actions mt-md-0 mt-2">
                                <PartnerInvoicePrice
                                    partnerInvoice={partnerInvoice}
                                    order={order}
                                    partner={partner?.data}
                                />
                                {partnerInvoice.status_id === ItemStatus.PENDING && REACT_APP_IS_PARTNER && (
                                    <div className="card mb-6">
                                        <div className="card-body">
                                            <Elements stripe={stripePromise}>
                                                <PaymentForm
                                                    id={partnerInvoice.id!}
                                                    onPaymentSuccess={() => refetch()}
                                                />
                                            </Elements>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
