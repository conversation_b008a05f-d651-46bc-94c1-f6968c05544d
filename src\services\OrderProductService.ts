import { ApiResponse, DataListResponse } from 'types/common';
import { OrderStatus } from 'types/Order';
import OrderProduct, { OrderProductStatus, SearchOrderProduct, UpdateDesignPackedQuantity } from 'types/OrderProduct';
import Http from './http.class';
import { join } from 'lodash';

const http = new Http().instance;

const OrderProductService = {
    async getList(searchParams: SearchOrderProduct, manufacturerId: string, productStatusId: number) {
        const params = { ...searchParams };
        params['order.warehouse.manufacturer_id'] = manufacturerId;
        params.status_id__i = productStatusId.toString();
        if (params.productChild_sku) {
            params['productChild.sku'] = params.productChild_sku;
            delete params.productChild_sku;
        }
        if (params.order_status_id__i) {
            if (productStatusId === OrderProductStatus.PENDING) {
                params['order.status_id__i'] = params.order_status_id__i as string;
            }
            delete params.order_status_id__i;
        } else if (productStatusId === OrderProductStatus.PENDING) {
            params['order.status_id__i'] = join(
                [OrderStatus.PENDING, OrderStatus.REQUESTED, OrderStatus.ENOUGH_STOCK, OrderStatus.MANUFACTURING],
                ','
            );
        }
        if (productStatusId === OrderProductStatus.MANUFACTURING) {
            params['order.status_id__i'] = OrderStatus.MANUFACTURING.toString();
            params.status_id__i = join([productStatusId, OrderProductStatus.PENDING], ',');
        }
        if (productStatusId === OrderProductStatus.FINISH) {
            params['order.status_id__i'] = join([OrderStatus.WAIT_PACK, OrderStatus.MANUFACTURING], ',');
        }
        if (params.quantity_draft_full) {
            if (+params.quantity_draft_full !== 1) delete params.quantity_draft_full;
        }
        if (params.designed_quantity_full) {
            if (+params.designed_quantity_full !== 1) delete params.designed_quantity_full;
        }
        const { data } = await http.get<ApiResponse<DataListResponse<OrderProduct>>>('/order-product', { params });
        if (data.success) return data.data;
    },

    async startManufacturing(orderId: string, orderProductId: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/order/${orderId}/production-started/${orderProductId}`);
        return data;
    },

    async finishManufacture(orderId: string, products: UpdateDesignPackedQuantity[]) {
        const { data } = await http.patch<ApiResponse<{}>>(`/order/${orderId}/production-finished`, { products });
        return data;
    },

    async finishPacking(orderId: string, products: UpdateDesignPackedQuantity[]) {
        const { data } = await http.patch<ApiResponse<{}>>(`/order/${orderId}/packaging-complete`, { products });
        return data;
    },
};

export default OrderProductService;
