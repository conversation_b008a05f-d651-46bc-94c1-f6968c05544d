import { useEffect, useRef } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import Shop from 'types/Shop';

interface UseShopProductFormProps {
    shops: Shop[];
    isShopProductRoute: boolean;
}

export const useShopProductForm = ({ shops, isShopProductRoute }: UseShopProductFormProps) => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const location = useLocation();
    const prevPartnerIdRef = useRef<string | null>(null);

    // Auto-set domain when shop_id is available in URL params
    useEffect(() => {
        if (isShopProductRoute && searchParams.get('shop_id') && !searchParams.get('domain')) {
            const shopId = searchParams.get('shop_id');
            const selectedShop = shops.find((shop) => shop.id === shopId);

            if (selectedShop?.domain) {
                const newSearchParams = new URLSearchParams(searchParams);
                newSearchParams.set('domain', selectedShop.domain);
                navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
            }
        }
    }, [searchParams, shops, isShopProductRoute, navigate, location.pathname]);

    // Reset shop selection when partner changes or is cleared
    useEffect(() => {
        if (isShopProductRoute) {
            const currentPartnerId = searchParams.get('partner_id');

            // Check if partner changed or was cleared
            if (prevPartnerIdRef.current !== null && prevPartnerIdRef.current !== currentPartnerId) {
                // Partner changed or cleared, reset shop and domain
                const newSearchParams = new URLSearchParams(searchParams);
                newSearchParams.delete('shop_id');
                newSearchParams.delete('domain');
                navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
            }

            prevPartnerIdRef.current = currentPartnerId;
        }
    }, [searchParams, isShopProductRoute, navigate, location.pathname]);

    return {
        selectedShopId: searchParams.get('shop_id'),
        selectedDomain: searchParams.get('domain'),
        selectedPartnerId: searchParams.get('partner_id'),
    };
};
