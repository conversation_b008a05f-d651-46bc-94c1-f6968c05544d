import { useQuery } from '@tanstack/react-query';
import ResetButton from 'components/partials/ResetButton';
import Spinner from 'components/partials/Spinner';
import UpdateButton from 'components/partials/UpdateButton';
import { QUERY_KEY } from 'constants/common';
import { isUndefined, omitBy } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import Chart from 'react-apexcharts';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select, { MultiValue, SingleValue } from 'react-select';
import DashboardService from 'services/DashboardService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import WarehouseService from 'services/WarehouseService';
import { TotalPriceParam } from 'types/Dashboard';
import { SearchOrderStatusNames } from 'types/Order';
import User, { UserRole } from 'types/User';
import { ItemStatus, SelectOptionModel } from 'types/common/Item';
import { convertObjectToSelectOptions, flattenObject } from 'utils/common';
import { SearchWarehouse } from '../../../types/Warehouse';

interface IProps {
    user: User;
}

type SelectSearch = 'OrderStatus' | 'Warehouse' | 'Manufacturer' | 'Partner' | 'Shop';

const listMonths = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
const listMonthsTxt = ['Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'June', 'July', 'Aug.', 'Sept.', 'Oct.', 'Nov.', 'Dec.'];

const currentDate = new Date();

export default function TotalPrice({ user }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [searchParams, setSearchParams] = useState<TotalPriceParam>({ year: currentDate.getFullYear() });
    const { handleSubmit, reset, setValue } = useForm<TotalPriceParam>();
    const [selectedDate, setSelectedDate] = useState(currentDate);
    const [orderStatus, setOrderStatus] = useState<SelectOptionModel | null>(null);
    const [shopSelected, setShopSelected] = useState<MultiValue<SelectOptionModel>>([]);
    const [warehouseSelected, setWarehouseSelected] = useState<SelectOptionModel | null>(null);
    const [manufacturerSelected, setManufacturerSelected] = useState<SelectOptionModel | null>(null);
    const [partnerSelected, setPartnerSelected] = useState<SelectOptionModel | null>(null);
    const chartOptions = useMemo(
        () => ({
            chart: {
                id: 'apexchart-example',
            },
            xaxis: {
                categories: listMonthsTxt,
            },
            yaxis: {
                labels: {
                    formatter: function (value: number) {
                        return value + ' AUD';
                    },
                },
            },
        }),
        []
    );

    useEffect(() => {
        if (user.role_id === UserRole.PARTNER) setValue('shop.partner_id', user.id as never);
    }, [user]);

    const renderYearContent = (year: number) => {
        const tooltipText = `Tooltip for year: ${year}`;
        return <span title={tooltipText}>{year}</span>;
    };

    const handleYearChange = (date: Date | null) => {
        setSelectedDate(date ?? currentDate);
        setValue('year', date?.getFullYear() ?? currentDate.getFullYear());
    };

    const { data: shops } = useQuery({
        queryKey: [QUERY_KEY.SHOPS],
        queryFn: () => ShopService.getList({ status_id__i: ItemStatus.ACTIVE.toString() }),
    });

    const { data: warehouses } = useQuery({
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
    });

    const { data: manufacturers } = useQuery({
        queryKey: [QUERY_KEY.USERS, UserRole.MANUFACTURER],
        queryFn: () =>
            UserService.getList({
                status_id__i: ItemStatus.ACTIVE.toString(),
                role_id__i: UserRole.MANUFACTURER.toString(),
            }),
    });

    const { data: partners } = useQuery({
        queryKey: [QUERY_KEY.USERS, UserRole.PARTNER],
        queryFn: () =>
            UserService.getList({
                status_id__i: ItemStatus.ACTIVE.toString(),
                role_id__i: UserRole.PARTNER.toString(),
            }),
        enabled: user.role_id === UserRole.ADMIN,
    });

    const onChangeSelectSearch = (option: SingleValue<SelectOptionModel>, type: SelectSearch) => {
        switch (type) {
            case 'OrderStatus':
                setValue('status_id__i', option?.value ?? undefined);
                setOrderStatus(option);
                break;
            case 'Warehouse':
                setValue('warehouse_id', option?.value);
                setWarehouseSelected(option);
                break;
            case 'Manufacturer':
                setValue('warehouse.manufacturer_id', option?.value as never);
                setManufacturerSelected(option);
                break;
            case 'Partner':
                setValue('shop.partner_id', option?.value as never);
                setPartnerSelected(option);
                break;
            default:
        }
    };

    const onChangeSelectShop = (options: MultiValue<SelectOptionModel>) => {
        setValue('shop__id', options.map((option) => option.value).join());
        setShopSelected(options);
    };

    const handleSearch = (formData: TotalPriceParam) => {
        const flattenedData = flattenObject(formData);
        const cleanedData = omitBy(flattenedData, isUndefined);
        setSearchParams((prev) => ({ ...prev, ...cleanedData }));
    };

    const handleReset = () => {
        reset();
        setSearchParams({ year: currentDate.getFullYear() });
        setSelectedDate(currentDate);
        setOrderStatus(null);
        setShopSelected([]);
        setWarehouseSelected(null);
        setManufacturerSelected(null);
        setPartnerSelected(null);
    };

    const { data, isLoading, isRefetching } = useQuery({
        queryKey: [QUERY_KEY.DASHBOARD_TOTAL_PRICE, searchParams],
        queryFn: async ({ queryKey }) => {
            const res = await DashboardService.getTotalPrice(queryKey[1] as TotalPriceParam);
            const monthlyRevenue = listMonths.map((month) => {
                const matchingData = res.find((item) => parseInt(item.month) === month);
                return matchingData ? parseFloat(matchingData.total_revenue) : 0;
            });
            return [
                {
                    name: t('total_revenue'),
                    data: monthlyRevenue,
                },
            ];
        },
    });

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{t('salesRevenue')}</h4>
            </div>
            <div className="card-body">
                <form onSubmit={handleSubmit(handleSearch)}>
                    <div className="row">
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('year')}</label>
                            <br />
                            <DatePicker
                                selected={selectedDate}
                                renderYearContent={renderYearContent}
                                onChange={handleYearChange}
                                showYearPicker
                                dateFormat="yyyy"
                                yearItemNumber={9}
                                className="form-control"
                            />
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('order.status')}</label>
                            <Select
                                options={convertObjectToSelectOptions(SearchOrderStatusNames, true, t)}
                                onChange={(option) => onChangeSelectSearch(option, 'OrderStatus')}
                                value={orderStatus}
                                isClearable={true}
                            />
                        </div>
                        {shops && (
                            <div className="col-12 col-md-4 mb-1">
                                <label className="form-label">{t('shop.single')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(
                                        shops.items.map(
                                            (item) => ({
                                                id: item.id!,
                                                name: item.name,
                                            }),
                                            true
                                        )
                                    )}
                                    onChange={onChangeSelectShop}
                                    value={shopSelected}
                                    isClearable={true}
                                    isMulti={true}
                                />
                            </div>
                        )}
                        {warehouses && (
                            <div className="col-12 col-md-4 mb-1">
                                <label className="form-label">{t('warehouse.single')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(
                                        warehouses.map(
                                            (item) => ({
                                                id: item.id!,
                                                name: item.name,
                                            }),
                                            true
                                        )
                                    )}
                                    onChange={(option) => onChangeSelectSearch(option, 'Warehouse')}
                                    value={warehouseSelected}
                                    isClearable={true}
                                />
                            </div>
                        )}
                        {manufacturers && (
                            <div className="col-12 col-md-4 mb-1">
                                <label className="form-label">{t('manufacturer.single')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(
                                        manufacturers.items.map(
                                            (item) => ({
                                                id: item.id!,
                                                name: `${item.first_name} ${item.last_name}`,
                                            }),
                                            true
                                        )
                                    )}
                                    onChange={(option) => onChangeSelectSearch(option, 'Manufacturer')}
                                    value={manufacturerSelected}
                                    isClearable={true}
                                />
                            </div>
                        )}
                        {user.role_id === UserRole.ADMIN && partners && (
                            <div className="col-12 col-md-4 mb-1">
                                <label className="form-label">{t('partner.single')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(
                                        partners.items.map(
                                            (item) => ({
                                                id: item.id!,
                                                name: `${item.first_name} ${item.last_name}`,
                                            }),
                                            true
                                        )
                                    )}
                                    onChange={(option) => onChangeSelectSearch(option, 'Partner')}
                                    value={partnerSelected}
                                    isClearable={true}
                                />
                            </div>
                        )}
                        <div className="col-md-4 col-12">
                            <UpdateButton
                                btnText={t('search')}
                                isLoading={isLoading}
                                hasDivWrap={false}
                                btnClass={['me-1']}
                            />
                            <ResetButton btnText={t('reset')} isLoading={isLoading} handleReset={handleReset} />
                        </div>
                    </div>
                </form>
            </div>
            <div className="card-body">
                {(isLoading || isRefetching) && <Spinner />}
                {data && <Chart options={chartOptions} series={data} type="bar" width={'100%'} height={320} />}
            </div>
        </div>
    );
}
