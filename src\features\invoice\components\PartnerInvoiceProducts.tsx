import { useTranslation } from 'react-i18next';
import PartnerInvoice from '../../../types/PartnerInvoice';
import Order from '../../../types/Order';
import { find } from 'lodash';
import ProductColumnInfo from '../../../components/partials/ProductColumnInfo';
import { TransferGoodBarcodeProduct } from '../../../types/TransferGood';

interface IProps {
    partnerInvoice: PartnerInvoice;
    order: Order;
    products: TransferGoodBarcodeProduct[];
}

export default function PartnerInvoiceProducts({ partnerInvoice, order, products }: Readonly<IProps>) {
    const { t } = useTranslation();

    const getProductInfo = (id: string) => {
        const item = find(products, { product_child_id: id });
        return item ? (
            <ProductColumnInfo item={item} />
        ) : (
            <>
                <td className="text-center"></td>
                <td></td>
                <td className="text-center"></td>
            </>
        );
    };

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('quantity')}</th>
                        <th className="text-center">{t('code')}</th>
                        <th>{t('style')}</th>
                        <th className="text-center">{t('barcode')}</th>
                    </tr>
                </thead>
                <tbody>
                    {order.products.map((item) => (
                        <tr key={item.id}>
                            <td className="text-center">{item.quantity}</td>
                            {getProductInfo(item.product_child_id)}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
