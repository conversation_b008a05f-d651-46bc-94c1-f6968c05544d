import classNames from 'classnames';
import { useEffect, useLayoutEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { convertObjectToSelectOptions, getSelectStyle, showToast, toggleModalOpen } from 'utils/common';
import UpdateButton from '../../../components/partials/UpdateButton';
import { ProductChild } from '../../../types/Product';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup/dist/yup';
import Select, { SingleValue } from 'react-select';
import { SelectOptionModel } from '../../../types/common/Item';
import { optionModelDefault } from '../../../constants/common';
import { find } from 'lodash';
import ProductService from '../../../services/ProductService';

interface IProps {
    show: boolean;
    productName: string;
    childId: string;
    rootId: string;
    productChilds: ProductChild[];
    changeShow: (s: boolean, updatedChild: boolean) => void;
}

interface FormData {
    root_id: string;
}

export default function ModalProductChildRoot({
    show,
    productName,
    childId,
    rootId,
    productChilds,
    changeShow,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const [rootValue, setRootValue] = useState<SelectOptionModel>(optionModelDefault);

    const schema = yup
        .object({
            root_id: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        setValue,
        handleSubmit,
        reset,
        clearErrors,
        formState: { errors, isSubmitting },
    } = useForm<FormData>({
        resolver: yupResolver(schema),
    });
    // Reset form when status prop changes
    useEffect(() => {
        reset({ root_id: rootId });
        const root = find(productChilds, { id: rootId });
        if (root) {
            setRootValue({ value: root.id, label: `${root.title} - ${root.sku}` });
        } else {
            setRootValue(optionModelDefault);
        }
    }, [rootId, productChilds, reset]);

    const onChangeRoot = (option: SingleValue<SelectOptionModel>) => {
        setRootValue(option ?? optionModelDefault);
        setValue('root_id', option?.value ?? '');
        if (option?.value) clearErrors('root_id');
    };

    const onSubmit = async (data: FormData) => {
        const response = await ProductService.updateChildRoot(childId, data.root_id);
        showToast(response.success, response.messages);
        if (response.success) {
            changeShow(false, true);
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">Choose Root SKU for {productName}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false, false)} />
                        </div>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12">
                                        <div className="mb-1">
                                            <label className="form-label">
                                                Choose Root SKU <span className="text-danger">*</span>
                                            </label>
                                            <Select
                                                options={convertObjectToSelectOptions(
                                                    productChilds.map((item) => ({
                                                        id: item.id,
                                                        name: `${item.title} - ${item.sku}`,
                                                    })),
                                                    true
                                                )}
                                                onChange={onChangeRoot}
                                                value={rootValue}
                                                styles={{
                                                    control: (baseStyles, state) =>
                                                        getSelectStyle(baseStyles, state, !!errors.root_id?.message),
                                                }}
                                            />
                                            <span className="error">{errors.root_id?.message}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isSubmitting} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
