/* ==================================== */
/* Template Modal Components CSS       */
/* ==================================== */

/* ===== MODAL DESIGN TEMPLATE ===== */
/* Modal Design Template Styles */
.upload-hint-container {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 13px;
}

.file-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: flex-start;
}

.selected-files-info {
  width: 100%;
  margin-bottom: 0.5rem;
}

/* File Preview Styles */
.file-preview-container {
  position: relative;
  display: inline-block;
  width: 150px;
  height: 150px;
  margin-right: 0.75rem;
  margin-bottom: 0.75rem;
  animation: fadeInScale 0.3s ease-out;
}

.file-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e3e6f0;
  background-color: #f8f9fa;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.file-preview-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Delete Button Styles */
.file-delete-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  padding: 0;
  font-size: 14px;
  line-height: 1;
  border: none;
  border-radius: 50%;
  background-color: #dc3545;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.file-delete-button:hover {
  background-color: #c82333;
  transform: scale(1.1);
}

.file-delete-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

/* File Name Overlay Styles */
.file-name-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  text-align: center;
  padding: 4px 6px;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity 0.2s ease;
}

.file-preview-container:hover .file-name-overlay {
  opacity: 1;
}

/* Animation for adding new files */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== MODAL TEMPLATE DETAIL ===== */
/* Modal Template Detail Styles */
.modal-template-detail .modal-dialog {
    max-width: 1200px;
}

.template-detail-container {
    display: flex;
    gap: 20px;
    min-height: 500px;
}

/* Left: Image thumbnails */
.image-thumbnails {
    flex: 0 0 120px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 500px;
    overflow-y: auto;
    padding-right: 5px;
}

.thumbnail-item {
    width: 100px;
    height: 100px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.thumbnail-item:hover {
    border-color: #007bff;
    transform: scale(1.02);
}

.thumbnail-item.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.no-images {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;
    border: 1px dashed #dee2e6;
    border-radius: 8px;
    flex-shrink: 0;
}

/* Center: Main image */
.main-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    min-height: 500px;
    max-height: 500px;
    position: relative;
    padding: 10px;
}

.main-preview {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 8px;
}

.image-counter {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.no-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #6c757d;
}

/* Right: Template info */
.template-info {
    flex: 0 0 300px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.info-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.template-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: #212529;
    word-wrap: break-word;
}

.template-id {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-item label {
    font-weight: 500;
    color: #495057;
    font-size: 0.9rem;
    margin: 0;
}

.info-item span {
    color: #212529;
    font-size: 0.95rem;
    word-wrap: break-word;
}

.info-item .badge {
    align-self: flex-start;
    font-size: 0.8rem;
}

.description {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
    color: #495057;
    word-wrap: break-word;
}

/* Custom scrollbar for thumbnails */
.image-thumbnails::-webkit-scrollbar {
    width: 6px;
}

.image-thumbnails::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.image-thumbnails::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.image-thumbnails::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Custom scrollbar for template info */
.template-info::-webkit-scrollbar {
    width: 6px;
}

.template-info::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.template-info::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.template-info::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== RESPONSIVE DESIGN ===== */
/* Large screens (992px and down) */
@media (max-width: 992px) {
    .template-detail-container {
        flex-direction: column;
        gap: 15px;
    }

    .image-thumbnails {
        flex-direction: row;
        max-height: 120px;
        overflow-x: auto;
        overflow-y: hidden;
        flex: none;
        padding-bottom: 5px;
        padding-right: 0;
    }

    .image-thumbnails::-webkit-scrollbar {
        height: 6px;
        width: auto;
    }

    .thumbnail-item {
        width: 100px;
        height: 100px;
        flex-shrink: 0;
    }

    .template-info {
        flex: none;
        max-height: 300px;
    }

    .main-image {
        min-height: 400px;
        max-height: 400px;
        padding: 8px;
    }

    .main-preview {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
    }
}

/* Medium screens (768px and down) */
@media (max-width: 768px) {
    /* Modal Template Detail responsive */
    .modal-template-detail .modal-dialog {
        max-width: 95%;
        margin: 1rem;
    }

    .template-detail-container {
        gap: 10px;
    }

    .thumbnail-item {
        width: 80px;
        height: 80px;
        flex-shrink: 0;
    }

    .image-thumbnails {
        max-height: 90px;
    }

    .main-image {
        min-height: 300px;
        max-height: 300px;
        padding: 6px;
    }

    .main-preview {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
    }

    .template-name {
        font-size: 1.3rem;
    }

    .info-item {
        gap: 2px;
    }

    .template-info {
        max-height: 250px;
    }

    /* Modal Design Template responsive */
    .file-preview-container {
        width: 120px;
        height: 120px;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .file-upload-container {
        gap: 0.5rem;
    }
    
    .file-name-overlay {
        font-size: 9px;
        padding: 3px 4px;
    }
}

/* Small screens (576px and down) */
@media (max-width: 576px) {
    .file-preview-container {
        width: 100px;
        height: 100px;
    }
    
    .file-name-overlay {
        font-size: 8px;
        padding: 2px 3px;
    }
    
    .file-delete-button {
        width: 20px;
        height: 20px;
        font-size: 12px;
        top: -6px;
        right: -6px;
    }
} 
