import { BaseModel, BaseSearch } from 'types/common';
import { Barcode, ItemParam, ItemParamModel, ProductBarcode } from './common/Item';
import { ProductChild } from './Product';

export interface SearchOrder extends BaseSearch {
    customer_id__i?: string[] | string;
    shop_id__i?: string[] | string;
    financial_status__i?: string[] | string;
    fulfillment_status__i?: string[] | string;
    order_created_at__le?: string;
    order_created_at__ge?: string;
    status_id__i?: string[] | string;
    warehouse_id__i?: string[] | string;
    warehouse_id__n?: string;
    partner_id__i?: string[] | string;
    'shop.partner_id__i'?: string;
    'warehouse.manufacturer_id'?: string;
    designer_id__i?: string;
    is_done_designer?: string;
}

export type SearchOrderParam = {
    [key in keyof SearchOrder]: string;
};

export enum OrderStatus {
    PENDING_PARTNER = 9,
    PENDING = 1,
    REQUESTED = 2,
    ENOUGH_STOCK = 3,
    MANUFACTURING = 4,
    WAIT_PACK = 5,
    PACKED = 6,
    TRANSPORTING = 7,
    FINISH = 8,
}

export enum OrderProductStatus {
    PENDING = 1,
    MANUFACTURING = 2,
    FINISH = 3,
}

export const OrderStatusNames: ItemParam[] = [
    { id: OrderStatus.PENDING_PARTNER, name: 'pending_partner', className: 'badge badge-glow bg-danger' },
    { id: OrderStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-danger' },
    { id: OrderStatus.REQUESTED, name: 'requested', className: 'badge badge-glow bg-secondary' },
    { id: OrderStatus.ENOUGH_STOCK, name: 'enough_stock', className: 'badge badge-glow bg-info' },
    { id: OrderStatus.MANUFACTURING, name: 'manufacturing', className: 'badge badge-glow bg-warning' },
    { id: OrderStatus.WAIT_PACK, name: 'wait_pack', className: 'badge badge-glow bg-secondary' },
    { id: OrderStatus.PACKED, name: 'packed', className: 'badge badge-glow bg-primary' },
    { id: OrderStatus.TRANSPORTING, name: 'transporting', className: 'badge badge-glow bg-info' },
    { id: OrderStatus.FINISH, name: 'finish', className: 'badge badge-glow bg-success' },
];

export const OrderProductStatusNames: ItemParam[] = [
    { id: OrderProductStatus.PENDING, name: 'pending', className: 'badge badge-glow bg-danger' },
    { id: OrderProductStatus.MANUFACTURING, name: 'manufacturing', className: 'badge badge-glow bg-warning' },
    { id: OrderProductStatus.FINISH, name: 'finish', className: 'badge badge-glow bg-success' },
];

export interface OrderAddress {
    name: string;
    zip: string;
    address1: string;
    address2?: string;
    city: string;
    province: string;
    country: string;
    latitude?: number;
    longitude?: number;
}

export interface OrderProduct extends BaseModel {
    order_product_id: number;
    order_id: string;
    product_id: string;
    product_child_id: string;
    quantity: number;
    quantity_draft?: number;
    designed_quantity: number;
    price: number;
    status_id: OrderProductStatus;
    designed_product_child_id?: string;
    designProductChild?: ProductBarcode;
    productChild?: ProductChild;
}

export default interface Order extends BaseModel {
    order_id: string;
    customer_id: string;
    shop_id: string;
    warehouse_id?: string;
    financial_status: string;
    fulfillment_status: string;
    status_id: OrderStatus;
    order_number: string;
    currency: string;
    total_price: string;
    source_name: string;
    payment_gateway_names: string[];
    order_created_at: string;
    gg_folder_id?: string;
    is_done_designer: boolean;
    json: {
        id: number;
        name: string;
        customer: {
            email: string;
            phone: string;
            last_name: string;
            first_name: string;
            verified_email: boolean;
        };
        line_items: {
            id: number;
            name: string;
            price: string;
            quantity: number;
            vendor: string;
        }[];
        billing_address?: OrderAddress;
        shipping_address?: OrderAddress;
        shipping_lines?: { price: string }[];
    };
    products: OrderProduct[];
    ship_info?: {
        request: {
            value: number;
            pickup: {
                date: string;
                latest: string;
                earliest: string;
            };
            parcels: {
                width: number;
                height: number;
                length: number;
                weight: number;
            }[];
            service: string;
            contents: string;
            delivery: {
                add1: string;
                add2: null;
                city: string;
                name: string;
                email: string;
                state: string;
                company: null;
                country: string;
                postcode: string;
                telephone: string;
            };
            validate: boolean;
            reference: string;
            collection: {
                add1: string;
                add2: string;
                city: string;
                name: string;
                email: string;
                state: string;
                company: string;
                country: string;
                postcode: string;
                telephone: string;
            };
        };
        response?: {
            success: boolean;
        };
    };
    total_weight: number;
    designer_id: string;
    design_at: string;
}

export interface OrderProductBarcode extends Order {
    barcode: Barcode;
    count_products: number;
    finishPacking?: boolean;
}

/*export const FinancialStatusNames: ItemParamModel[] = [
    'any',
    'authorized',
    'expired',
    'paid',
    'partially_paid',
    'partially_refunded',
    'pending',
    'refunded',
    'unpaid',
    'voided',
].map((item) => ({ id: item, name: item }));

export const FulfillmentStatusNames: ItemParamModel[] = ['any', 'partial', 'shipped', 'unfulfilled', 'unshipped'].map(
    (item) => ({
        id: item,
        name: item,
    })
);*/

export const SearchOrderStatusNames: ItemParamModel[] = [
    { id: OrderStatus.PENDING.toString(), name: 'pending' },
    { id: OrderStatus.REQUESTED.toString(), name: 'requested' },
    { id: OrderStatus.ENOUGH_STOCK.toString(), name: 'enough_stock' },
    { id: OrderStatus.MANUFACTURING.toString(), name: 'manufacturing' },
    { id: OrderStatus.WAIT_PACK.toString(), name: 'wait_pack' },
    { id: OrderStatus.PACKED.toString(), name: 'packed' },
    { id: OrderStatus.TRANSPORTING.toString(), name: 'transporting' },
    { id: OrderStatus.FINISH.toString(), name: 'finish' },
];
