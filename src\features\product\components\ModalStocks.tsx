import classNames from 'classnames';
import FormatNumber from 'components/partials/FormatNumber';
import { find, findIndex } from 'lodash';
import { useEffect, useLayoutEffect, useState } from 'react';
import { Save } from 'react-feather';
import { useTranslation } from 'react-i18next';
import WarehouseService from 'services/WarehouseService';
import { ProductStock } from 'types/Product';
import Warehouse from 'types/Warehouse';
import { getFieldInArrayObject, showToast, toggleModalOpen } from 'utils/common';

interface IProps {
    show: boolean;
    items: ProductStock[];
    warehouses: Warehouse[];
    productChildId: string;
    productName: string;
    changeShow: (s: boolean, updatedStocks: boolean) => void;
}

interface StockProps {
    name: string;
    count: number;
    quantity_draft: number;
}

export default function ModalStocks({
    show,
    items,
    warehouses,
    productChildId,
    productName,
    changeShow,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const [newStocks, setNewStocks] = useState<StockProps[]>([]);
    const [updatedStocks, setUpdatedStocks] = useState(false);

    useEffect(() => {
        if (show) {
            setUpdatedStocks(false);

            const initialStocks: StockProps[] = [];
            items.forEach((item) => {
                const draftQuantity = getFieldInArrayObject(
                    [item],
                    item.warehouse_id,
                    'quantity_draft',
                    '1',
                    'warehouse_id'
                );

                initialStocks.push({
                    name: item.warehouse_id,
                    count: 0,
                    quantity_draft: Number(draftQuantity),
                });
            });
            setNewStocks(initialStocks);
        }
    }, [show, items]);

    const setNewQuantity = (warehouseId: string, newQuantity: number) => {
        const stocks = [...newStocks];
        const index = findIndex(stocks, { name: warehouseId });
        if (index > -1) stocks[index].count = newQuantity;
        else {
            stocks.push({
                name: warehouseId,
                count: newQuantity,
                quantity_draft: 0,
            });
        }
        setNewStocks(stocks);
    };

    const setDraftQuantity = (warehouseId: string, draftQuantity: number) => {
        const stocks = [...newStocks];
        const index = findIndex(stocks, { name: warehouseId });
        if (index > -1) stocks[index].quantity_draft = draftQuantity;
        else {
            stocks.push({
                name: warehouseId,
                count: 0,
                quantity_draft: draftQuantity,
            });
        }
        setNewStocks(stocks);
    };

    const updateQuantity = async (warehouseId: string) => {
        const item = find(newStocks, { name: warehouseId });
        if (item && productChildId) {
            if (item.count >= 0 && item.quantity_draft >= 0) {
                const stock = find(items, { warehouse_id: warehouseId, product_child_id: productChildId });
                const response = await WarehouseService.updateStock(warehouseId, {
                    id: stock?.id,
                    product_child_id: productChildId,
                    quantity: item.count,
                    quantity_draft: item.quantity_draft,
                });
                showToast(response.success, response.messages);
                if (response.success) setUpdatedStocks(true);
            } else showToast(false, [t('error.min_0')]);
        } else showToast(false, [t('error.common')]);
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-xl">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t('product.quantityOf', { data: productName })}</h5>
                            <button
                                type="button"
                                className="btn-close"
                                onClick={() => changeShow(false, updatedStocks)}
                            />
                        </div>
                        <div className="modal-body">
                            <div className="table-responsive">
                                <table className="table">
                                    <thead>
                                        <tr>
                                            <th className="text-center">{t('no.')}</th>
                                            <th>{t('warehouse.single')}</th>
                                            <th className="text-center">{t('virtualQuantity')}</th>
                                            <th className="text-center">{t('quantity')}</th>
                                            <th className="text-center">{t('product.newQuantity')}</th>
                                            <th className="thAction1"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {warehouses.map((item: Warehouse, index: number) => (
                                            <tr key={item.id}>
                                                <td className="text-center">{index + 1}</td>
                                                <td>{item.name}</td>
                                                <td className="text-center">
                                                    <FormatNumber
                                                        value={find(newStocks, { name: item.id! })?.quantity_draft ?? 0}
                                                        isInput={true}
                                                        onValueChange={(value: number) =>
                                                            setDraftQuantity(item.id!, value)
                                                        }
                                                    />
                                                </td>
                                                <td className="text-center">
                                                    {getFieldInArrayObject(
                                                        items,
                                                        item.id!,
                                                        'quantity',
                                                        '0',
                                                        'warehouse_id'
                                                    )}
                                                </td>
                                                <td className="text-center">
                                                    <FormatNumber
                                                        value={find(newStocks, { name: item.id! })?.count ?? 0}
                                                        isInput={true}
                                                        onValueChange={(value: number) =>
                                                            setNewQuantity(item.id!, value)
                                                        }
                                                    />
                                                </td>

                                                <td className="text-center">
                                                    <button
                                                        type="button"
                                                        title="Lưu trữ"
                                                        className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                        onClick={() => updateQuantity(item.id!)}
                                                    >
                                                        <Save size={14} />
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
