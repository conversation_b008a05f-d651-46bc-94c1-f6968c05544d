import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import FormatNumber from 'components/partials/FormatNumber';
import ProductColumnInfo from 'components/partials/ProductColumnInfo';
import { ceil, find, includes, isEmpty, some, sumBy } from 'lodash';
import { useEffect, useState } from 'react';
import { AlertTriangle, CheckSquare, Download, Edit, ExternalLink, Printer } from 'react-feather';
import { useTranslation } from 'react-i18next';
import OrderService from 'services/OrderService';
import ProductService from 'services/ProductService';
import { ApiResponse } from 'types/common';
import Order, { OrderProductStatusNames, OrderStatus, OrderStatusNames } from 'types/Order';
import Shop from 'types/Shop';
import { TransferGoodBarcodeProduct } from 'types/TransferGood';
import Warehouse from 'types/Warehouse';
import { downloadPdf, getFieldHtml, getFieldInArrayObject, showToast } from 'utils/common';
import { SSITracking } from '../../../types/Starshipit';
import User from '../../../types/User';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import ScanZone from '../../transfer/components/ScanZone';

interface IProps {
    order: Order;
    shop?: Shop;
    products: TransferGoodBarcodeProduct[];
    warehouses: Warehouse[];
    designers: User[];
    shipTracks?: SSITracking;
    onChooseWarehouse: () => void;
    refetch: (
        options?: RefetchOptions
    ) =>
        | Promise<QueryObserverResult<ApiResponse<Order>, Error>>
        | Promise<QueryObserverResult<Order | undefined, Error>>;
}

export default function OrderProducts({
    order,
    shop,
    products,
    warehouses,
    designers,
    shipTracks,
    onChooseWarehouse,
    refetch,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(false);
    const [resendLoading, setResendLoading] = useState(false);
    const [productsTemp, setProductsTemp] = useState<TransferGoodBarcodeProduct[]>([]);

    useEffect(() => {
        setProductsTemp(products);
    }, [products]);

    const getProductInfo = (id: string) => {
        const item = find(products, { product_child_id: id });
        return item ? (
            <ProductColumnInfo item={item} />
        ) : (
            <>
                <td className="text-center"></td>
                <td></td>
                <td className="text-center"></td>
            </>
        );
    };

    const checkIsDesign = (id: string) => {
        const item = find(products, { product_child_id: id });
        return !!item?.isDesign;
    };

    const downloadDesigns = async (id: string) => {
        const item = find(products, { product_child_id: id });
        if (item) {
            const response = await ProductService.downloadMockupFile(item.productId, item.productChildId);
            const blob = new Blob([response.data], { type: response.headers['content-type'] });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `${item.barcode.code}.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }
    };

    const printItem = async () => {
        setLoading(true);
        const response = await OrderService.printItem(order.id!);
        setLoading(false);
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
    };

    const resendShipping = async () => {
        setResendLoading(true);
        try {
            const response = await OrderService.resendShipping(order.id!);
            showToast(true, response.messages);
            if (response.success) {
                await refetch();
            }
        } catch (error) {
            showToast(false, [t('error.common')]);
        } finally {
            setResendLoading(false);
        }
    };

    const printOrder = async () => {
        const carrier = 'Sendle', // shipTracks?.results.carrier_name,
            serviceCode = 'STANDARD-PICKUP'; // shipTracks?.results.carrier_service;
        // if (!carrier || !serviceCode) {
        //     const response = await OrderService.getDeliveryServices(order.id!);
        //     if (response.success && response.data.success) {
        //         const deliveryService = response.data.default_service
        //             ? response.data.default_service
        //             : response.data.services[0];
        //         if (deliveryService) {
        //             carrier = deliveryService.carrier_name;
        //             serviceCode = deliveryService.service_code;
        //         }
        //     }
        // }
        if (carrier && serviceCode) {
            const result = await OrderService.printLabel(order.id!, carrier, serviceCode);
            if (result.success && !isEmpty(result.data.labels)) {
                try {
                    downloadPdf(result.data.labels[0], `${order.order_number}.pdf`);
                } catch {
                    showToast(false, [t('error.common')]);
                }
            } else {
                showToast(false, [t('error.common')]);
            }
        } else showToast(false, [t('error.common')]);
    };

    const printHeroBarcode = async (item: TransferGoodBarcodeProduct) => {
        const response = await OrderService.transferPrint(item.barcode.code, {
            product: {
                image: item.style?.image ?? '',
                name: item.style.title,
                code: item.code,
                size: item.style.size,
                color: item.style.color,
                barcode: item.barcode.code,
                barcodeImg: item.barcode.image,
            },
        });
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
    };

    const onPrint = (id: string) => {
        const item = find(products, (_item) => _item.product_child_id === id);
        if (item) printHeroBarcode(item);
        else showToast(false, [t('error.barcode')]);
    };

    const onScan = async (code: string, it?: TransferGoodBarcodeProduct[]) => {
        let index = -1;
        const items = [...(it ?? productsTemp)];
        items.forEach((item, i) => {
            if (item.barcode.code === code) {
                index = i;
                return false;
            }
        });
        if (index > -1) {
            items[index].scan_quantity++;
            setProductsTemp(items);
        } else {
            const response = await ProductService.getBySku(code);
            if (response.success) {
                items.push({ ...response.data, transfer_good_product_id: '', quantity: 0, scan_quantity: 1 });
                setProductsTemp(items);
            } else showToast(response.success, response.messages);
        }
    };

    const onDoneDesigner = async () => {
        const response = await OrderService.updateDoneDesigner([order.id!]);
        showToast(response.success, response.messages);
        if (response.success) {
            await refetch();
        }
    };

    return (
        <div className="col-12">
            <div className="card mb-6">
                <div className="card-header d-flex justify-content-between align-items-center">
                    <h5 className="card-title m-0">{t('product.multiple')}</h5>
                </div>
                <div className="table-responsive mb-1">
                    <table className="table">
                        <thead>
                            <tr>
                                <th className="text-center">{t('code')}</th>
                                <th>{t('style')}</th>
                                <th className="text-center">{t('barcode')}</th>
                                <th className="text-center"></th>
                                <th className="text-end">{t('totalPrice')}</th>
                                {some(products, (item) => item.isDesign) && (
                                    <th className="text-center">{t('manufacturer.status')}</th>
                                )}
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            {order.products.map((item) => (
                                <tr key={item.id}>
                                    {getProductInfo(
                                        item.product_child_id
                                        // isEmpty(item.designed_product_child_id)
                                        //     ? item.product_child_id
                                        //     : item.designed_product_child_id!
                                    )}
                                    <td className="text-center">
                                        <FormatNumber
                                            value={item.price}
                                            isInput={false}
                                            renderText={(value) => value}
                                        />{' '}
                                        {order.currency} x{' '}
                                        <FormatNumber
                                            value={item.quantity}
                                            isInput={false}
                                            renderText={(value) => value}
                                        />
                                        {(item.quantity_draft ?? 0) < item.quantity &&
                                            includes([OrderStatus.PENDING, OrderStatus.REQUESTED], order.status_id) && (
                                                <>
                                                    <br />
                                                    <span className="text-danger">
                                                        {t('missing')} {item.quantity - (item.quantity_draft ?? 0)}
                                                    </span>
                                                </>
                                            )}
                                    </td>
                                    <td className="text-end">
                                        <FormatNumber
                                            value={ceil(item.quantity * +item.price, 2)}
                                            isInput={false}
                                            renderText={(value) => value}
                                        />{' '}
                                        {order.currency}
                                    </td>
                                    {some(products, (item) => item.isDesign) && (
                                        <td className="text-center">
                                            {checkIsDesign(
                                                isEmpty(item.designed_product_child_id)
                                                    ? item.product_child_id
                                                    : item.designed_product_child_id!
                                            ) && (
                                                <>
                                                    {item.designed_quantity} <br />
                                                    {getFieldHtml(OrderProductStatusNames, item.status_id, t)}
                                                    <br />
                                                    <Download
                                                        size={14}
                                                        className="cursor-pointer"
                                                        onClick={() =>
                                                            downloadDesigns(
                                                                isEmpty(item.designed_product_child_id)
                                                                    ? item.product_child_id
                                                                    : item.designed_product_child_id!
                                                            )
                                                        }
                                                    />
                                                </>
                                            )}
                                        </td>
                                    )}
                                    <td className="text-center">
                                        <Printer
                                            size={14}
                                            className="cursor-pointer"
                                            onClick={() => onPrint(item.product_child_id)}
                                        />
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
                <div className="row mb-1">
                    <div className="col-xl-8 col-md-7 col-12">
                        {shipTracks?.results && (
                            <div className="table-responsive">
                                <div className="card-header d-flex justify-content-between align-items-center">
                                    <h5 className="card-title m-0">{t('order.shipping')}</h5>
                                </div>
                                <table className="table mb-1">
                                    <thead>
                                        <tr>
                                            <th className="text-center">{t('order.carrier')}</th>
                                            <th className="text-center">{t('status')}</th>
                                            <th className="thAction1"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td className="text-center">
                                                {shipTracks.results.carrier_name}
                                                <br />
                                                {shipTracks.results.carrier_service}
                                            </td>
                                            <td className="text-center">
                                                {shipTracks.results.order_status}{' '}
                                                {shipTracks.results.tracking_url && (
                                                    <a href={shipTracks.results.tracking_url} target="_blank">
                                                        <ExternalLink size={14} />
                                                    </a>
                                                )}
                                                <br />
                                                {shipTracks.results.last_updated_date !== '0001-01-01T00:00:00' &&
                                                    formatDateTime(
                                                        shipTracks.results.last_updated_date,
                                                        FORMAT_DATE.SHOW_DATE_MINUTE
                                                    )}
                                            </td>
                                            <td className="text-center">
                                                <Printer
                                                    size={14}
                                                    className="cursor-pointer text-danger"
                                                    onClick={printOrder}
                                                />
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                {!isEmpty(shipTracks.results.tracking_events) && (
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th className="text-center">{t('date')}</th>
                                                <th className="text-center">{t('status')}</th>
                                                <th>{t('details')}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                {shipTracks.results.tracking_events?.map((item, index) => (
                                                    <tr key={index}>
                                                        <td className="text-center">
                                                            {formatDateTime(
                                                                item.event_datetime,
                                                                FORMAT_DATE.SHOW_DATE_MINUTE
                                                            )}
                                                        </td>
                                                        <td className="text-center">{item.status}</td>
                                                        <td>{item.details}</td>
                                                    </tr>
                                                ))}
                                            </tr>
                                        </tbody>
                                    </table>
                                )}
                            </div>
                        )}
                    </div>
                    <div className="col-xl-4 col-md-5 col-12">
                        <div>
                            <div className="d-flex justify-content-start mb-1">
                                <span className="w-px-150 text-heading">{t('statusName')}</span>
                                <h6 className="mb-0">
                                    {getFieldHtml(OrderStatusNames, order.status_id, t)}
                                    {order.status_id === OrderStatus.PACKED && (
                                        <>
                                            {shipTracks?.success ? (
                                                <CheckSquare size={10} className="text-success mx-1" />
                                            ) : (
                                                <button
                                                    type="button"
                                                    className="btn btn-icon btn-icon rounded-circle btn-flat-danger waves-effect mx-1"
                                                    title={t('order.resendShip')}
                                                    onClick={resendShipping}
                                                    disabled={resendLoading}
                                                >
                                                    <AlertTriangle scale={10} />
                                                </button>
                                            )}
                                        </>
                                    )}
                                </h6>
                            </div>
                            <div className="d-flex justify-content-start mb-1">
                                <span className="w-px-150 text-heading">{t('designer.single')}</span>
                                <h6 className="mb-0">
                                    {order.designer_id && getFieldInArrayObject(designers, order.designer_id)}
                                    {!order.is_done_designer && (
                                        <button
                                            type="button"
                                            className="btn btn-icon btn-icon btn-flat-info waves-effect mx-1"
                                            onClick={onDoneDesigner}
                                        >
                                            Done Design
                                        </button>
                                    )}
                                </h6>
                            </div>
                            <div className="d-flex justify-content-start mb-1">
                                <span className="w-px-150 text-heading">{t('shop.single')}</span>
                                <h6 className="mb-0">
                                    {shop && (
                                        <a href={`https://${shop.domain}`} target="_blank">
                                            {shop.name}
                                        </a>
                                    )}
                                </h6>
                            </div>
                            <div className="d-flex justify-content-start mb-1">
                                <span className="w-px-150 text-heading">{t('warehouse.single')}</span>
                                <h6 className="mb-0">
                                    {order.warehouse_id ? (
                                        getFieldInArrayObject(warehouses, order.warehouse_id)
                                    ) : (
                                        <Edit
                                            size={14}
                                            className="text-primary cursor-pointer"
                                            onClick={onChooseWarehouse}
                                        />
                                    )}
                                </h6>
                            </div>
                            <div className="d-flex justify-content-start mb-1">
                                <span className="w-px-150 text-heading">{t('order.shippingCost')}</span>
                                <h6 className="mb-0">
                                    <FormatNumber
                                        value={sumBy(order.json.shipping_lines, (item) => +item.price)}
                                        isInput={false}
                                        renderText={(value) => value}
                                    />{' '}
                                    {order.currency}
                                </h6>
                            </div>
                            <div className="d-flex justify-content-start mb-1">
                                <h6 className="w-px-150 text-heading">{t('totalPrice')}</h6>
                                <h6 className="mb-0">
                                    <FormatNumber
                                        value={order.total_price}
                                        isInput={false}
                                        renderText={(value) => value}
                                    />{' '}
                                    {order.currency}
                                </h6>
                            </div>
                            <div className="d-flex justify-content-start">
                                <button
                                    className="btn btn-success waves-effect waves-float waves-light w-100"
                                    disabled={loading}
                                    onClick={printItem}
                                >
                                    {t('printJobSheet')}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <ScanZone onScan={onScan} products={productsTemp} hideInput={true} />
        </div>
    );
}
