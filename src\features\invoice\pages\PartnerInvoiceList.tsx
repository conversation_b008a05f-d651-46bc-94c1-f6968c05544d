import { keepPreviousData, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, REACT_APP_IS_ADMIN } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import PartnerInvoiceService from 'services/PartnerInvoiceService';
import { SearchPartnerInvoice, SearchPartnerInvoiceParam } from 'types/PartnerInvoice';
import ListPartnerInvoice from '../components/ListPartnerInvoice';
import SearchPartnerInvoiceForm from '../components/SearchPartnerInvoiceForm';
import { SearchUser, UserRole } from '../../../types/User';
import { ItemStatus } from '../../../types/common/Item';
import UserService from '../../../services/UserService';
import { useAuthStore } from '../../../stores/authStore';

export default function PartnerInvoiceList() {
    const { t } = useTranslation();
    const user = useAuthStore((state) => state.user);
    const { queryParams, setQueryParams } = useQueryParams<SearchPartnerInvoiceParam>();
    const paramConfig: SearchPartnerInvoiceParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            partner_id__i: queryParams.partner_id__i,
            status_id__i: queryParams.status_id__i,
        },
        isUndefined
    );

    const { data: partners } = useQuery({
        enabled: REACT_APP_IS_ADMIN,
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.PARTNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data, isLoading, isRefetching } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.PARTNER_INVOICES, paramConfig, user],
        queryFn: ({ queryKey }) => PartnerInvoiceService.getList(queryKey[1] as SearchPartnerInvoice, user!.id),
        placeholderData: keepPreviousData,
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>{t(`partnerInvoice.multiple`)}</title>
            </Helmet>
            <ContentHeader title={t(`partnerInvoice.multiple`)} />
            <div className="content-body">
                <div className="col-12">
                    <SearchPartnerInvoiceForm partners={partners?.items ?? []} isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListPartnerInvoice
                                items={data.items}
                                partners={partners?.items ?? []}
                                paging={data.pagination}
                            />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
