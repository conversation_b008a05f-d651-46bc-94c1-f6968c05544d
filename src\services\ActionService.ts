import {
    MenuDesigners,
    MenuManufacturers,
    MenuPartners,
    REACT_APP_IS_ADMIN,
    REACT_APP_IS_DESIGNER,
    REACT_APP_IS_MANUFACTURER,
    REACT_APP_IS_PARTNER,
} from 'constants/common';
import Action from 'types/Action';
import { ApiResponse } from 'types/common';
import Http from './http.class';

const http = new Http().instance;

const getFlatList = (actions: Action[]) => {
    const items: Action[] = [];
    actions.forEach((item) => {
        items.push(item);
        if (item.children) {
            item.children.forEach((child) => items.push(child));
        }
    });
    return items;
};

const ActionService = {
    getFlatList,

    async getList(isFlat = false) {
        const { data } = await http.get<ApiResponse<Action[]>>('/action');
        if (data.success) {
            return isFlat ? getFlatList(data.data) : data.data;
        }
        return [];
    },

    async getAuthActions() {
        if (REACT_APP_IS_ADMIN) {
            const { data } = await http.get<ApiResponse<Action[]>>('/auth/actions');
            return data.success ? data.data : [];
        }
        if (REACT_APP_IS_PARTNER) return MenuPartners;
        if (REACT_APP_IS_MANUFACTURER) return MenuManufacturers;
        if (REACT_APP_IS_DESIGNER) return MenuDesigners;
        return [];
    },

    async update(param: Partial<Action>, id: string = '') {
        delete param.id;
        const { data } = id
            ? await http.put<ApiResponse<Action>>(`/action/${id}`, param)
            : await http.post<ApiResponse<Action>>('/action', param);
        return data;
    },

    async deleteItem(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/action/${id}`);
        return data;
    },
};

export default ActionService;
