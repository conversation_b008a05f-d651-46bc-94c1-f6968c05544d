import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { find, isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import DesignerScheduleService from 'services/DesignerScheduleService';
import { useAuthStore } from 'stores/authStore';
import DesignerSchedule, { SearchDesignerSchedule, SearchDesignerScheduleParam } from 'types/DesignerSchedule';
import { showToast } from 'utils/common';
import ListDesignerSchedule from '../components/ListDesignerSchedule';
import ModalDesignerScheduleUpdate from '../components/ModalDesignerScheduleUpdate';
import { useLocation } from 'react-router-dom';

export default function DesignerScheduleList() {
    const { t } = useTranslation();
    const [itemId, setItemId] = useState('');
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const user = useAuthStore((state) => state.user);

    const { search } = useLocation();
    const searchParams = new URLSearchParams(search);
    const mode = searchParams.get('mode');

    const { queryParams, setQueryParams } = useQueryParams<SearchDesignerScheduleParam>();
    const paramConfig: SearchDesignerScheduleParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            designer_id__i: queryParams.designer_id__i,
        },
        isUndefined
    );

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.DESIGNER_SCHEDULES, paramConfig, user],
        queryFn: ({ queryKey }) => DesignerScheduleService.getList(queryKey[1] as SearchDesignerSchedule, user!.id),
        placeholderData: keepPreviousData,
    });

    const updateMutation = useMutation({
        mutationFn: (param: DesignerSchedule) => DesignerScheduleService.update(param, param.id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowUpdate(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => DesignerScheduleService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const updateItem = (body: DesignerSchedule) => {
        body.designer_id = user!.id!;
        updateMutation.mutate(body);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const actionMenu = () => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId('');
    };

    const handleEdit = (id: string) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    return (
        <>
            <Helmet>
                <title>{t(`designerSchedule.multiple`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`designerSchedule.multiple`)}
                contextMenu={
                    mode && mode === 'view'
                        ? []
                        : [
                              {
                                  text: t('designerSchedule.add'),
                                  icon: 'PLUS',
                                  fnCallBack: { actionMenu },
                              },
                          ]
                }
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListDesignerSchedule
                                    items={data.items}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                    mode={mode}
                                />
                                <PaginationTable
                                    countItem={data.pagination.count_item}
                                    totalPage={data.pagination.total_page}
                                    currentPage={data.pagination.current_page}
                                    handlePageChange={handlePageChange}
                                />
                            </div>
                            <ModalDesignerScheduleUpdate
                                show={showUpdate}
                                designerSchedule={find(data.items, { id: itemId })}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
