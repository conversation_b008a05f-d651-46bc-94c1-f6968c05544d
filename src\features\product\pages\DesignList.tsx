import { keepPreviousData, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ProductService from 'services/ProductService';
import UserService from 'services/UserService';
import { SearchDesign, SearchDesignParam } from 'types/Product';
import { SearchUser, UserRole } from 'types/User';
import { ItemStatus } from 'types/common/Item';
import ListDesign from '../components/ListDesign';
import SearchDesignForm from '../components/SearchDesignForm';

export default function DesignList() {
    const { t } = useTranslation();
    const { queryParams, setQueryParams } = useQueryParams<SearchDesignParam>();
    const paramConfig: SearchDesignParam = omitBy(
        {
            limit: 600, //queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            is_design_ok: queryParams.is_design_ok,
            size__i: queryParams.size__i,
            code__i: queryParams.code__i,
            color: queryParams.color,
            placement_id__i: queryParams.placement_id__i,
            placement__i: queryParams.placement__i,
            product_id__i: queryParams.product_id__i,
            'product.vendor__i': queryParams['product.vendor__i'],
        },
        isUndefined
    );

    const { data: sizes } = useQuery({
        queryKey: [QUERY_KEY.DESIGN_SIZES],
        queryFn: () => ProductService.getDesignSizes(),
    });

    const { data: codes } = useQuery({
        queryKey: [QUERY_KEY.DESIGN_CODES],
        queryFn: () => ProductService.getDesignCodes(),
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        queryKey: [QUERY_KEY.PRODUCT_DESIGNS, paramConfig],
        queryFn: ({ queryKey }) => ProductService.getListDesign(queryKey[1] as SearchDesign),
        placeholderData: keepPreviousData,
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const { data: suppliers } = useQuery({
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.SUPPLIER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    return (
        <>
            <Helmet>
                <title>{t('design.multiple')}</title>
            </Helmet>
            <ContentHeader title={t('design.multiple')} />
            <div className="content-body">
                <div className="col-12">
                    <SearchDesignForm
                        isLoading={isLoading || isRefetching}
                        suppliers={suppliers?.items ?? []}
                        sizes={sizes ?? []}
                        codes={codes ?? []}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListDesign items={data.items} paging={data.pagination} refetch={() => refetch()} />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
