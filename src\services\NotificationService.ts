import { ApiResponse, DataListResponse } from 'types/common';
import Http from './http.class';
import Notification, { SearchNotification } from '../types/Notification';
import { ItemStatus } from '../types/common/Item';

const http = new Http().instance;

const NotificationService = {
    async getList(params: SearchNotification) {
        const { data } = await http.get<ApiResponse<DataListResponse<Notification>>>('/notifications', { params });
        if (data.success) return data.data;
    },

    async read(id: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/notifications/${id}/status`, {
            status_id: ItemStatus.ACTIVE,
        });
        return data;
    },

    async readAll() {
        const { data } = await http.patch<ApiResponse<{}>>('/notifications/status-all', {
            status_id: ItemStatus.ACTIVE,
        });
        return data;
    },
};

export default NotificationService;
