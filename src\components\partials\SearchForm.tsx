import classNames from 'classnames';
import ResetButton from 'components/partials/ResetButton';
import UpdateButton from 'components/partials/UpdateButton';
import { isArray } from 'lodash';
import { useEffect, useState } from 'react';
import { ArrowDown, ArrowUp } from 'react-feather';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import Select from 'react-select';
import { SearchField } from 'types/common/Search';

interface IProps {
    fields: SearchField[];
    isLoading: boolean;
}

export default function SearchForm({ fields, isLoading }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [showSearch, setShowSearch] = useState(false);
    const { handleSubmit, reset, control, getValues } = useForm<FieldValues>({
        defaultValues: fields.reduce((acc, field) => {
            acc[field.name] = field.type === 'select' ? [] : '';
            return acc;
        }, {} as FieldValues),
    });
    const navigate = useNavigate();
    const location = useLocation();
    const [searchParams] = useSearchParams();

    useEffect(() => {
        const defaultValues: FieldValues = {};
        fields.forEach((field) => {
            if (field.type === 'select') {
                const selectedValue = searchParams.get(field.name);
                if (selectedValue) {
                    const selectedValues = selectedValue.split(',');
                    defaultValues[field.name] = field.options.choices.filter((choice) =>
                        selectedValues.includes(choice.value.toString())
                    );
                } else {
                    defaultValues[field.name] = field.options.multiple ? [] : null;
                }
            } else {
                defaultValues[field.name] = searchParams.get(field.name) ?? '';
            }
        });
        reset(defaultValues);
    }, [searchParams, fields, reset]);

    const onSubmit = (data: FieldValues) => {
        const newSearchParams = new URLSearchParams();
        Object.entries(data).forEach(([key, value]) => {
            if (value) {
                if (Array.isArray(value)) {
                    const stringValue = value.map((item) => item.value).join(',');
                    if (stringValue) newSearchParams.set(key, stringValue);
                } else if (typeof value === 'object' && value !== null) {
                    if (value.value) {
                        newSearchParams.set(key, value.value);
                    } else {
                        Object.entries(value).forEach(([keyChild, valueChild]) => {
                            const keySearch = `${key}.${keyChild}`;
                            if (Array.isArray(valueChild)) {
                                const stringValue = valueChild.map((item) => item.value).join(',');
                                if (stringValue) newSearchParams.set(keySearch, stringValue);
                            } else if (valueChild) newSearchParams.set(keySearch, String(valueChild));
                        });
                    }
                } else {
                    newSearchParams.set(key, value.toString());
                }
            }
        });
        navigate(`${location.pathname}?${newSearchParams.toString()}`);
    };

    const handleReset = () => {
        reset();
        navigate(location.pathname);
    };

    return (
        <div className="card">
            <div
                className="card-header cursor-pointer d-flex justify-content-between align-items-start pb-1"
                onClick={() => setShowSearch((prev) => !prev)}
            >
                <h4 className="card-title">{t('search')}</h4>
                {!showSearch ? <ArrowDown size={14} /> : <ArrowUp size={14} />}
            </div>
            <div className={classNames('card-body', { 'd-none': !showSearch })}>
                <form className="form" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        {fields.map((fieldItem) => {
                            switch (fieldItem.type) {
                                case 'text':
                                    return (
                                        <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                            <div className="mb-1">
                                                <div key={fieldItem.name}>
                                                    <label htmlFor={fieldItem.name} className="form-label">
                                                        {fieldItem.label}
                                                    </label>
                                                    <Controller
                                                        name={fieldItem.name}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <input type="text" {...field} className="form-control" />
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                case 'select':
                                    return (
                                        <div className={fieldItem.wrapClassName} key={fieldItem.name}>
                                            <div className="mb-1">
                                                <div key={fieldItem.name}>
                                                    <label htmlFor={fieldItem.name} className="form-label">
                                                        {fieldItem.label}
                                                    </label>
                                                    <Controller
                                                        name={fieldItem.name}
                                                        control={control}
                                                        render={({ field }) => (
                                                            <Select
                                                                {...field}
                                                                options={fieldItem.options.choices}
                                                                isMulti={fieldItem.options.multiple}
                                                                onChange={(val) => {
                                                                    field.onChange(val);
                                                                    if (fieldItem.autoSubmit) {
                                                                        // Auto-submit when autoSubmit is true
                                                                        setTimeout(() => {
                                                                            const currentValues = getValues();
                                                                            const updatedValues = {
                                                                                ...currentValues,
                                                                                [fieldItem.name]: val,
                                                                            };
                                                                            onSubmit(updatedValues);
                                                                        }, 0);
                                                                    }
                                                                }}
                                                                value={field.value}
                                                                isClearable
                                                            />
                                                        )}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    );
                                // Other fields ...
                                default:
                                    return null;
                            }
                        })}
                        <div className="col-12">
                            <UpdateButton
                                btnText={t('search')}
                                isLoading={isLoading}
                                hasDivWrap={false}
                                btnClass={['me-1']}
                            />
                            <ResetButton btnText={t('reset')} isLoading={isLoading} handleReset={handleReset} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
