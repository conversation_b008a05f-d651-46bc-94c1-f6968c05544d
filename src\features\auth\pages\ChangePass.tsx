import { ChevronLeft } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import ChangePassForm from '../components/ChangePassForm';

export default function ChangePass() {
    const { t } = useTranslation();

    return (
        <>
            <Helmet>
                <title>{t('resetPassword')}</title>
            </Helmet>
            <div className="d-flex col-lg-4 align-items-center auth-bg px-2 p-lg-5">
                <div className="col-12 col-sm-8 col-md-6 col-lg-12 px-xl-2 mx-auto">
                    <h2 className="card-title fw-bold mb-1 text-center">{t('resetPassword')}</h2>
                    <p className="card-text mb-2 text-center">{t('resetPasswordDesc')}</p>
                    <ChangePassForm />
                    <p className="text-center mt-2">
                        <Link to="/">
                            <ChevronLeft size={14} /> {t('backToLogin')}
                        </Link>
                    </p>
                </div>
            </div>
        </>
    );
}
