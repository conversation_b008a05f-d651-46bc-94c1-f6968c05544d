import classNames from 'classnames';
import { CheckCircle } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Order, { OrderAddress } from 'types/Order';

interface IProps {
    order: Order;
}

export default function OrderInfo({ order }: Readonly<IProps>) {
    const { t } = useTranslation();

    const getAddress = (item?: OrderAddress) =>
        item ? (
            <>
                {item.name} <br />
                {item.address1} <br />
                {/* {item.address2 && (
                    <>
                        {item.address2}
                        <br />
                    </>
                )} */}
                {item.province && (
                    <>
                        {item.province}
                        <br />
                    </>
                )}
                {item.city} {item.zip} <br />
                {item.country}
                {item.latitude && item.longitude && (
                    <>
                        <br />
                        <a
                            href={`https://maps.google.com/?q=${item.latitude},${item.longitude}&t=h&z=17`}
                            target="_blank"
                        >
                            {t('order.viewMap')}
                        </a>
                    </>
                )}
            </>
        ) : (
            <></>
        );

    return (
        <div className="row ms-0">
            <div className="card shadow-none col-md-4 col-12">
                <div className="card-header">
                    <h5 className="card-title m-0">{t('customer.details')}</h5>
                </div>
                <div className="card-body">
                    <div className="d-flex justify-content-start align-items-center mb-6">
                        <div className="d-flex flex-column mb-1">
                            <Link to={`/user/edit/${order.customer_id}`} className="text-body text-nowrap">
                                <h6 className="mb-0">
                                    {order.json.customer.first_name} {order.json.customer.last_name}
                                </h6>
                            </Link>
                        </div>
                    </div>
                    <p className="mb-1">
                        {t('email')}:{' '}
                        <CheckCircle
                            size={14}
                            className={classNames({
                                'text-primary': order.json.customer.verified_email,
                                'text-danger': !order.json.customer.verified_email,
                            })}
                        />{' '}
                        <a href={`mailto:${order.json.customer.email}`} target="_blank">
                            {order.json.customer.email}
                        </a>
                    </p>
                    <p className="mb-0">
                        {t('phoneNumber')}: {order.json.customer.phone}
                    </p>
                </div>
            </div>
            <div className="card shadow-none col-md-4 col-12">
                <div className="card-header d-flex justify-content-between">
                    <h5 className="card-title m-0">{t('order.shippingAddress')}</h5>
                </div>
                <div className="card-body">
                    <p className="mb-0">{getAddress(order.json.shipping_address)}</p>
                </div>
            </div>
            <div className="card shadow-none col-md-4 col-12">
                <div className="card-header d-flex justify-content-between">
                    <h5 className="card-title m-0">{t('order.billingAddress')}</h5>
                </div>
                <div className="card-body">
                    <p className="mb-6">{getAddress(order.json.billing_address)}</p>
                    <h5 className="mb-1">{t('order.paymentGateway')}</h5>
                    {order.payment_gateway_names.map((item, index) => (
                        <p key={index} className="mb-0">
                            {item}
                        </p>
                    ))}
                </div>
            </div>
        </div>
    );
}
