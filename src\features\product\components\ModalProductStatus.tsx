import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useEffect, useLayoutEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ProductService from 'services/ProductService';
import { ProductStatusNames } from 'types/Product';
import { showToast, toggleModalOpen } from 'utils/common';
import * as yup from 'yup';

interface IProps {
    show: boolean;
    productId: string;
    productName: string;
    status: string;
    changeShow: (s: boolean, updatedStatus: boolean) => void;
}

interface FormData {
    status: string;
}

export default function ModalProductStatus({ show, productId, productName, status, changeShow }: Readonly<IProps>) {
    const { t } = useTranslation();
    useLayoutEffect(() => toggleModalOpen(show), [show]);

    const schema = yup
        .object({
            status: yup.string().required(t('error.required')).trim(),
        })
        .required();

    const {
        register,
        handleSubmit,
        reset,
        formState: { errors, isSubmitting },
    } = useForm<FormData>({
        resolver: yupResolver(schema),
        defaultValues: {
            status,
        },
    });
    // Reset form when status prop changes
    useEffect(() => {
        reset({ status });
    }, [status, reset]);

    const onSubmit = async (data: FormData) => {
        const response = await ProductService.updateStatus(productId, data.status);
        showToast(response.success, response.messages);
        if (response.success) {
            changeShow(false, true);
        }
    };

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {t('product.updateStatus')}: {productName}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false, false)} />
                        </div>
                        <form onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12">
                                        <div className="mb-1">
                                            <label className="form-label">
                                                {t('statusName')} <span className="text-danger">*</span>
                                            </label>
                                            <select {...register('status')} className="form-select">
                                                {ProductStatusNames.map((item) => (
                                                    <option key={item.id} value={item.id}>
                                                        {t(`constants.${item.name}`)}
                                                    </option>
                                                ))}
                                            </select>
                                            <span className="error">{errors.status?.message}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={isSubmitting} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
