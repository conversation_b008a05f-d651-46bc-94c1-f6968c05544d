import { useQuery } from '@tanstack/react-query';
import ResetButton from 'components/partials/ResetButton';
import Spinner from 'components/partials/Spinner';
import UpdateButton from 'components/partials/UpdateButton';
import { QUERY_KEY } from 'constants/common';
import { isUndefined, omitBy } from 'lodash';
import { useMemo, useState } from 'react';
import Chart from 'react-apexcharts';
import { Controller, FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select from 'react-select';
import DashboardService from 'services/DashboardService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import WarehouseService from 'services/WarehouseService';
import { ItemStatus } from 'types/common/Item';
import { CountOrderByStatusParam } from 'types/Dashboard';
import { OrderStatusNames } from 'types/Order';
import User, { UserRole } from 'types/User';
import { convertObjectToSelectOptions, showToast } from 'utils/common';
import { validateDateRange } from 'utils/date';
import { SearchWarehouse } from '../../../types/Warehouse';

interface IProps {
    user: User;
}

export default function CountOrderByStatus({ user }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [searchParams, setSearchParams] = useState<CountOrderByStatusParam | null>();
    const { control, handleSubmit, reset } = useForm<FieldValues>();
    const chartOptions = useMemo(
        () => ({
            chart: {
                id: 'count-order-status',
            },
            xaxis: {
                categories: OrderStatusNames.map((status) => t(`constants.${status.name}`)),
            },
        }),
        []
    );

    const { data: shops } = useQuery({
        queryKey: [QUERY_KEY.SHOPS],
        queryFn: async () => ShopService.getList({ status_id__i: ItemStatus.ACTIVE.toString() }),
        enabled: user.role_id === UserRole.ADMIN,
    });

    const { data: warehouses } = useQuery({
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
        enabled: user.role_id === UserRole.ADMIN,
    });

    const { data: manufacturers } = useQuery({
        queryKey: [QUERY_KEY.USERS, UserRole.MANUFACTURER],
        queryFn: () =>
            UserService.getList({
                status_id__i: ItemStatus.ACTIVE.toString(),
                role_id__i: UserRole.MANUFACTURER.toString(),
            }),
        enabled: user.role_id === UserRole.ADMIN,
    });

    const { data: partners } = useQuery({
        queryKey: [QUERY_KEY.USERS, UserRole.PARTNER],
        queryFn: () =>
            UserService.getList({
                status_id__i: ItemStatus.ACTIVE.toString(),
                role_id__i: UserRole.PARTNER.toString(),
            }),
        enabled: user.role_id === UserRole.ADMIN,
    });

    const handleSearch = (formData: FieldValues) => {
        const { order_created_at__ge, order_created_at__le, shop, shop_id, warehouse, warehouse_id } = formData;
        const errorMessage = validateDateRange(order_created_at__ge, order_created_at__le);
        if (errorMessage) {
            showToast(false, [errorMessage]);
            return;
        }
        const dataSearch: CountOrderByStatusParam = {
            order_created_at__ge: order_created_at__ge ? new Date(order_created_at__ge).toISOString() : undefined,
            order_created_at__le: order_created_at__le ? new Date(order_created_at__le).toISOString() : undefined,
            shop_id: shop_id?.value,
            warehouse_id: warehouse_id?.value,
            'warehouse.manufacturer_id': warehouse?.manufacturer_id?.value,
            'shop.partner_id': shop?.partner_id?.value,
        };
        const cleanedData = omitBy(dataSearch, isUndefined);
        setSearchParams((prev) => ({ ...prev, ...cleanedData }));
    };

    const handleReset = () => {
        reset();
        setSearchParams(null);
    };

    const { data, isLoading, isRefetching } = useQuery({
        queryKey: [QUERY_KEY.COUNT_ORDER_BY_STATUS, searchParams],
        queryFn: async ({ queryKey }) => {
            const res = await DashboardService.countOrderByStatus(queryKey[1] as CountOrderByStatusParam);
            if (res) {
                const valuesArray: number[] = [];
                OrderStatusNames.forEach((status) => {
                    //@ts-ignore
                    valuesArray.push(res[status.name] ?? 0);
                });
                return [
                    {
                        name: t('total_order'),
                        data: valuesArray,
                    },
                ];
            }
        },
    });

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{t('countOrderByStatus')}</h4>
            </div>
            <div className="card-body">
                <form onSubmit={handleSubmit(handleSearch)}>
                    <div className="row">
                        <div className="col-12 col-md-3 mb-1">
                            <label className="form-label">{t('startDate')}</label>
                            <Controller
                                name="order_created_at__ge"
                                control={control}
                                render={({ field }) => (
                                    <input {...field} type="date" className="form-control" value={field.value || ''} />
                                )}
                            />
                        </div>
                        <div className="col-12 col-md-3 mb-1">
                            <label className="form-label">{t('endDate')}</label>
                            <Controller
                                name="order_created_at__le"
                                control={control}
                                render={({ field }) => (
                                    <input {...field} type="date" className="form-control" value={field.value || ''} />
                                )}
                            />
                        </div>
                        {user.role_id === UserRole.ADMIN && (
                            <>
                                {shops && (
                                    <div className="col-12 col-md-3 mb-1">
                                        <label className="form-label">{t('shop.single')}</label>
                                        <Controller
                                            name="shop_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    {...field}
                                                    options={convertObjectToSelectOptions(
                                                        shops.items.map((item) => ({
                                                            id: item.id!,
                                                            name: item.name,
                                                        })),
                                                        true
                                                    )}
                                                    onChange={(val) => field.onChange(val)}
                                                    value={field.value}
                                                    isClearable
                                                />
                                            )}
                                        />
                                    </div>
                                )}
                                {warehouses && (
                                    <div className="col-12 col-md-3 mb-1">
                                        <label className="form-label">{t('warehouse.single')}</label>
                                        <Controller
                                            name="warehouse_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    {...field}
                                                    options={convertObjectToSelectOptions(
                                                        warehouses.map((item) => ({
                                                            id: item.id!,
                                                            name: item.name,
                                                        })),
                                                        true
                                                    )}
                                                    onChange={(val) => field.onChange(val)}
                                                    value={field.value}
                                                    isClearable
                                                />
                                            )}
                                        />
                                    </div>
                                )}
                                {manufacturers && (
                                    <div className="col-12 col-md-3 mb-1">
                                        <label className="form-label">{t('manufacturer.single')}</label>
                                        <Controller
                                            name="warehouse.manufacturer_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    {...field}
                                                    options={convertObjectToSelectOptions(
                                                        manufacturers.items.map(
                                                            (item) => ({
                                                                id: item.id!,
                                                                name: `${item.first_name} ${item.last_name}`,
                                                            }),
                                                            true
                                                        )
                                                    )}
                                                    onChange={(val) => field.onChange(val)}
                                                    value={field.value}
                                                    isClearable
                                                />
                                            )}
                                        />
                                    </div>
                                )}
                                {partners && (
                                    <div className="col-12 col-md-3 mb-1">
                                        <label className="form-label">{t('partner.single')}</label>
                                        <Controller
                                            name="shop.partner_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    {...field}
                                                    options={convertObjectToSelectOptions(
                                                        partners.items.map(
                                                            (item) => ({
                                                                id: item.id!,
                                                                name: `${item.first_name} ${item.last_name}`,
                                                            }),
                                                            true
                                                        )
                                                    )}
                                                    onChange={(val) => field.onChange(val)}
                                                    value={field.value}
                                                    isClearable
                                                />
                                            )}
                                        />
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                    <div className="col-md-4 col-12">
                        <UpdateButton
                            btnText={t('search')}
                            isLoading={isLoading}
                            hasDivWrap={false}
                            btnClass={['me-1']}
                        />
                        <ResetButton btnText={t('reset')} isLoading={isLoading} handleReset={handleReset} />
                    </div>
                </form>
            </div>
            <div className="card-body">
                {(isLoading || isRefetching) && <Spinner />}
                {data && <Chart options={chartOptions} series={data} type="bar" width={'100%'} height={320} />}
            </div>
        </div>
    );
}
