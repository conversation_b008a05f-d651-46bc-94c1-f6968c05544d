import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY, REACT_APP_IS_ADMIN, REACT_APP_IS_MANUFACTURER } from 'constants/common';
import ListOrder from 'features/order/components/ListOrder';
import SearchOrderForm from 'features/order/components/SearchOrderForm';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import OrderService from 'services/OrderService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import WarehouseService from 'services/WarehouseService';
import { useAuthStore } from 'stores/authStore';
import { ItemStatus } from 'types/common/Item';
import { SearchOrder, SearchOrderParam } from 'types/Order';
import { SearchShop } from 'types/Shop';
import { SearchUser, UserRole } from 'types/User';
import { showToast } from 'utils/common';
import ModalUpdateWarehouse from '../components/ModalUpdateWarehouse';
import OrderWarehouseCount from '../components/OrderWarehouseCount';
import { SearchWarehouse } from '../../../types/Warehouse';

interface IProps {
    customerId?: string;
    isFromCustomer?: boolean;
}

export default function OrderList({ customerId, isFromCustomer = false }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [isNoWarehouse, setIsNoWarehouse] = useState(false);
    const [show, setShow] = useState(false);
    const [itemId, setItemId] = useState('');
    const user = useAuthStore((state) => state.user);

    const { queryParams, setQueryParams } = useQueryParams<SearchOrderParam>();
    const paramConfig: SearchOrderParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            customer_id__i: customerId ?? queryParams.customer_id__i,
            shop_id__i: queryParams.shop_id__i,
            // financial_status__i: queryParams.financial_status__i,
            // fulfillment_status__i: queryParams.fulfillment_status__i,
            status_id__i: queryParams.status_id__i,
            warehouse_id__i: queryParams.warehouse_id__i,
            partner_id__i: queryParams.partner_id__i,
            is_done_designer: queryParams.is_done_designer,
            designer_id__i: queryParams.designer_id__i,
        },
        isUndefined
    );

    const { data: shops } = useQuery({
        enabled: !!user,
        queryKey: [
            QUERY_KEY.SHOPS,
            {
                status_id__i: ItemStatus.ACTIVE.toString(),
            },
            user,
        ],
        queryFn: ({ queryKey }) => ShopService.getList(queryKey[1] as SearchShop, user!.id),
    });

    const { data: warehouses } = useQuery({
        enabled: !!user && !REACT_APP_IS_MANUFACTURER,
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }, user],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse, user!.id),
    });

    const { data: customers } = useQuery({
        enabled: !!user,
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.CUSTOMER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
            user,
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser, user!.id),
    });

    const { data: partners } = useQuery({
        enabled: REACT_APP_IS_ADMIN,
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.PARTNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data: countOrderNoWarehouse, refetch: refetchCount } = useQuery({
        enabled: REACT_APP_IS_ADMIN && !customerId,
        queryKey: [QUERY_KEY.ORDER_COUNT],
        queryFn: OrderService.countNoWarehouse,
    });

    const { data: designerData } = useQuery({
        enabled: REACT_APP_IS_ADMIN || REACT_APP_IS_MANUFACTURER,
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.DESIGNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
        placeholderData: keepPreviousData,
    });

    const { data, isLoading, isRefetching, refetch } = useQuery({
        enabled: !!user,
        queryKey: [QUERY_KEY.ORDERS, paramConfig, user, isNoWarehouse],
        queryFn: ({ queryKey }) => OrderService.getList(queryKey[1] as SearchOrder, user!.id, isNoWarehouse),
        placeholderData: keepPreviousData,
    });

    const updateMutation = useMutation({
        mutationFn: (id: string) => OrderService.updateWarehouse(itemId, id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShow(false);
                    setItemId('');
                    refetchCount();
                    refetch();
                }
            }
        },
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const handleUpdate = (id: string) => {
        updateMutation.reset();
        setItemId(id);
        setShow(true);
    };

    const updateWarehouse = async (id: string) => {
        if (itemId) {
            updateMutation.mutate(id);
        }
    };

    return (
        <>
            {!customerId && (
                <>
                    <Helmet>
                        <title>{t('order.multiple')}</title>
                    </Helmet>
                    <ContentHeader title={t(`order.multiple`)} />
                </>
            )}
            <div className="content-body">
                <div className="col-12">
                    {REACT_APP_IS_ADMIN && !customerId && (countOrderNoWarehouse ?? 0) > 0 && (
                        <OrderWarehouseCount
                            isNoWarehouse={isNoWarehouse}
                            countOrderNoWarehouse={countOrderNoWarehouse ?? 0}
                            onChange={setIsNoWarehouse}
                        />
                    )}
                    <SearchOrderForm
                        isLoading={isLoading || isRefetching}
                        customerId={customerId}
                        customers={customers?.items ?? []}
                        partners={partners?.items ?? []}
                        shops={shops?.items ?? []}
                        warehouses={warehouses ?? []}
                        designers={designerData?.items ?? []}
                    />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListOrder
                                items={data.items}
                                shops={shops?.items ?? []}
                                warehouses={warehouses ?? []}
                                customers={customers?.items ?? []}
                                partners={partners?.items ?? []}
                                paging={data.pagination}
                                onChooseWarehouse={handleUpdate}
                                onDoneDesigner={() => {}}
                                isFromCustomer={isFromCustomer}
                                designers={designerData?.items ?? []}
                            />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
            <ModalUpdateWarehouse
                show={show}
                warehouses={warehouses ?? []}
                isLoading={updateMutation.isPending}
                changeShow={(s: boolean) => setShow(s)}
                submitAction={updateWarehouse}
            />
        </>
    );
}
