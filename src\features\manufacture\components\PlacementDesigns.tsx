import { useQuery } from '@tanstack/react-query';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { find, isEmpty, uniq } from 'lodash';
import ProductService from 'services/ProductService';
import { DesignItem } from 'types/common/Item';
import { CustomDesign, PlacementNames, ProductDesign } from 'types/Product';
import { insertPlacement } from 'utils/common';
import ProductOwnerDesignPlacement from './ProductOwnerDesignPlacement';
import ProductPlacement from './ProductPlacement';

interface IProps {
    productDesigns: ProductDesign[];
    designs: DesignItem[];
    color: string;
    barcode: string;
    ggFolderId?: string;
    customDesigns?: CustomDesign[];
}

export default function PlacementDesigns({
    productDesigns,
    designs,
    color,
    barcode,
    ggFolderId,
    customDesigns,
}: Readonly<IProps>) {
    const { data, isLoading } = useQuery({
        queryKey: [
            QUERY_KEY.BARCODE,
            uniq(designs.map((item) => item.placement)).map((item) => insertPlacement(barcode, item.toUpperCase())),
        ],
        queryFn: ({ queryKey }) => ProductService.genBarCodes(queryKey[1] as string[]),
        enabled: !isEmpty(designs),
    });
    const { data: customDesignsData, isLoading: isLoadingCustomDesigns } = useQuery({
        queryKey: [
            QUERY_KEY.BARCODE,
            customDesigns?.map((item) => item.placement).map((item) => insertPlacement(barcode, item.toUpperCase())),
        ],
        queryFn: ({ queryKey }) => ProductService.genBarCodes(queryKey[1] as string[]),
        enabled: !isEmpty(customDesigns),
    });

    if (isLoading || isLoadingCustomDesigns) return <Spinner />;
    return (
        <>
            {uniq(designs.map((item) => item.placement)).map((item) => {
                const placementId = find(PlacementNames, { name: item })?.id ?? 0;
                const code = find(data, (it) => it.code === insertPlacement(barcode, item.toUpperCase()));
                const designSizes = productDesigns.filter(
                    (design) => design.color === color && design.placement_id === placementId
                );
                if (placementId > 0 && code) {
                    return (
                        <ProductPlacement
                            key={item}
                            productDesign={designSizes}
                            designs={designs.filter((it) => it.placement === item && it.type !== 'container')}
                            placement={item}
                            code={code}
                            ggFolderId={ggFolderId}
                        />
                    );
                }
            })}
            {customDesigns?.map((customDesign) => {
                const code = find(
                    customDesignsData,
                    (it) => it.code === insertPlacement(barcode, customDesign.placement.toUpperCase())
                );
                if (code) {
                    return (
                        <ProductOwnerDesignPlacement
                            key={customDesign.placement}
                            code={code}
                            customDesign={customDesign}
                            ggFolderId={ggFolderId}
                        />
                    );
                }
            })}
        </>
    );
}
