import { REACT_APP_IS_DESIGNER, REACT_APP_IS_MANUFACTURER, REACT_APP_IS_PARTNER } from 'constants/common';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useLocation, useSearchParams } from 'react-router-dom';
import LoginForm from '../components/LoginForm';

export default function Login() {
    const [url, setUrl] = useState('');
    const { pathname } = useLocation();
    const [searchParams] = useSearchParams();
    const { t } = useTranslation();

    useEffect(() => {
        const uri = searchParams.get('url') ?? '';
        setUrl(uri);
    }, [pathname, searchParams]);

    const getLoginDesc = () => {
        if (REACT_APP_IS_PARTNER) return 'loginPartnerDesc';
        if (REACT_APP_IS_MANUFACTURER) return 'loginManufacturerDesc';
        if (REACT_APP_IS_DESIGNER) return 'loginDesignerDesc';
        return 'loginAdminDesc';
    };

    return (
        <>
            <Helmet>
                <title>{t('login')}</title>
            </Helmet>
            <div className="d-flex col-lg-4 align-items-center auth-bg px-2 p-lg-5">
                <div className="col-12 col-sm-8 col-md-6 col-lg-12 px-xl-2 mx-auto">
                    {/* <h2 className="card-title fw-bold mb-1 text-center">{t('siteName')}</h2> */}
                    <h2 className="card-title fw-bold mb-0 text-center">
                        <img className="img-fluid" src="/assets/images/prp/logo-2.png" alt="" />
                    </h2>
                    <p className="card-text mb-2 text-center">{t(getLoginDesc())}</p>
                    <LoginForm url={url} />
                </div>
            </div>
        </>
    );
}
