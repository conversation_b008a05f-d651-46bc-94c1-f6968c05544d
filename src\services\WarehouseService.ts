import { REACT_APP_IS_MANUFACTURER } from 'constants/common';
import { ApiResponse, DataListResponse } from 'types/common';
import Warehouse, { SearchWarehouse, WarehouseWithManufacturer } from 'types/Warehouse';
import Http from './http.class';

const http = new Http().instance;

const WarehouseService = {
    async getList(params: SearchWarehouse, manufacturerId?: string) {
        if (REACT_APP_IS_MANUFACTURER && manufacturerId) params.manufacturer_id = manufacturerId;
        const { data } = await http.get<ApiResponse<DataListResponse<Warehouse>>>('/warehouse', { params });
        if (data.success) return data.data.items;
    },

    async deleteItem(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/warehouse/${id}`);
        return data;
    },

    async update(param: Partial<Warehouse>, id: string = '') {
        delete param.id;
        const { data } = id
            ? await http.patch<ApiResponse<Warehouse>>(`/warehouse/${id}`, param)
            : await http.post<ApiResponse<Warehouse>>('/warehouse', param);
        return data;
    },

    async updateStock(
        id: string,
        param: { id?: string; product_child_id: string; quantity: number; quantity_draft: number }
    ) {
        const { data } = await http.post<ApiResponse<{}>>(`/warehouse/${id}/stock`, param);
        return data;
    },

    async getWithManufacturer(warehouseId: string) {
        const { data } = await http.get<ApiResponse<WarehouseWithManufacturer>>(
            `/warehouse/${warehouseId}/manufacturer`
        );
        if (data.success) return data.data;
    },
};

export default WarehouseService;
