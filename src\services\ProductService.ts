import { find, join } from 'lodash';
import { ApiResponse, DataListResponse } from 'types/common';
import { Barcode } from 'types/common/Item';
import Product, { DesignPosition, ProductDesign, ProductWeight, SearchDesign, SearchProduct } from 'types/Product';
import { TransferGoodBarcodeProduct } from 'types/TransferGood';
import Warehouse from 'types/Warehouse';
import Http from './http.class';

const http = new Http().instance;
const httpForZip = new Http('application/json', 'blob').instance;

const getListChildren = async (ids?: string[], orderId?: string, productId?: string) => {
    const { data } = await http.get<ApiResponse<DataListResponse<TransferGoodBarcodeProduct>>>('/product-child', {
        params: { id__i: ids ? join(ids, ',') : undefined, order_id: orderId, product_id: productId },
    });
    return data;
};

const ProductService = {
    async getList(searchParams: SearchProduct, stockWarning: boolean, warehouses: Warehouse[]) {
        const params = { ...searchParams };
        if (stockWarning) {
            if (params.warehouse_id) {
                const item = find(warehouses, { id: params.warehouse_id });
                if (item) params.s_quantity_minimum = item.quantity_minimum;
            } else {
                params.warehouse_id = warehouses![0].id!;
                params.s_quantity_minimum = warehouses![0].quantity_minimum;
            }
        }
        if (params.warehouse_id) {
            params['children.stocks.warehouse_id'] = params.warehouse_id;
            delete params.warehouse_id;
        }
        if (params.sku) {
            params['children.sku__l'] = params.sku;
            delete params.sku;
        }
        const { data } = await http.get<ApiResponse<DataListResponse<Product>>>('/product', { params });
        if (data.success) return data.data;
    },

    getListChildren,

    async getArrayChildren(ids: string[], orderId?: string) {
        const data = await getListChildren(ids, orderId);
        return data.success ? data.data.items : [];
    },

    async getBySku(sku: string) {
        const { data } = await http.get<ApiResponse<TransferGoodBarcodeProduct>>(`/product/sku/${sku}`);
        return data;
    },

    async syncProducts(shopId: string) {
        const { data } = await http.get<ApiResponse<{}>>(`/product/sync/${shopId}`);
        return data;
    },

    async getDesigns(productId: string) {
        const { data } = await http.get<ApiResponse<ProductDesign[]>>(`/product/${productId}/design`);
        if (data.success) return data.data;
    },

    async getListDesign(params: SearchDesign) {
        const { data } = await http.get<ApiResponse<DataListResponse<ProductDesign>>>('/product/designs', { params });
        if (data.success) return data.data;
    },

    async addDesign(productId: string, param: ProductDesign) {
        const { data } = await http.patch<ApiResponse<ProductDesign>>(`/product/${productId}/design`, param);
        return data;
    },

    async deleteDesign(id: string) {
        const { data } = await http.delete<ApiResponse<{}>>(`/product/design/${id}`);
        return data;
    },

    async downloadMockupFile(productId: string, variantId: string) {
        return httpForZip.get<Blob>(`/product/customer-design/${productId}/${variantId}/image-zip`);
    },

    async updateTemplate(id: string, designTemplateId: string, rootId: string, design_template_json: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/product/${id}/template`, {
            design_template_id: designTemplateId,
            root_id: rootId,
            design_template_json,
        });
        return data;
    },

    async genBarCodes(codes: string[]) {
        const { data } = await http.post<ApiResponse<Barcode[]>>('/file/convert-to-barcode', { codes });
        return data.data;
    },

    async getDesignSizes() {
        const { data } = await http.get<ApiResponse<string[]>>('/product-design/sizes');
        return data.data ?? [];
    },

    async getDesignCodes() {
        const { data } = await http.get<ApiResponse<string[]>>('/product-design/codes');
        return data.data ?? [];
    },

    async updateDesignSizes({
        position,
        ids,
        code = '',
    }: Readonly<{ position: DesignPosition; ids: string[]; code?: string }>) {
        const { data } = await http.patch<ApiResponse<{}>>('/product-design', { ids, position, code });
        return data;
    },

    async changeDesignOk(flag: boolean, ids: string[]) {
        const { data } = await http.patch<ApiResponse<{}>>('/product-design/is-ok', { ids, is_design_ok: flag });
        return data;
    },

    async deleteDesigns(ids: string[]) {
        const { data } = await http.post<ApiResponse<{}>>('/product-design/delete-list', { ids });
        return data;
    },

    async saveProductToNewType(code: string, ids: string[]) {
        const { data } = await http.patch<ApiResponse<{}>>('/product-design/code', { ids, code });
        return data;
    },

    async getProductWeight(tag: string) {
        const { data } = await http.get<ApiResponse<ProductWeight[]>>(`/product-weight?tag=${tag}`);
        return data.data ?? [];
    },

    async updatePreset(preset: string, id: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/product-child/${id}/preset`, { preset });
        return data;
    },

    async updatePartnerPrice(price: number, id: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/product-child/${id}/partner-price`, {
            partner_price: price,
        });
        return data;
    },

    async updateChildRoot(childId: string, rootId: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/product-child/${childId}/root`, { root_id: rootId });
        return data;
    },

    async assignProductToShop(productId: string, shopIds: string[]) {
        const { data } = await http.patch<ApiResponse<{}>>(`/product/${productId}/tags`, { tags: shopIds });
        return data;
    },

    async updateStatus(productId: string, status: string) {
        const { data } = await http.patch<ApiResponse<{}>>(`/product/${productId}/shopify-status`, { status });
        return data;
    },
};

export default ProductService;
