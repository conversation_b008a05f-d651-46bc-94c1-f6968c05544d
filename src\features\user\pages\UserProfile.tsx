import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import { useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { includes } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import UserService from 'services/UserService';
import ChangePassForm from '../components/ChangePassForm';
import Setup2FA from '../components/Setup2FA';
import UpdateUserForm from '../components/UpdateUserForm';

export default function UserProfile() {
    const navigate = useNavigate();
    const { hash } = useLocation();
    const { t } = useTranslation();
    const [tabId, setTabId] = useState('info');

    useEffect(() => {
        if (hash) {
            let hashId = hash.replace('#', '');
            if (!includes(['info', 'password'], hashId)) hashId = 'info';
            setTabId(hashId);
        }
    }, [hash]);

    const getUser = async () => {
        const response = await UserService.getProfile();
        if (response.success) return response.data;
        return null;
    };

    const {
        data: user,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: [QUERY_KEY.PROFILE],
        queryFn: () => getUser(),
    });

    const handleTabChange = async (_event: React.SyntheticEvent, tab: string) => {
        setTabId(tab);
        navigate('#' + tab);
    };

    return (
        <>
            <Helmet>
                <title>{t('profile')}</title>
            </Helmet>
            <ContentHeader title={t('profile')} />
            <div className="content-body">
                <div className="col-12">
                    {isLoading && <Spinner />}
                    {!isLoading && user && (
                        <Box sx={{ width: '100%', typography: 'body1' }}>
                            <TabContext value={tabId}>
                                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                    <TabList onChange={handleTabChange} className="nav nav-pills mb-2">
                                        <Tab label={t('baseInformation')} value="info" />
                                        <Tab label={t('security')} value="password" />
                                    </TabList>
                                </Box>
                                <TabPanel value="info" style={{ padding: 0 }}>
                                    <UpdateUserForm
                                        id={user.id ?? ''}
                                        roleId={user.role_id}
                                        user={user}
                                        isProfile={true}
                                    />
                                </TabPanel>
                                <TabPanel value="password" style={{ padding: 0 }}>
                                    <ChangePassForm id={user.id ?? ''} />
                                    <Setup2FA
                                        id={user.id ?? ''}
                                        twoFactorEnabled={user?.two_factor_enabled ?? false}
                                        refetchProfile={refetch}
                                    />
                                </TabPanel>
                            </TabContext>
                        </Box>
                    )}
                </div>
            </div>
        </>
    );
}
