import { TransferGoodBarcodeProduct } from '../../../types/TransferGood';
import { useTranslation } from 'react-i18next';
import ProductColumnInfo from '../../../components/partials/ProductColumnInfo';
import { Printer } from 'react-feather';

interface IProps {
    items: TransferGoodBarcodeProduct[];
    onPrint: (item: TransferGoodBarcodeProduct) => void;
}

export default function ListTransferGoodOrderProduct({ items, onPrint }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('quantity')}</th>
                        <th className="text-center">{t('code')}</th>
                        <th>{t('style')}</th>
                        <th className="text-center">{t('barcode')}</th>
                        <th className="thAction1"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item, index) => (
                        <tr key={item.product_child_id}>
                            <td className="text-center">{item.quantity}</td>
                            <ProductColumnInfo item={item} />
                            <td className="text-center">
                                <Printer size={14} className="cursor-pointer" onClick={() => onPrint(item)} />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
