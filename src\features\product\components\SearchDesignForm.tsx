import SearchForm from 'components/partials/SearchForm';
import { useTranslation } from 'react-i18next';
import { YesNoChoices } from 'types/common/Item';
import { DesignPlacementNames, PlacementNames } from 'types/Product';
import User from 'types/User';
import { convertConstantToSelectOptions, convertObjectToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
    suppliers: User[];
    sizes: string[];
    codes: string[];
}

export default function SearchDesignForm({ isLoading, suppliers, sizes, codes }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <SearchForm
            fields={[
                { name: 'product_id__i', type: 'text', label: 'id', wrapClassName: 'col-md-4 col-12' },
                { name: 'color', type: 'text', label: t('color'), wrapClassName: 'col-md-4 col-12' },
                {
                    name: 'code__i',
                    type: 'select',
                    label: t('code'),
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertObjectToSelectOptions(
                            codes.filter((item) => !!item).map((item) => ({ id: item, name: item }))
                        ),
                    },
                },
                {
                    name: 'placement_id__i',
                    type: 'select',
                    label: t('design.placement') + ' 1',
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertSelectToStringKey(convertConstantToSelectOptions(PlacementNames, t, true)),
                    },
                },
                {
                    name: 'placement__i',
                    type: 'select',
                    label: t('design.placement') + ' 2',
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertObjectToSelectOptions(DesignPlacementNames, true, t),
                    },
                },
                {
                    name: 'size__i',
                    type: 'select',
                    label: t('size'),
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertObjectToSelectOptions(
                            sizes.filter((item) => !!item).map((item) => ({ id: item, name: item }))
                        ),
                    },
                },
                {
                    name: 'product.vendor__i',
                    type: 'select',
                    label: t('vendor'),
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: true,
                        choices: convertObjectToSelectOptions(
                            suppliers.map((item) => ({ id: item.last_name ?? item.first_name, name: item.first_name }))
                        ),
                    },
                },
                {
                    name: 'is_design_ok',
                    type: 'select',
                    label: t('design.done'),
                    wrapClassName: 'col-md-4 col-12',
                    options: {
                        multiple: false,
                        choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
                    },
                },
            ]}
            isLoading={isLoading}
        />
    );
}
