import classNames from 'classnames';
import { useOutsideClick } from 'hooks/useOutsideClick';
import { useEffect, useState } from 'react';
import { Code } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { TransferGoodBarcodeProduct } from 'types/TransferGood';
import { replace } from 'lodash';

interface IProps {
    onScan: (code: string, products?: TransferGoodBarcodeProduct[]) => void;
    products?: TransferGoodBarcodeProduct[];
    hideScanArea?: boolean;
    hideInput?: boolean;
}

export default function ScanZone({ onScan, products, hideScanArea = false, hideInput = false }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [focus, setFocus] = useState(false);
    const [barcode, setBarcode] = useState('');
    const ref = useOutsideClick(() => setFocus(false));

    useEffect(() => {
        let code = '';
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Enter') {
                if (code) onScan(replace(code, 'CapsLock', ''), products);
                code = '';
                return;
            }
            if (event.key !== 'Shift') code = code + event.key;
        };

        const currentRef = ref.current;
        if (currentRef) {
            currentRef.focus();
            setFocus(true);
            currentRef.addEventListener('keydown', handleKeyDown);
        }

        return () => {
            if (currentRef) {
                currentRef.removeEventListener('keydown', handleKeyDown);
                code = '';
            }
        };
    }, [products]);

    return (
        <>
            {!hideScanArea && (
                <div
                    className={classNames('card', { 'card-congratulations': focus })}
                    tabIndex={0}
                    ref={ref}
                    onFocus={() => setFocus(true)}
                >
                    <div className="card-body text-center">
                        <h1 className={classNames('mb-1', { 'text-white': focus })}>{t('scan.tipTitle')}</h1>
                        <p className="card-text m-auto w-75">{t('scan.tipDescription')}</p>
                    </div>
                </div>
            )}
            {!hideInput && (
                <div className="row mb-1">
                    <div className="col-md-4" />
                    <div className="col-md-4 col-12">
                        <div className="input-group input-group-sm">
                            <button
                                className="btn btn-outline-primary waves-effect"
                                type="button"
                                onClick={() => {
                                    if (barcode) onScan(replace(barcode, 'CapsLock', ''), products);
                                    setBarcode('');
                                }}
                            >
                                <Code size={24} />
                            </button>
                            <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder={t(!hideScanArea ? 'barcode' : 'orderCode')}
                                value={barcode}
                                onChange={(e) => setBarcode(e.target.value.trim())}
                            />
                            <button
                                className="btn btn-outline-primary waves-effect"
                                type="button"
                                onClick={() => {
                                    if (barcode) onScan(replace(barcode, 'CapsLock', ''), products);
                                    setBarcode('');
                                }}
                            >
                                {t('search')}
                            </button>
                        </div>
                    </div>
                    <div className="col-md-4" />
                </div>
            )}
        </>
    );
}
