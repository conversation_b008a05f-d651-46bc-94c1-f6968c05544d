import classNames from 'classnames';
import { Fragment, useLayoutEffect } from 'react';
import { ExternalLink } from 'react-feather';
import { useTranslation } from 'react-i18next';
import { DesignItem } from 'types/common/Item';
import { toggleModalOpen } from 'utils/common';

interface IProps {
    show: boolean;
    designs: DesignItem[];
    placement: string;
    ggFolderId?: string;
    changeShow: (s: boolean) => void;
}

export default function ModalProductPlacement({ show, designs, placement, ggFolderId, changeShow }: Readonly<IProps>) {
    useLayoutEffect(() => toggleModalOpen(show), [show]);
    const { t } = useTranslation();

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">
                                {t(`constants.${placement}`)}
                                {ggFolderId && (
                                    <a
                                        href={`https://drive.google.com/drive/folders/${ggFolderId}`}
                                        className="ms-1"
                                        target="_blank"
                                    >
                                        <ExternalLink size={14} />
                                    </a>
                                )}
                            </h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false)} />
                        </div>
                        <div className="modal-body">
                            <div className="table-responsive">
                                <table className="table">
                                    <thead>
                                        <tr>
                                            <th className="text-center">{t('no.')}</th>
                                            <th className="text-center">{t('design.file')}</th>
                                            <th>{t('properties')}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {designs
                                            .filter((item) => item.type === 'layer')
                                            .map((item, index) => (
                                                <tr key={index}>
                                                    <td className="text-center">{index + 1}</td>
                                                    <td className="text-center">
                                                        <img
                                                            src={item.url}
                                                            className="uploadedAvatar rounded me-50"
                                                            alt=""
                                                            height={100}
                                                            width={100}
                                                        />
                                                    </td>
                                                    <td>
                                                        {item.properties &&
                                                            Object.entries(item.properties).map(([k, v]) => (
                                                                <Fragment key={k}>
                                                                    {`${k}: ${
                                                                        k === 'width' || k === 'height'
                                                                            ? v //customP2C(Number(v)) + 'cm'
                                                                            : v
                                                                    }`}
                                                                    <br />
                                                                </Fragment>
                                                            ))}
                                                    </td>
                                                </tr>
                                            ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
}
