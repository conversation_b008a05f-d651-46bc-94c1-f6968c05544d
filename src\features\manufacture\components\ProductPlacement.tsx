import { useQuery } from '@tanstack/react-query';
import classNames from 'classnames';
import Barcode from 'components/partials/Barcode';
import { includes, isEmpty } from 'lodash';
import { useMemo, useState } from 'react';
import { ExternalLink, Eye, Printer } from 'react-feather';
import { useTranslation } from 'react-i18next';
import ProductService from 'services/ProductService';
import { DesignItem, Barcode as IBarcode } from 'types/common/Item';
import { Placement, PlacementNames, ProductDesign } from 'types/Product';
import FileService from '../../../services/FileService';
import TransferGoodService from '../../../services/TransferGoodService';
import { showToast } from '../../../utils/common';
import ModalProductPlacement from './ModalProductPlacement';

interface IProps {
    productDesign?: ProductDesign[];
    designs: DesignItem[];
    placement: string;
    code: IBarcode;
    ggFolderId?: string;
}

interface IProductDesc {
    id: Placement;
    width: number;
    height: number;
}

function convertSize(
    defaultWidthCm: number,
    defaultHeightCm: number,
    defaultWitdthPx: number,
    defaultHeightPx: number,
    width: number,
    height: number
) {
    const convertWidth = defaultWidthCm / defaultWitdthPx;
    const convertHeight = defaultHeightCm / defaultHeightPx;
    return { width: Math.round(width * convertWidth), height: Math.round(height * convertHeight) };
}

export default function ProductPlacement({ productDesign, designs, placement, code, ggFolderId }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [show, setShow] = useState(false);
    const [loading, setLoading] = useState(false);
    const designItems = designs.filter((design) => design.type === 'design');
    const productCode = useMemo(
        () => (productDesign && productDesign.length > 0 ? productDesign[0].code : ''),
        [productDesign]
    );

    const { data: productWeightData } = useQuery({
        queryKey: ['PRODUCT_WEIGHT', productCode],
        queryFn: () => ProductService.getProductWeight(productCode),
    });

    const productDesc: IProductDesc[] = useMemo(
        () => (isEmpty(productWeightData?.[0]?.desc) ? [] : JSON.parse(productWeightData![0].desc)),
        [productWeightData]
    );

    const imgSizes = useMemo(() => {
        switch (designItems[0].placement) {
            case 'front':
                return productDesc.filter((item: IProductDesc) => item.id === Placement.FRONT);
            case 'back':
                return productDesc.filter((item: IProductDesc) => item.id === Placement.BACK);
            case 'left_sleeve':
                return productDesc.filter((item: IProductDesc) => item.id === Placement.LEFT_SLEEVE);
            case 'right_sleeve':
                return productDesc.filter((item: IProductDesc) => item.id === Placement.RIGHT_SLEEVE);
            default:
                return [];
        }
    }, [designItems, productDesc]);

    const onPrint = async () => {
        setLoading(true);
        const htmlPrint = TransferGoodService.generateProductCard('', '', '', '', '', code.code, code.image, true);
        const response = await FileService.exportPdf(htmlPrint, false);
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
        setLoading(false);
    };

    return (
        <>
            <div className="alert alert-dark rounded-0">
                <div className="alert-body d-flex justify-content-between flex-md-row flex-column">
                    <div className="text-uppercase">{t(`constants.${placement}`)}</div>
                    <div>
                        <Eye size={14} className="text-primary cursor-pointer" onClick={() => setShow(true)} />
                        {ggFolderId && (
                            <a
                                href={`https://drive.google.com/drive/folders/${ggFolderId}`}
                                className="ms-1"
                                target="_blank"
                            >
                                <ExternalLink size={14} />
                            </a>
                        )}
                    </div>
                </div>
            </div>
            {designItems.length > 0 && productDesign && (
                <div className="d-flex justify-content-between flex-md-row flex-column">
                    <div className="d-flex">
                        {designItems.map((design, index) => {
                            const layer = designs.find(
                                (it) =>
                                    it.designId === design.designId &&
                                    it.properties?.id === design.layerId &&
                                    it.type === 'layer'
                            );
                            const placementId = PlacementNames.find((it) => it.name === design.placement)?.id;
                            const productDesignFiltered = productDesign.find((it) => it.placement_id === placementId);
                            const { width, height } = convertSize(
                                (imgSizes[index] ?? imgSizes[0])?.width,
                                (imgSizes[index] ?? imgSizes[0])?.height,
                                productDesignFiltered?.position?.w ?? 0,
                                productDesignFiltered?.position?.h ?? 0,
                                layer?.properties?.width ?? 0,
                                layer?.properties?.height ?? 0
                            );
                            return (
                                <div className="demo-inline-spacing ps-1" key={`${design.designId}-${index}`}>
                                    <div className="m-0">
                                        <img
                                            className="order-image-position border-1 border-dark"
                                            src={design.url}
                                            alt=""
                                        />
                                        {imgSizes.length > 0 && (
                                            <p className="text-center">
                                                {t('size')}: {width}cm x {height}cm
                                            </p>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                    <div>
                        <p>
                            <strong>{t('design.sizeAndPosition')}:</strong>
                            <Printer
                                size={14}
                                className={classNames(`ms-2`, {
                                    'cursor-pointer': !loading,
                                    'pointer-events-none': loading,
                                })}
                                onClick={onPrint}
                            />
                        </p>
                        <p>
                            {t('location.single')}: {includes(['front', 'back'], placement) && `${t('centre')} `}
                            {t(`constants.${placement}`)}
                        </p>
                        <p>
                            <Barcode image={code.image} code={code.code} />
                        </p>
                    </div>
                    <div />
                    <div />
                </div>
            )}
            <ModalProductPlacement
                show={show}
                designs={designs}
                placement={placement}
                ggFolderId={ggFolderId}
                changeShow={(s: boolean) => setShow(s)}
            />
        </>
    );
}
