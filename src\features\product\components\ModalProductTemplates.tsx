import { yupResolver } from '@hookform/resolvers/yup/dist/yup';
import classNames from 'classnames';
import ModalTemplateDetail, { ModalTemplateDetailRef } from 'features/template/components/ModalTemplateDetail';
import { isEmpty } from 'lodash';
import { ChangeEvent, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Edit, PlusCircle, Trash2 } from 'react-feather';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select from 'react-select';
import FileService from 'services/FileService';
import * as yup from 'yup';
import UpdateButton from '../../../components/partials/UpdateButton';
import ProductService from '../../../services/ProductService';
import DesignTemplate from '../../../types/DesignTemplate';
import Product, { DesignTemplatesType, ProductPlacementOptions, TemplatePlacementNames } from '../../../types/Product';
import { getSelectStyle, isValidImageFile, showToast, toggleModalOpen } from '../../../utils/common';
import './ModalProductTemplate.css';

interface IProps {
    show: boolean;
    productId: string;
    productName: string;
    templateId: string;
    rootId: string;
    templates: DesignTemplate[];
    rootProducts: Product[];
    changeShow: (s: boolean, updatedTemplate: boolean) => void;
    designTemplates: DesignTemplatesType[];
}

export default function ModalProductTemplates({
    show,
    productId,
    productName,
    templateId,
    rootId,
    templates,
    rootProducts,
    changeShow,
    designTemplates,
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const [isOpenImageModal, setIsOpenImageModal] = useState(false);
    const templateDetailModalRef = useRef<ModalTemplateDetailRef>();
    const [selectedRowIndex, setSelectedRowIndex] = useState<number>(0);
    const uploadProductImageRef = useRef<HTMLInputElement | null>(null);

    const schema = yup
        .object({
            root_id: yup.string().required(t('error.required')).trim(),
            design_template_id: yup.string().required(t('error.required')).trim(),
            sizes: yup
                .array()
                .of(
                    yup.object({
                        placement: yup.string().required(),
                        image: yup.string().required(),
                        width: yup.string().required(),
                        height: yup.string().required(),
                    })
                )
                .min(1, t('error.sizes_required', 'At least one row is required'))
                .test(
                    'unique-placement',
                    t('error.placement_unique', 'Placement must be unique in all rows'),
                    (value) => {
                        if (!Array.isArray(value)) return true;
                        const placements = value.map((row) => row.placement).filter(Boolean);
                        return new Set(placements).size === placements.length;
                    }
                ),
            placements: yup
                .array()
                .of(
                    yup.object({
                        placement: yup.string().required(),
                        image: yup.mixed().test('file', 'Image is required', function (value) {
                            if (!value) return false;
                            // Accept File (from file input)
                            if (value instanceof File) return true;
                            // Accept string (e.g., existing image URL)
                            if (typeof value === 'string' && value.trim() !== '') return true;
                            return false;
                        }),
                    })
                )
                .min(1, t('error.placements_required', 'At least one row is required'))
                .test(
                    'unique-product-placement',
                    t('error.product_placement_unique', 'Placement must be unique in all rows'),
                    (value) => {
                        if (!Array.isArray(value)) return true;
                        const placements = value.map((row) => row.placement).filter(Boolean);
                        return new Set(placements).size === placements.length;
                    }
                ),
        })
        .required();
    type FormType = yup.InferType<typeof schema>;
    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
        reset,
        watch,
    } = useForm<FormType>({
        resolver: yupResolver(schema),
        defaultValues: {
            sizes: [{ placement: '', image: '', width: '', height: '' }],
            root_id: '',
            design_template_id: '',
            placements: [{ placement: '', image: '' }],
        },
        mode: 'onChange',
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'sizes',
    });
    const {
        fields: placementFields,
        append: placementAppend,
        remove: placementRemove,
    } = useFieldArray({
        control,
        name: 'placements',
    });

    const designTemplateId = watch('design_template_id');

    const rootProductsOptions = useMemo(
        () =>
            rootProducts.map((e) => ({
                label: e.title,
                value: e.id,
            })),
        [rootProducts]
    );
    const templatesOptions = useMemo(
        () =>
            templates.map((e) => ({
                label: e.name,
                value: e.id,
            })),
        [templates]
    );

    const placementOptions = useMemo(
        () =>
            TemplatePlacementNames.map((e) => ({
                label: e.label,
                value: e.value.toString(),
            })),
        [TemplatePlacementNames]
    );

    const productPlacementOptions = useMemo(
        () =>
            ProductPlacementOptions.map((e) => ({
                label: e.label,
                value: e.value.toString(),
            })),
        [ProductPlacementOptions]
    );

    const onSubmit = async (data: FormType) => {
        // 1. Find all placements with a File to upload
        const placementsWithFiles =
            data.placements?.map((p, idx) => ({ ...p, idx })).filter((p) => isValidImageFile(p.image)) || [];

        // 2. Upload all files
        const uploadResults = await Promise.all(placementsWithFiles.map((p) => FileService.upload(p.image)));

        // 3. Replace image field with uploaded file URL or ID
        const updatedPlacements = data?.placements?.map((placement, idx) => {
            const fileIdx = placementsWithFiles.findIndex((p) => p.idx === idx);
            if (fileIdx !== -1) {
                // Use uploadResults[fileIdx].data.url or .data.id as needed
                return {
                    ...placement,
                    image: uploadResults[fileIdx].data.url, // or .id if backend expects ID
                };
            }
            return placement;
        });
        const response = await ProductService.updateTemplate(
            productId,
            data.design_template_id,
            data.root_id,
            JSON.stringify([...(updatedPlacements || []), ...(data.sizes || [])])
        );
        showToast(response.success, response.messages);
        if (response.success) changeShow(false, true);
    };

    const addSize = () => {
        append({ placement: '', image: '', width: '', height: '' });
    };
    const addPlacement = () => {
        placementAppend({ placement: '', image: '' });
    };

    const onCloseImageModal = () => {
        setIsOpenImageModal(false);
        setSelectedRowIndex(0);
    };

    const onOpenImageModal = (index: number) => {
        setIsOpenImageModal(true);
        setSelectedRowIndex(index);
    };

    const onSaveImage = () => {
        const selectedImage = templateDetailModalRef.current?.getSelectedImage();

        if (selectedImage) {
            setValue(`sizes.${selectedRowIndex}.image`, selectedImage);
        }

        onCloseImageModal();
    };

    const onOpenUploadFile = (index: number) => {
        setSelectedRowIndex(index);
        uploadProductImageRef.current?.click();
    };

    const onFileChange = (e: ChangeEvent<HTMLInputElement> | undefined) => {
        const file = isEmpty(e?.target.files) ? undefined : e?.target.files?.[0];

        if (!file) {
            return setSelectedRowIndex(0);
        }

        const type = file.type;

        if (!type.startsWith('image/')) return setSelectedRowIndex(0);

        setValue(`placements.${selectedRowIndex}.image`, file);
    };

    useEffect(() => {
        if (!templateId || !rootId) return;

        const sizes = !isEmpty(designTemplates)
            ? designTemplates.filter((t) => placementOptions.some((otp) => otp.value === t.placement))
            : [{ placement: '', image: '', width: '', height: '' }];

        const placements = !isEmpty(designTemplates)
            ? designTemplates.filter((t) => productPlacementOptions.some((otp) => otp.value === t.placement))
            : [{ placement: '', image: '' }];

        reset({
            sizes,
            placements,
            root_id: rootId,
            design_template_id: templateId,
        });
    }, [templateId, rootId, designTemplates, productPlacementOptions, placementOptions]);

    useLayoutEffect(() => toggleModalOpen(show), [show]);

    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h5 className="modal-title">{t('template.of', { data: productName })}</h5>
                            <button type="button" className="btn-close" onClick={() => changeShow(false, false)} />
                        </div>
                        <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                            <div className="modal-body">
                                <div className="row">
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            {t('product.root')} <span className="error">*</span>
                                        </label>
                                        <Controller
                                            name="root_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    options={rootProductsOptions}
                                                    onChange={(newValue) => field.onChange(newValue?.value)}
                                                    value={rootProductsOptions.find((e) => e.value === field.value)}
                                                    styles={{
                                                        control: (baseStyles, state) =>
                                                            getSelectStyle(
                                                                baseStyles,
                                                                state,
                                                                !!errors.root_id?.message
                                                            ),
                                                    }}
                                                />
                                            )}
                                        />
                                        <span className="error">{errors.root_id?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <label className="form-label">
                                            {t('template.single')} <span className="error">*</span>
                                        </label>
                                        <Controller
                                            name="design_template_id"
                                            control={control}
                                            render={({ field }) => (
                                                <Select
                                                    options={templatesOptions}
                                                    onChange={(newValue) => field.onChange(newValue?.value)}
                                                    value={templatesOptions.find((e) => e.value === field.value)}
                                                    styles={{
                                                        control: (baseStyles, state) =>
                                                            getSelectStyle(
                                                                baseStyles,
                                                                state,
                                                                !!errors.design_template_id?.message
                                                            ),
                                                    }}
                                                />
                                            )}
                                        />
                                        <span className="error">{errors.design_template_id?.message}</span>
                                    </div>
                                    <div className="col-12 mb-1">
                                        <div className="table-responsive">
                                            <table className="table">
                                                <thead>
                                                    <tr>
                                                        <th
                                                            className="text-center"
                                                            style={{
                                                                width: 224,
                                                                verticalAlign: 'middle',
                                                            }}
                                                        >
                                                            {t('design.placement')}
                                                        </th>
                                                        <th
                                                            style={{
                                                                verticalAlign: 'middle',
                                                            }}
                                                            className="text-center"
                                                        >
                                                            Image
                                                        </th>
                                                        <th
                                                            className="text-center"
                                                            style={{
                                                                width: 50,
                                                                verticalAlign: 'middle',
                                                            }}
                                                        >
                                                            <button
                                                                type="button"
                                                                title={t('add')}
                                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                onClick={addPlacement}
                                                            >
                                                                <PlusCircle size={14} />
                                                            </button>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {placementFields.map((field, index) => (
                                                        <tr key={field.id}>
                                                            <td className="text-center">
                                                                <Controller
                                                                    control={control}
                                                                    name={`placements.${index}.placement`}
                                                                    render={({ field }) => (
                                                                        <Select
                                                                            options={productPlacementOptions}
                                                                            onChange={(newValue) =>
                                                                                field.onChange(newValue?.value)
                                                                            }
                                                                            value={productPlacementOptions.find(
                                                                                (e) => e.value === field.value
                                                                            )}
                                                                            styles={{
                                                                                menuPortal: (baseStyles) => ({
                                                                                    ...baseStyles,
                                                                                    zIndex: 1056,
                                                                                }),
                                                                            }}
                                                                            menuPortalTarget={document.body}
                                                                        />
                                                                    )}
                                                                />
                                                            </td>
                                                            <td className="text-center">
                                                                <Controller
                                                                    name={`placements.${index}.image`}
                                                                    control={control}
                                                                    render={({ field }) => (
                                                                        <div
                                                                            style={{
                                                                                gap: 4,
                                                                            }}
                                                                            className="flex-row d-flex align-items-center justify-content-center"
                                                                        >
                                                                            {field.value && (
                                                                                <img
                                                                                    style={{
                                                                                        width: 60,
                                                                                        height: 60,
                                                                                        borderRadius: 8,
                                                                                    }}
                                                                                    src={
                                                                                        isValidImageFile(field.value)
                                                                                            ? URL.createObjectURL(
                                                                                                  field.value
                                                                                              )
                                                                                            : field.value
                                                                                    }
                                                                                />
                                                                            )}
                                                                            <button
                                                                                type="button"
                                                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                                onClick={() => onOpenUploadFile(index)}
                                                                            >
                                                                                {field.value ? (
                                                                                    <Edit size={14} />
                                                                                ) : (
                                                                                    <PlusCircle size={14} />
                                                                                )}
                                                                            </button>
                                                                        </div>
                                                                    )}
                                                                />
                                                            </td>
                                                            <td className="text-center">
                                                                <button
                                                                    type="button"
                                                                    title={t('delete')}
                                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                                    onClick={() => placementRemove(index)}
                                                                >
                                                                    <Trash2 size={14} />
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                        <input
                                            onChange={onFileChange}
                                            ref={uploadProductImageRef}
                                            type="file"
                                            style={{ display: 'none' }}
                                            accept="image/*"
                                            multiple={false}
                                        />
                                        {/* Show error under table if any row is missing a required field */}
                                        {Array.isArray(errors.placements) &&
                                            errors.placements.some(
                                                (rowErr) => rowErr && (rowErr.placement || rowErr.image)
                                            ) && <div className="error mt-1">{t('error.required')}</div>}
                                        {errors.placements?.message && (
                                            <div className="error mt-1">{errors.placements.message}</div>
                                        )}
                                    </div>
                                    <div className="col-12 mb-1">
                                        <div className="table-responsive">
                                            <table className="table">
                                                <thead>
                                                    <tr>
                                                        <th
                                                            className="text-center"
                                                            style={{
                                                                width: 224,
                                                                verticalAlign: 'middle',
                                                            }}
                                                        >
                                                            {t('design.placement')}
                                                        </th>
                                                        <th
                                                            style={{
                                                                verticalAlign: 'middle',
                                                            }}
                                                            className="text-center"
                                                        >
                                                            Image
                                                        </th>
                                                        <th
                                                            className="text-center"
                                                            style={{
                                                                width: 138,
                                                                verticalAlign: 'middle',
                                                            }}
                                                        >
                                                            {t('width')} (cm)
                                                        </th>
                                                        <th
                                                            className="text-center"
                                                            style={{
                                                                width: 142,
                                                                verticalAlign: 'middle',
                                                            }}
                                                        >
                                                            {t('height')} (cm)
                                                        </th>
                                                        <th
                                                            className="text-center"
                                                            style={{
                                                                width: 50,
                                                                verticalAlign: 'middle',
                                                            }}
                                                        >
                                                            <button
                                                                type="button"
                                                                title={t('add')}
                                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                onClick={addSize}
                                                            >
                                                                <PlusCircle size={14} />
                                                            </button>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {fields.map((field, index) => (
                                                        <tr key={field.id}>
                                                            <td className="text-center">
                                                                <Controller
                                                                    control={control}
                                                                    name={`sizes.${index}.placement`}
                                                                    render={({ field }) => (
                                                                        <Select
                                                                            options={placementOptions}
                                                                            onChange={(newValue) =>
                                                                                field.onChange(newValue?.value)
                                                                            }
                                                                            value={placementOptions.find(
                                                                                (e) => e.value === field.value
                                                                            )}
                                                                            styles={{
                                                                                control: (baseStyles, state) =>
                                                                                    getSelectStyle(
                                                                                        baseStyles,
                                                                                        state,
                                                                                        !!errors.design_template_id
                                                                                            ?.message
                                                                                    ),
                                                                                menuPortal: (baseStyles) => ({
                                                                                    ...baseStyles,
                                                                                    zIndex: 1056,
                                                                                }),
                                                                            }}
                                                                            menuPortalTarget={document.body}
                                                                        />
                                                                    )}
                                                                />
                                                            </td>
                                                            <td className="text-center">
                                                                <Controller
                                                                    name={`sizes.${index}.image`}
                                                                    control={control}
                                                                    render={({ field }) => (
                                                                        <div
                                                                            style={{
                                                                                gap: 4,
                                                                            }}
                                                                            className="flex-row d-flex align-items-center justify-content-center"
                                                                        >
                                                                            {field.value && (
                                                                                <img
                                                                                    style={{
                                                                                        width: 60,
                                                                                        height: 60,
                                                                                        borderRadius: 8,
                                                                                    }}
                                                                                    src={field.value || ''}
                                                                                />
                                                                            )}
                                                                            <button
                                                                                type="button"
                                                                                className="btn btn-icon btn-sm btn-flat-info waves-effect"
                                                                                onClick={() => onOpenImageModal(index)}
                                                                            >
                                                                                {field.value ? (
                                                                                    <Edit size={14} />
                                                                                ) : (
                                                                                    <PlusCircle size={14} />
                                                                                )}
                                                                            </button>
                                                                        </div>
                                                                    )}
                                                                />
                                                            </td>
                                                            <td className="text-center">
                                                                <Controller
                                                                    name={`sizes.${index}.width`}
                                                                    control={control}
                                                                    render={({ field }) => (
                                                                        <input
                                                                            {...field}
                                                                            type="text"
                                                                            className="form-control"
                                                                        />
                                                                    )}
                                                                />
                                                            </td>
                                                            <td className="text-center">
                                                                <Controller
                                                                    name={`sizes.${index}.height`}
                                                                    control={control}
                                                                    render={({ field }) => (
                                                                        <input
                                                                            {...field}
                                                                            type="text"
                                                                            className="form-control"
                                                                        />
                                                                    )}
                                                                />
                                                            </td>
                                                            <td className="text-center">
                                                                <button
                                                                    type="button"
                                                                    title={t('delete')}
                                                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                                    onClick={() => remove(index)}
                                                                >
                                                                    <Trash2 size={14} />
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                        {/* Show error under table if any row is missing a required field */}
                                        {Array.isArray(errors.sizes) &&
                                            errors.sizes.some(
                                                (rowErr) =>
                                                    rowErr &&
                                                    (rowErr.placement || rowErr.image || rowErr.width || rowErr.height)
                                            ) && <div className="error mt-1">{t('error.required')}</div>}
                                        {errors.sizes?.message && (
                                            <div className="error mt-1">{errors.sizes.message}</div>
                                        )}
                                    </div>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <UpdateButton btnText={t('update')} isLoading={false} hasDivWrap={false} />
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}

            <ModalTemplateDetail
                ref={templateDetailModalRef}
                showInfo={false}
                show={isOpenImageModal}
                itemId={designTemplateId}
                onClose={onCloseImageModal}
                modalClassName="custom__modal-template-detail"
                renderButtonActions={
                    <button type="button" className="btn btn-primary" onClick={onSaveImage}>
                        {t('save')}
                    </button>
                }
            />
        </>
    );
}
