import { getFieldHtml } from '../../../utils/common';
import { Link } from 'react-router-dom';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import { ItemStatusNames } from '../../../types/common/Item';
import { useTranslation } from 'react-i18next';
import PartnerInvoice from '../../../types/PartnerInvoice';
import Order from '../../../types/Order';
import FormatNumber from '../../../components/partials/FormatNumber';
import User from '../../../types/User';

interface IProps {
    partnerInvoice: PartnerInvoice;
    order: Order;
    partner: User | undefined;
}

export default function PartnerInvoicePrice({ partnerInvoice, order, partner }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="card mb-6">
            <div className="card-body">
                <div className="table-responsive mb-1">
                    <table className="table">
                        <tbody>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('statusName')}</td>
                                <td className="p-0 py-50 border-none">
                                    {getFieldHtml(ItemStatusNames, partnerInvoice.status_id, t)}
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('partner.single')}</td>
                                <td className="p-0 py-50 border-none">{partner?.first_name}</td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('order.single')}</td>
                                <td className="p-0 py-50 border-none">
                                    <Link to={`/order/view/${partnerInvoice.order_id}`}>{order.order_number}</Link>
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('product.cost')}</td>
                                <td className="p-0 py-50 border-none">
                                    <FormatNumber
                                        value={partnerInvoice.product_cost}
                                        isInput={false}
                                        renderText={(value) => value}
                                    />
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('design.cost')}</td>
                                <td className="p-0 py-50 border-none">
                                    <FormatNumber
                                        value={partnerInvoice.design_cost}
                                        isInput={false}
                                        renderText={(value) => value}
                                    />
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('order.shippingCost')}</td>
                                <td className="p-0 py-50 border-none">
                                    <FormatNumber
                                        value={partnerInvoice.ship_cost}
                                        isInput={false}
                                        renderText={(value) => value}
                                    />
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('partner.cost')}</td>
                                <td className="p-0 py-50 border-none">
                                    {partnerInvoice.partner_percent > 0 ? (
                                        partnerInvoice.partner_percent + '%'
                                    ) : (
                                        <FormatNumber
                                            value={partnerInvoice.partner_cost}
                                            isInput={false}
                                            renderText={(value) => value}
                                        />
                                    )}
                                </td>
                            </tr>
                            <tr>
                                <td className="p-0 py-50 border-none">{t('totalCost')}</td>
                                <td className="p-0 py-50 border-none">
                                    <span className="badge badge-glow bg-danger">
                                        <FormatNumber
                                            value={partnerInvoice.sum_cost}
                                            isInput={false}
                                            renderText={(value) => value}
                                        />
                                    </span>
                                </td>
                            </tr>
                            {partnerInvoice.updated_at && (
                                <tr>
                                    <td className="p-0 py-50 border-none">{t('partnerInvoice.paymentTime')}</td>
                                    <td className="p-0 py-50 border-none">
                                        {formatDateTime(partnerInvoice.updated_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
}
