import { BASE_CENTI, BASE_PIXEL } from 'constants/common';
import { TFunction } from 'i18next';
import { find, get, isEmpty } from 'lodash';
import { toast, ToastOptions } from 'react-toastify';
import { ItemParam, ItemParamModel, SelectOption, SelectOptionModel } from 'types/common/Item';

export const showToast = (success: boolean, messages?: string[]) => {
    const options: ToastOptions<{}> = {
        position: 'top-right',
        toastId: Math.random(),
    };
    let MsgNode = null;
    if (messages && messages.length > 1) {
        MsgNode = (
            <div>
                {messages.map((message: string, index: number) => (
                    <p key={index}>{message}</p>
                ))}
            </div>
        );
    }
    if (success) {
        if (!isEmpty(messages)) {
            if (messages!.length === 1) {
                toast.success(messages![0], options);
            } else {
                toast.success(MsgNode, options);
            }
        }
    } else {
        if (!isEmpty(messages)) {
            if (messages!.length === 1) {
                toast.error(messages![0], options);
            } else {
                toast.error(MsgNode, options);
            }
        }
    }
};

export const genTableIndex = (index: number, limit: number, currentPage: number) =>
    index + limit * (currentPage - 1) + 1;

export const getFieldHtml = (
    fields: ItemParam[] | ItemParamModel[],
    id: number | string,
    t: TFunction<'translation', undefined>
) => {
    const item = find(fields, { id }) as ItemParam;
    if (item) {
        return <span className={item.className}>{t(`constants.${item.name}`)}</span>;
    }
    return <></>;
};

export const getFieldInArrayObject = (
    listObj: {}[],
    id: number | string,
    fieldName: string = 'name',
    defaultValue: string = '',
    fieldCompare = 'id'
) => get(find(listObj, { [fieldCompare]: id }), fieldName, defaultValue);

export const toggleModalOpen = (show: boolean) => {
    if (show) {
        document.body.classList.add('modal-open');
    } else {
        document.body.classList.remove('modal-open');
    }
};

export const selectItem = (listItems: ItemParam[], t: TFunction<'translation', undefined>, noNoneOption?: boolean) => {
    const selectOptions: JSX.Element[] = [];
    if (!noNoneOption) {
        selectOptions.push(
            <option key={0} value={0}>
                --
            </option>
        );
    }
    listItems.forEach((item) => {
        selectOptions.push(
            <option key={item.id} value={item.id}>
                {t(`constants.${item.name}`)}
            </option>
        );
    });
    return selectOptions;
};

export const convertConstantToSelectOptions = (
    items: ItemParam[],
    t: TFunction<'translation', undefined>,
    noNoneOption?: boolean
) => {
    const selectOptions: SelectOption[] = [];
    if (!noNoneOption) {
        selectOptions.push({
            value: 0,
            label: '',
        });
    }
    items.forEach((item) => {
        selectOptions.push({
            value: item.id,
            label: t(`constants.${item.name}`),
        });
    });
    return selectOptions;
};

export const convertObjectToSelectOptions = (
    items: ItemParamModel[],
    noNoneOption?: boolean,
    t?: TFunction<'translation', undefined>
) => {
    const selectOptions: SelectOptionModel[] = [];
    if (!noNoneOption) {
        selectOptions.push({
            value: '',
            label: '--',
        });
    }
    items.forEach((item) => {
        selectOptions.push({
            value: item.id,
            label: t ? t(`constants.${item.name}`) : item.name,
        });
    });
    return selectOptions;
};

export const convertSelectToStringKey = (items: SelectOption[]): SelectOptionModel[] =>
    items.map((item) => ({
        value: item.value.toString(),
        label: item.label,
    }));

// tslint:disable-next-line
export const getSelectStyle = (baseStyles: any, state: any, isError: boolean) => ({
    ...baseStyles,
    borderColor: state.isFocused ? (isError ? '#ea5455' : baseStyles.borderColor) : isError ? '#ea5455' : '#d8d6de',
    boxShadow: 'none',
});

export const isValidImageFile = (file: File) => {
    const validImageTypes = [
        'image/png',
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/webp',
        'image/bmp',
        'image/svg+xml',
    ];
    return file && validImageTypes.includes(file.type);
};

/* tslint:disable:no-any */
export function flattenObject(obj: any, prefix = ''): Record<string, any> {
    return Object.keys(obj).reduce((acc, k) => {
        const pre = prefix.length ? prefix + '.' : '';
        if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
            Object.assign(acc, flattenObject(obj[k], pre + k));
        } else {
            acc[pre + k] = obj[k];
        }
        return acc;
    }, {} as Record<string, any>);
}

export const customP2C = (pixels: number) => {
    // Tỷ lệ: 5392px = 190cm
    // => 1cm = 5392/190 pixels
    const pixelsPerCm = BASE_PIXEL / BASE_CENTI;

    return (pixels / pixelsPerCm).toFixed(1);
};

/**
 * Chèn placement vào mã sản phẩm theo quy tắc
 * @param {string} productCode - Mã sản phẩm ban đầu
 * @param {string} placement - Vị trí
 * @returns {string} - Mã sản phẩm sau khi chèn vị trí
 */
export function insertPlacement(productCode: string, placement: string) {
    if (productCode.endsWith('-')) {
        return productCode.slice(0, -1) + `-${placement}-`;
    } else {
        const lastChar = productCode.slice(-1);
        const restOfCode = productCode.slice(0, -1);
        return restOfCode + `${placement}-` + lastChar;
    }
}

function sanitizeBase64String(base64: string) {
    base64 = base64.replace(/^data:application\/pdf;base64,/, '');
    base64 = base64.replace(/\s/g, '');
    base64 = base64.replace(/-/g, '+').replace(/_/g, '/');
    while (base64.length % 4 !== 0) {
        base64 += '=';
    }
    return base64;
}

export const downloadPdf = (base64Str: string, fileName = 'label.pdf') => {
    const cleanBase64Str = sanitizeBase64String(base64Str);
    const byteCharacters = atob(cleanBase64Str);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'application/pdf' });
    const blobUrl = URL.createObjectURL(blob);
    window.open(blobUrl, '_blank');
};
