import { useQuery } from '@tanstack/react-query';
import PaginationTable from 'components/partials/PaginationTable';
import ResetButton from 'components/partials/ResetButton';
import Spinner from 'components/partials/Spinner';
import UpdateButton from 'components/partials/UpdateButton';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import { isUndefined, omitBy } from 'lodash';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import Select, { SingleValue } from 'react-select';
import DashboardService from 'services/DashboardService';
import UserService from 'services/UserService';
import { ItemStatus, SelectOptionModel } from 'types/common/Item';
import { ProductsSoldParam } from 'types/Dashboard';
import { ProductTypeNames } from 'types/Product';
import { SearchUser, UserRole } from 'types/User';
import { convertObjectToSelectOptions, flattenObject, showToast } from 'utils/common';
import { validateDateRange } from 'utils/date';

type SelectSearch = 'ProductType' | 'ProductVendor';

export default function ProductsSold() {
    const { t } = useTranslation();
    const { control, handleSubmit, reset, setValue } = useForm<ProductsSoldParam>();
    const [productType, setProductType] = useState<SelectOptionModel | null>(null);
    const [productVendor, setProductVendor] = useState<SelectOptionModel | null>(null);
    const [searchParams, setSearchParams] = useState<ProductsSoldParam>({
        limit: PAGINATION.limit,
        page: 1,
    });

    const { data: suppliers, isLoading: isLoadingVendor } = useQuery({
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.SUPPLIER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
    });

    const { data, isLoading, isRefetching } = useQuery({
        queryKey: [QUERY_KEY.DASHBOARD_PRODUCTS_SOLD, searchParams],
        queryFn: ({ queryKey }) => DashboardService.getProductSold(queryKey[1] as ProductsSoldParam),
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setSearchParams((prev) => ({ ...prev, page }));
    };

    const onChangeSelectSearch = (option: SingleValue<SelectOptionModel>, type: SelectSearch) => {
        switch (type) {
            case 'ProductType':
                setValue('product.product_type', (option?.value ?? '') as never);
                setProductType(option);
                break;
            case 'ProductVendor':
                setValue('product.vendor', (option?.value ?? '') as never);
                setProductVendor(option);
                break;
            default:
        }
    };

    const handleSearch = (formData: ProductsSoldParam) => {
        const flattenedData = flattenObject(formData);
        const cleanedData = omitBy(flattenedData, isUndefined);
        const errorMessage = validateDateRange(
            cleanedData['orderProducts.order.order_created_at__ge'],
            cleanedData['orderProducts.order.order_created_at__le']
        );
        if (errorMessage) {
            showToast(false, [errorMessage]);
            return;
        }
        setSearchParams((prev) => ({ ...prev, ...cleanedData, page: 1 }));
    };

    const handleReset = () => {
        reset();
        setSearchParams({ limit: PAGINATION.limit, page: 1 });
        setProductType(null);
        setProductVendor(null);
    };

    return (
        <div className="card">
            <div className="card-header">
                <h4 className="card-title">{t('goodsSold')}</h4>
            </div>
            <div className="card-body">
                <form onSubmit={handleSubmit(handleSearch)}>
                    <div className="row">
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('keywords')}</label>
                            <Controller
                                name="search_text"
                                control={control}
                                defaultValue=""
                                render={({ field }) => <input {...field} type="text" className="form-control" />}
                            />
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('sku')}</label>
                            <Controller
                                name="sku__i"
                                control={control}
                                defaultValue=""
                                render={({ field }) => <input {...field} type="text" className="form-control" />}
                            />
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('type')}</label>
                            <Select
                                options={convertObjectToSelectOptions(ProductTypeNames, true)}
                                onChange={(option) => onChangeSelectSearch(option, 'ProductType')}
                                value={productType}
                                isClearable={true}
                            />
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('startDate')}</label>
                            <Controller
                                name="orderProducts.order.order_created_at__ge"
                                control={control}
                                render={({ field }) => (
                                    <input {...field} type="date" className="form-control" value={field.value || ''} />
                                )}
                            />
                        </div>
                        <div className="col-12 col-md-4 mb-1">
                            <label className="form-label">{t('endDate')}</label>
                            <Controller
                                name="orderProducts.order.order_created_at__le"
                                control={control}
                                render={({ field }) => (
                                    <input {...field} type="date" className="form-control" value={field.value || ''} />
                                )}
                            />
                        </div>
                        {!isLoadingVendor && suppliers && (
                            <div className="col-12 col-md-4 mb-1">
                                <label className="form-label">{t('vendor')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(
                                        suppliers.items.map(
                                            (item) => ({
                                                id: item.last_name ?? item.first_name,
                                                name: item.first_name,
                                            }),
                                            true
                                        )
                                    )}
                                    onChange={(option) => onChangeSelectSearch(option, 'ProductVendor')}
                                    value={productVendor}
                                    isClearable={true}
                                />
                            </div>
                        )}
                        {/* TODO: Shop */}
                        <div className="col-md-4 col-12">
                            <UpdateButton
                                btnText={t('search')}
                                isLoading={isLoading}
                                hasDivWrap={false}
                                btnClass={['me-1']}
                            />
                            <ResetButton btnText={t('reset')} isLoading={isLoading} handleReset={handleReset} />
                        </div>
                    </div>
                </form>
            </div>
            {(isLoading || isRefetching) && <Spinner />}
            {data && (
                <>
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th className="text-center">{t('no.')}</th>
                                    <th>{t('name')}</th>
                                    <th>{t('sku')}</th>
                                    <th>{t('type')}</th>
                                    <th>{t('vendor')}</th>
                                    <th className="text-center">{t('quantity')}</th>
                                </tr>
                            </thead>

                            <tbody>
                                {data.items.map((item, index) => (
                                    <tr key={index}>
                                        <td className="text-center">{index + 1}</td>
                                        <td>
                                            {item['product-title']} - {item['product_children-title']}
                                        </td>
                                        <td>{item['product_children-sku']}</td>
                                        <td>{item['product-product_type']}</td>
                                        <td>{item['product-vendor']}</td>
                                        <td>{item['quantity_sold']}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <PaginationTable
                        countItem={data.pagination.count_item}
                        totalPage={data.pagination.total_page}
                        currentPage={data.pagination.current_page}
                        handlePageChange={handlePageChange}
                    />
                </>
            )}
        </div>
    );
}
