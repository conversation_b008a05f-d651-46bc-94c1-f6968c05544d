import { Trash2 } from 'react-feather';
import { useTranslation } from 'react-i18next';
import ProductWeight from 'types/ProductWeight';

interface IProps {
    items: ProductWeight[];
    handleEdit: (id: string) => void;
    handleDelete: (id: string) => void;
}

export default function ListWeight({ items, handleEdit, handleDelete }: Readonly<IProps>) {
    const { t } = useTranslation();
    return (
        <div className="table-responsive">
            <table className="table">
                <thead>
                    <tr>
                        <th className="text-center">{t('no.')}</th>
                        <th>{t('name')}</th>
                        <th>{t('productWeight.weight')}</th>
                        <th className="thAction3"></th>
                    </tr>
                </thead>
                <tbody>
                    {items.map((item: ProductWeight, index: number) => (
                        <tr key={item.id}>
                            <td className="text-center">{index + 1}</td>
                            <td className="text-primary cursor-pointer" onClick={() => handleEdit(item.id!)}>
                                {item.tag}
                            </td>
                            <td>{item.weight}</td>
                            <td>
                                <button
                                    type="button"
                                    title={t('delete')}
                                    className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                    onClick={() => handleDelete(item.id!)}
                                >
                                    <Trash2 size={14} />
                                </button>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}
