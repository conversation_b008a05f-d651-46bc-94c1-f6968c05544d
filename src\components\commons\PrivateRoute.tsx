import { useQuery } from '@tanstack/react-query';
import BoxErrorMessage from 'components/partials/BoxErrorMessage';
import Footer from 'components/partials/Footer';
import Header from 'components/partials/Header';
import { AUTH_KEYS } from 'constants/auth';
import { QUERY_KEY, REACT_APP_IS_ADMIN, REACT_APP_IS_MANUFACTURER, REACT_APP_IS_PARTNER } from 'constants/common';
import { find, includes, isEmpty, startsWith } from 'lodash';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import ActionService from 'services/ActionService';
import OrderService from 'services/OrderService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import { useAuthStore } from 'stores/authStore';
import { UserRoleNames } from 'types/User';
import { getLocalStorage, removeLocalStorage } from 'utils/localStorage';

export function PrivateRoute({ children }: Readonly<{ children: JSX.Element }>) {
    const location = useLocation();
    const accessToken = getLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
    const [canAccess, setCanAccess] = useState(false);
    const user = useAuthStore((state) => state.user);
    const authorized = useAuthStore((state) => state.authorized);
    const unauthorized = useAuthStore((state) => state.unauthorized);
    const navigate = useNavigate();
    const { t } = useTranslation();

    const handleLogout = async () => {
        try {
            await UserService.logout();
        } finally {
            unauthorized();
            removeLocalStorage(AUTH_KEYS.ACCESS_TOKEN);
            removeLocalStorage(AUTH_KEYS.REFRESH_TOKEN);
            navigate('/');
        }
    };

    const { data: profile } = useQuery({
        enabled: !user && !!accessToken,
        queryKey: [QUERY_KEY.PROFILE, accessToken],
        queryFn: async () => {
            const response = await UserService.getProfile();
            if (response.success) {
                authorized(response.data);
            } else handleLogout();
            return response.data;
        },
        staleTime: Infinity,
    });

    const {
        data: actions,
        isLoading,
        isRefetching,
    } = useQuery({
        enabled: !!user || !!profile,
        queryKey: [QUERY_KEY.USER_ACTIONS, user?.id ?? profile?.id],
        queryFn: () => ActionService.getAuthActions(),
    });

    const { data: shops } = useQuery({
        enabled: !isEmpty(actions) && (REACT_APP_IS_ADMIN || REACT_APP_IS_PARTNER),
        queryKey: [QUERY_KEY.SHOPS, user?.id ?? profile?.id],
        queryFn: ({ queryKey }) => ShopService.getOwns(queryKey[1] ?? ''),
    });

    const { data: countOrderNoWarehouse } = useQuery({
        enabled: REACT_APP_IS_ADMIN && !isEmpty(actions),
        queryKey: [QUERY_KEY.ORDER_COUNT],
        queryFn: OrderService.countNoWarehouse,
    });

    useEffect(() => {
        let isAccess = includes(['/dashboard', '/user/profile'], location.pathname);
        if (!isAccess) {
            const menus = ActionService.getFlatList(actions ?? []);
            let url = location.pathname;
            isAccess = !!find(menus, { url });
            if (!isAccess && (startsWith(url, '/user/add/') || startsWith(url, '/user/edit/'))) {
                UserRoleNames.forEach((item) => {
                    url = `/user/list/${item.name}`;
                    if (!isAccess) isAccess = !!find(menus, { url });
                });
            }
            if (!isAccess && !REACT_APP_IS_MANUFACTURER && startsWith(url, '/product/')) {
                isAccess = REACT_APP_IS_PARTNER;
                if (!isAccess) isAccess = !!find(menus, { url: '/product' });
            }
            if (!isAccess && REACT_APP_IS_ADMIN && startsWith(url, '/stockWarning/')) {
                isAccess = !!find(menus, { url: '/stockWarning' });
            }
            if (
                !isAccess &&
                !REACT_APP_IS_PARTNER &&
                (startsWith(url, '/transfer/add/') || startsWith(url, '/transfer/edit/'))
            ) {
                isAccess = !!find(menus, { url: '/transfer/list/receive' });
            }
            if (!isAccess && startsWith(url, '/order/')) {
                isAccess = !!find(menus, { url: '/order' });
            }
            if (!isAccess && url === '/action') isAccess = REACT_APP_IS_ADMIN;
        }
        //setCanAccess(isAccess);
        setCanAccess(true);
    }, [actions, location]);

    if (!isEmpty(accessToken)) {
        return (
            <>
                <Header
                    actions={actions ?? []}
                    user={user ?? profile}
                    shops={shops?.items ?? []}
                    countOrderNoWarehouse={countOrderNoWarehouse ?? 0}
                    handleLogout={handleLogout}
                />
                <div className="app-content content chat-application">
                    <div className="content-overlay" />
                    <div className="header-navbar-shadow" />
                    {!isLoading && !isRefetching && canAccess && (
                        <div className="content-wrapper container-xxl p-0">{children}</div>
                    )}
                    {!isLoading && !isRefetching && !canAccess && (
                        <div className="content-wrapper container-xxl p-0">
                            <div className="content-body">
                                <BoxErrorMessage title={t('notifications')} messages={[t('notFoundDesc')]} />
                            </div>
                        </div>
                    )}
                </div>
                <Footer />
            </>
        );
    }
    return <Navigate to={`/?url=${location.pathname}`} />;
}
