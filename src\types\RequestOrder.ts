import { BaseModel, BaseSearch } from './common';
import { ItemParamModel } from './common/Item';

export interface SearchRequestOrder extends BaseSearch {
    item_per_month__i?: string[] | string;
}

export type SearchRequestOrderParam = {
    [key in keyof SearchRequestOrder]: string;
};

export default interface RequestOrder extends BaseModel {
    name: string;
    email: string;
    phone: string;
    website: string;
    item_per_month: string;
    comment: string;
    ip_address: string;
}

export const ItemPerOrderNames: ItemParamModel[] = [
    'Less than 100 items',
    'From 100 - 500 items',
    'More than 500 items',
].map((item) => ({
    id: item,
    name: item,
}));
