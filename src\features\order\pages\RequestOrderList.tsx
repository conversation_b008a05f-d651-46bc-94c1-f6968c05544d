import { keepPreviousData, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import PaginationTable from 'components/partials/PaginationTable';
import Spinner from 'components/partials/Spinner';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import useQueryParams from 'hooks/useQueryParams';
import { isUndefined, omitBy } from 'lodash';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import RequestOrderService from 'services/RequestOrderService';
import { SearchRequestOrder, SearchRequestOrderParam } from 'types/RequestOrder';
import ListRequestOrder from '../components/ListRequestOrder';
import SearchRequestOrderForm from '../components/SearchRequestOrderForm';

export default function RequestOrderList() {
    const { t } = useTranslation();
    const { queryParams, setQueryParams } = useQueryParams<SearchRequestOrderParam>();
    const paramConfig: SearchRequestOrderParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            search_text: queryParams.search_text,
            item_per_month__i: queryParams.item_per_month__i,
        },
        isUndefined
    );

    const { data, isLoading, isRefetching } = useQuery({
        queryKey: [QUERY_KEY.REQUEST_ORDERS, paramConfig],
        queryFn: ({ queryKey }) => RequestOrderService.getList(queryKey[1] as SearchRequestOrder),
        placeholderData: keepPreviousData,
    });

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    return (
        <>
            <Helmet>
                <title>{t(`requestOrder.multiple`)}</title>
            </Helmet>
            <ContentHeader title={t(`requestOrder.multiple`)} />
            <div className="content-body">
                <div className="col-12">
                    <SearchRequestOrderForm isLoading={isLoading || isRefetching} />
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <div className="card">
                            <ListRequestOrder items={data.items} paging={data.pagination} />
                            <PaginationTable
                                countItem={data.pagination.count_item}
                                totalPage={data.pagination.total_page}
                                currentPage={data.pagination.current_page}
                                handlePageChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
