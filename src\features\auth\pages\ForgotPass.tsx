import { ChevronLeft } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import ForgotForm from '../components/ForgotForm';

export default function ForgotPass() {
    const { t } = useTranslation();

    return (
        <>
            <Helmet>
                <title>{t('forgotPassword')}</title>
            </Helmet>
            <div className="d-flex col-lg-4 align-items-center auth-bg px-2 p-lg-5">
                <div className="col-12 col-sm-8 col-md-6 col-lg-12 px-xl-2 mx-auto">
                    <h2 className="card-title fw-bold mb-1 text-center">{t('forgotPassword')}</h2>
                    <p className="card-text mb-2 text-center">{t('forgotPasswordDesc')}</p>
                    <ForgotForm />
                    <p className="text-center mt-2">
                        <Link to="/">
                            <ChevronLeft size={14} /> {t('backToLogin')}
                        </Link>
                    </p>
                </div>
            </div>
        </>
    );
}
