import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import { useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import UserService from 'services/UserService';
import { ChangePassword } from 'types/User';
import { ItemId } from 'types/common/Item';
import { showToast } from 'utils/common';
import * as yup from 'yup';

export default function SetPassForm({ id }: Readonly<ItemId>) {
    const [showPass, setShowPass] = useState(false);
    const [showRePass, setShowRePass] = useState(false);
    const { t } = useTranslation();
    const schema = yup
        .object({
            password: yup.string().required(t('error.required')).trim().min(6, t('error.passwordLength')),
            confirm_password: yup
                .string()
                .required(t('error.required'))
                .trim()
                .min(6, t('error.passwordLength'))
                .oneOf([yup.ref('password'), null], t('error.passwordNotSame')),
        })
        .required();
    const {
        register,
        handleSubmit,
        formState: { isSubmitting, errors },
    } = useForm<ChangePassword>({
        resolver: yupResolver(schema),
    });

    const onSubmit = async (data: ChangePassword) => {
        if (id) {
            const response = await UserService.changeUserPassword(id as string, data);
            showToast(response.success, response.messages);
        }
    };

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h4 className="card-title">{t('resetPassword')}</h4>
            </div>
            <div className="card-body pt-1">
                <form className="validate-form" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">{t('newPassword')}</label>
                            <div className="input-group input-group-merge form-password-toggle">
                                <input
                                    {...register('password')}
                                    className={classNames('form-control', 'form-control-merge', {
                                        error: Boolean(errors.password?.message),
                                    })}
                                    type={showPass ? 'text' : 'password'}
                                    placeholder="············"
                                />
                                <span
                                    className="input-group-text cursor-pointer"
                                    onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                                >
                                    {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                                </span>
                            </div>
                            <span className="error">{errors.password?.message}</span>
                        </div>
                        <div className="col-12 col-sm-6 mb-1">
                            <label className="form-label">{t('rePassword')}</label>
                            <div className="input-group input-group-merge form-password-toggle">
                                <input
                                    {...register('confirm_password')}
                                    className={classNames('form-control', 'form-control-merge', {
                                        error: Boolean(errors.confirm_password?.message),
                                    })}
                                    type={showRePass ? 'text' : 'password'}
                                    placeholder="············"
                                />
                                <span
                                    className="input-group-text cursor-pointer"
                                    onClick={() => setShowRePass((prevShowRePass) => !prevShowRePass)}
                                >
                                    {showRePass ? <EyeOff size={14} /> : <Eye size={14} />}
                                </span>
                            </div>
                            <span className="error">{errors.confirm_password?.message}</span>
                        </div>
                        <div className="col-12">
                            <UpdateButton btnText={t('resetPassword')} isLoading={isSubmitting} btnClass={['mt-1']} />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
}
