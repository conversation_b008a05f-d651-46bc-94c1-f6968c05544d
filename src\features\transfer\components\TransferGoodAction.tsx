import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import ModalConfirm from 'components/partials/ModalConfirm';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import TransferGoodService from 'services/TransferGoodService';
import { TrueFalse, YesNoChoices } from 'types/common/Item';
import { TransferGoodBarcode, TransferStatus, TransferStatusNames } from 'types/TransferGood';
import { getFieldHtml, getFieldInArrayObject, showToast } from 'utils/common';
import { FORMAT_DATE, formatDateTime } from 'utils/date';

interface IProps {
    transferGood: TransferGoodBarcode;
    isScan?: boolean;
    refetch: (options?: RefetchOptions) => Promise<QueryObserverResult<TransferGoodBarcode | null, Error>>;
}

export default function TransferGoodAction({ transferGood, isScan, refetch }: Readonly<IProps>) {
    const { t } = useTranslation();
    const [show, setShow] = useState(false);
    const [statusId, setStatusId] = useState(
        transferGood.status_id === TransferStatus.SENT && isScan ? TransferStatus.TRANSFERRING : 0
    );
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (transferGood.status_id === TransferStatus.SENT && isScan) {
            changeStatus();
        }
    }, [transferGood.status_id, isScan]);

    const changeStatus = async () => {
        setLoading(true);
        const response = await TransferGoodService.updateStatus(transferGood.id!, statusId);
        transferGood.status_id !== TransferStatus.SENT && !isScan && showToast(response.success, response.messages);
        if (response.success) {
            setShow(false);
            setStatusId(0);
            refetch();
        }
        setLoading(false);
    };

    const printItem = async () => {
        setLoading(true);
        const response = await TransferGoodService.printItem(transferGood.id!);
        setLoading(false);
        if (response.success) window.open(response.data, '_blank');
        else showToast(response.success, response.messages);
    };

    return (
        <>
            <div className="card mb-6">
                <div className="card-body">
                    <div className="table-responsive mb-1">
                        <table className="table">
                            <tbody>
                                <tr>
                                    <td className="p-0 py-50 border-none">{t('statusName')}</td>
                                    <td className="p-0 py-50 border-none">
                                        {getFieldHtml(TransferStatusNames, transferGood.status_id, t)}
                                    </td>
                                </tr>
                                <tr>
                                    <td className="p-0 py-50 border-none">{t('warehouse.single')}</td>
                                    <td className="p-0 py-50 border-none">{transferGood.warehouse.name}</td>
                                </tr>
                                <tr>
                                    <td className="p-0 py-50 border-none">{t('supplier.single')}</td>
                                    <td className="p-0 py-50 border-none">
                                        <Link to={`/user/edit/${transferGood.supplier.id}`}>
                                            {transferGood.supplier.first_name}
                                        </Link>
                                    </td>
                                </tr>
                                {transferGood.sent_at && (
                                    <tr>
                                        <td className="p-0 py-50 border-none">{t('sentTime')}</td>
                                        <td className="p-0 py-50 border-none">
                                            {formatDateTime(transferGood.sent_at, FORMAT_DATE.SHOW_DATE_MINUTE)}
                                        </td>
                                    </tr>
                                )}
                                <tr>
                                    <td className="p-0 py-50 border-none">{t('createAuto')}</td>
                                    <td className="p-0 py-50 border-none">
                                        {getFieldHtml(
                                            YesNoChoices,
                                            transferGood.is_auto ? TrueFalse.TRUE : TrueFalse.FALSE,
                                            t
                                        )}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    {transferGood.status_id === TransferStatus.PENDING && !isScan && (
                        <>
                            <button
                                className="btn btn-info waves-effect waves-float waves-light w-100 mb-75"
                                disabled={loading}
                                onClick={() => {
                                    setStatusId(TransferStatus.APPROVED);
                                    setShow(true);
                                }}
                            >
                                {t('constants.approved')}
                            </button>
                            <button
                                className="btn btn-primary waves-effect waves-float waves-light w-100 mb-75"
                                disabled={loading}
                                onClick={() => {
                                    setStatusId(TransferStatus.SENT);
                                    setShow(true);
                                }}
                            >
                                {t('constants.sent')}
                            </button>
                            {!transferGood.is_auto && (
                                <>
                                    <button
                                        className="btn btn-danger waves-effect waves-float waves-light w-100 mb-75"
                                        disabled={loading}
                                        onClick={() => {
                                            setStatusId(TransferStatus.REJECTED);
                                            setShow(true);
                                        }}
                                    >
                                        {t('constants.rejected')}
                                    </button>
                                    <button
                                        className="btn btn-secondary waves-effect waves-float waves-light w-100 mb-75"
                                        disabled={loading}
                                        onClick={() => {
                                            setStatusId(TransferStatus.CANCELLED);
                                            setShow(true);
                                        }}
                                    >
                                        {t('constants.cancelled')}
                                    </button>
                                </>
                            )}
                        </>
                    )}
                    {transferGood.status_id === TransferStatus.APPROVED && !isScan && (
                        <button
                            className="btn btn-primary waves-effect waves-float waves-light w-100 mb-75"
                            disabled={loading}
                            onClick={() => {
                                setStatusId(TransferStatus.SENT);
                                setShow(true);
                            }}
                        >
                            {t('constants.sent')}
                        </button>
                    )}
                    <button
                        className="btn btn-success waves-effect waves-float waves-light w-100 mb-75"
                        disabled={loading}
                        onClick={printItem}
                    >
                        {t('printJobSheet')}
                    </button>
                </div>
            </div>
            <ModalConfirm
                show={show}
                text={t('confirm.status', {
                    data: t(`constants.${getFieldInArrayObject(TransferStatusNames, statusId)}`),
                })}
                btnDisabled={loading}
                changeShow={(s: boolean) => setShow(s)}
                submitAction={changeStatus}
            />
        </>
    );
}
