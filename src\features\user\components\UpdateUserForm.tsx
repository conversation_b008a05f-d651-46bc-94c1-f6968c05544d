import { yupResolver } from '@hookform/resolvers/yup';
import classNames from 'classnames';
import UpdateButton from 'components/partials/UpdateButton';
import UploadImage from 'components/partials/UploadImage';
import { DEFAULT_VALUE, optionModelDefault } from 'constants/common';
import { find, includes } from 'lodash';
import { useEffect, useState } from 'react';
import { Eye, EyeOff } from 'react-feather';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Select, { SingleValue } from 'react-select';
import CountryService from 'services/CountryService';
import FileService from 'services/FileService';
import ShopService from 'services/ShopService';
import UserService from 'services/UserService';
import { useAuthStore } from 'stores/authStore';
import Country from 'types/Country';
import Shop from 'types/Shop';
import User, { Gender, GenderNames, User<PERSON><PERSON>, UserRole } from 'types/User';
import { ItemParamModel, ItemStatus, ItemStatusNames, SelectOptionModel } from 'types/common/Item';
import { convertObjectToSelectOptions, getSelectStyle, isValidImageFile, selectItem, showToast } from 'utils/common';
import { formatInputDateTime } from 'utils/date';
import * as yup from 'yup';
import FormatNumber from '../../../components/partials/FormatNumber';

interface IProps {
    id: string;
    roleId: UserRole | number;
    user?: User;
    isProfile?: boolean;
}

export default function UpdateUserForm({ id, roleId, user, isProfile = false }: Readonly<IProps>) {
    const [showPass, setShowPass] = useState(false);
    const [showRePass, setShowRePass] = useState(false);
    const [avatar, setAvatar] = useState(user?.avatar ?? DEFAULT_VALUE.IMAGE);
    const [fileAvatar, setFileAvatar] = useState<File>();
    const [countries, setCountries] = useState<Country[]>([]);
    const [shops, setShops] = useState<Shop[]>([]);
    const [countryValue, setCountryValue] = useState<SelectOptionModel>(optionModelDefault);
    const [shopValue, setShopValue] = useState<SelectOptionModel>(optionModelDefault);
    const [jsonManufacturer, setJsonManufacturer] = useState<UserJson>({
        company: user?.json?.company,
        add1: user?.json?.add1,
        add2: user?.json?.add2,
        city: user?.json?.city,
        postcode: user?.json?.postcode,
        state: user?.json?.state,
    });
    const [jsonPartner, setJsonPartner] = useState<UserJson>({
        partnerPercent: user?.json?.partnerPercent,
        partnerCost: user?.json?.partnerCost,
    });
    const userLogin = useAuthStore((state) => state.user);
    const authorized = useAuthStore((state) => state.authorized);
    const navigate = useNavigate();
    const { t } = useTranslation();
    const yupObj = {
        first_name: yup.string().required(t('error.required')).trim(),
        phone_number: yup.string().required(t('error.required')).trim(),
        email: yup
            .string()
            .required(t('error.required'))
            .trim()
            .matches(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, t('error.email')),
        address: yup.string().required(t('error.required')).trim(),
        country_id: yup.string().required(t('error.required')).trim(),
    };
    if (!id && includes([UserRole.ADMIN, UserRole.PARTNER, UserRole.MANUFACTURER, UserRole.DESIGNER], roleId)) {
        Object.assign(yupObj, {
            password: yup.string().required(t('error.required')).trim().min(6, t('error.passwordLength')),
            confirm_password: yup
                .string()
                .required(t('error.required'))
                .trim()
                .min(6, t('error.passwordLength'))
                .oneOf([yup.ref('password'), null], t('error.passwordNotSame')),
        });
    }
    if (includes([UserRole.SUPPLIER, UserRole.PARTNER], roleId)) {
        Object.assign(yupObj, {
            last_name: yup.string().required(t('error.required')).trim(),
        });
    }
    const schema = yup.object(yupObj).required();
    const {
        register,
        handleSubmit,
        setValue,
        clearErrors,
        reset,
        formState: { isSubmitting, errors },
    } = useForm<User>({
        resolver: yupResolver(schema),
    });

    // const isDisableUpdateCustomer = useMemo(
    //     () =>
    //         roleId === UserRole.CUSTOMER &&
    //         (userLogin?.role_id === UserRole.ADMIN || userLogin?.role_id === UserRole.PARTNER),
    //     [roleId]
    // );

    const isDisableUpdateCustomer = false;

    useEffect(() => {
        const getCountries = async (cId: string = '') => {
            const response = await CountryService.getList();
            setCountries(response);
            if (cId) {
                const item = find(response, { id: cId });
                if (item) {
                    setCountryValue({
                        value: item.id!,
                        label: item.name,
                    });
                } else setCountryValue(optionModelDefault);
            } else setCountryValue(optionModelDefault);
        };
        const getShops = async (sId: string) => {
            const response = await ShopService.getOwns(userLogin!.id!);
            setShops(response?.items ?? []);
            if (sId) {
                const item = find(response?.items, { id: sId });
                if (item) {
                    setShopValue({
                        value: item.id!,
                        label: item.name,
                    });
                } else setShopValue(optionModelDefault);
            } else setShopValue(optionModelDefault);
        };
        if (roleId > 0) {
            if (id && user) {
                reset({
                    ...user,
                    gender_id: user.gender_id ?? Gender.OTHER,
                    birthday: formatInputDateTime(user.birthday),
                });
                setAvatar(user.avatar ?? DEFAULT_VALUE.IMAGE);
                getCountries(user.country_id);
                if (roleId === UserRole.CUSTOMER) getShops(user.shop_id ?? '');
            } else {
                getCountries();
                setValue('status_id', ItemStatus.ACTIVE);
                setValue('gender_id', Gender.OTHER);
                if (roleId === UserRole.CUSTOMER) getShops(user?.shop_id ?? '');
            }
        }
    }, [id, user, roleId]);

    const onChangeFile = (file: File) => {
        if (!isValidImageFile(file)) {
            showToast(false, [t('error.chooseImage')]);
            return;
        }
        setFileAvatar(file);
        setAvatar(URL.createObjectURL(file));
    };

    const onChangeCountry = (option: SingleValue<SelectOptionModel>) => {
        setCountryValue(option ?? optionModelDefault);
        setValue('country_id', option?.value ?? '');
        if (option?.value) clearErrors('country_id');
    };

    const onChangeShop = (option: SingleValue<SelectOptionModel>) => {
        setShopValue(option ?? optionModelDefault);
        setValue('shop_id', option?.value ?? '');
    };

    const handleJsonFieldChange = (fieldName: keyof UserJson, value: string) => {
        if (includes(['partnerPercent', 'partnerCost'], fieldName)) {
            setJsonPartner((prev) => ({
                ...prev,
                [fieldName]: value,
            }));
        } else {
            setJsonManufacturer((prev) => ({
                ...prev,
                [fieldName]: value,
            }));
        }
    };

    const onSubmit = async (data: User) => {
        if (roleId === UserRole.PARTNER) {
            const percent = +(jsonPartner.partnerPercent ?? 0);
            const cost = +(jsonPartner.partnerCost ?? 0);
            if (percent < 0 || percent > 100 || cost < 0) {
                showToast(false, [t('partner.invalidPercentCost')]);
                return;
            }
        }
        delete data.id;
        if (!data.birthday) delete data.birthday;
        let avatarIdTmp = data.avatar_id ?? '',
            avatarTmp = avatar;
        if (fileAvatar) {
            const results = await FileService.upload(fileAvatar);
            if (results.success) {
                avatarIdTmp = results.data.id;
                avatarTmp = results.data.url;
                setAvatar(avatarTmp);
            } else {
                showToast(false, results.messages);
            }
            setFileAvatar(undefined);
        }
        if (avatarIdTmp) data.avatar_id = avatarIdTmp;
        else delete data.avatar_id;
        if (!data.last_name) delete data.last_name;
        if (!data.shop_id) delete data.shop_id;
        data.role_id = roleId;
        if (!id && !includes([UserRole.ADMIN, UserRole.PARTNER], roleId)) data.password = '123456';
        if (roleId === UserRole.MANUFACTURER) data.json = jsonManufacturer;
        if (roleId === UserRole.PARTNER) data.json = jsonPartner;
        const response = isProfile ? await UserService.updateProfile(data) : await UserService.update(data, id);
        showToast(response.success, response.messages);
        if (response.success) {
            if (!id) navigate(`/user/edit/${response.data.id}`);
            else {
                if (id === userLogin?.id) {
                    authorized({ ...userLogin, avatar_id: avatarIdTmp, avatar: avatarTmp });
                }
            }
        }
    };

    return (
        <div className="card">
            <div className="card-header border-bottom">
                <h4 className="card-title">{t('baseInformation')}</h4>
            </div>
            <div className="card-body py-2 my-25">
                <form className="validate-form pt-50" onSubmit={handleSubmit(onSubmit)}>
                    <div className="row">
                        {!isDisableUpdateCustomer && (
                            <>
                                <div className="col-12 col-sm-6">
                                    <UploadImage
                                        id={1}
                                        image={avatar}
                                        label={t('uploadAvatar')}
                                        onChangeFile={onChangeFile}
                                    />
                                </div>
                                <div className="col-12 col-sm-6"></div>
                            </>
                        )}
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t(roleId === UserRole.CUSTOMER ? 'firstName' : 'fullName')}{' '}
                                <span className="error">*</span>
                            </label>
                            <input
                                {...register('first_name')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.first_name?.message),
                                })}
                                disabled={isDisableUpdateCustomer}
                                readOnly={isDisableUpdateCustomer}
                            />
                            <span className="error">{errors.first_name?.message}</span>
                        </div>
                        {includes([UserRole.CUSTOMER, UserRole.SUPPLIER, UserRole.PARTNER], roleId) && (
                            <div className="col-12 col-sm-4 mb-1">
                                <label className="form-label">
                                    {t(roleId === UserRole.CUSTOMER ? 'lastName' : 'code')}{' '}
                                    {roleId !== UserRole.CUSTOMER && <span className="error">*</span>}
                                </label>
                                <input
                                    {...register('last_name')}
                                    type="text"
                                    className={classNames('form-control', {
                                        'is-invalid':
                                            Boolean(errors.last_name?.message) && roleId !== UserRole.CUSTOMER,
                                    })}
                                    disabled={isDisableUpdateCustomer}
                                    readOnly={isDisableUpdateCustomer}
                                />
                                {roleId !== UserRole.CUSTOMER && (
                                    <span className="error">{errors.last_name?.message}</span>
                                )}
                            </div>
                        )}
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('email')} <span className="error">*</span>
                            </label>
                            <input
                                {...register('email')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.email?.message),
                                })}
                                disabled={isDisableUpdateCustomer}
                                readOnly={isDisableUpdateCustomer}
                            />
                            <span className="error">{errors.email?.message}</span>
                        </div>
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('phoneNumber')} <span className="error">*</span>
                            </label>
                            <input
                                {...register('phone_number')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.phone_number?.message),
                                })}
                                disabled={isDisableUpdateCustomer}
                                readOnly={isDisableUpdateCustomer}
                            />
                            <span className="error">{errors.phone_number?.message}</span>
                        </div>
                        {roleId !== UserRole.CUSTOMER && (
                            <div className="col-12 col-sm-4 mb-1">
                                <label className="form-label">{t('statusName')}</label>
                                <select {...register('status_id')} className="form-select" disabled={isProfile}>
                                    {selectItem(ItemStatusNames, t, true)}
                                </select>
                            </div>
                        )}
                        {includes([UserRole.ADMIN, UserRole.CUSTOMER], roleId) && (
                            <>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('genderName')}</label>
                                    <select
                                        {...register('gender_id')}
                                        className="form-select"
                                        disabled={isDisableUpdateCustomer}
                                    >
                                        {selectItem(GenderNames, t, true)}
                                    </select>
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('birthday')}</label>
                                    <input
                                        {...register('birthday')}
                                        type="date"
                                        className="form-control"
                                        disabled={isDisableUpdateCustomer}
                                        readOnly={isDisableUpdateCustomer}
                                    />
                                </div>
                            </>
                        )}
                        <div className="col-12 col-sm-4 mb-1">
                            <label className="form-label">
                                {t('country')} <span className="error">*</span>
                            </label>
                            <Select
                                options={convertObjectToSelectOptions(countries as ItemParamModel[])}
                                onChange={onChangeCountry}
                                value={countryValue}
                                styles={{
                                    control: (baseStyles, state) =>
                                        getSelectStyle(baseStyles, state, !!errors.country_id?.message),
                                }}
                                isDisabled={isDisableUpdateCustomer}
                            />
                            <span className="error">{errors.country_id?.message}</span>
                        </div>
                        <div className={`col-12 mb-1 ${roleId === UserRole.ADMIN ? 'col-sm-8' : 'col-sm-4'}`}>
                            <label className="form-label">
                                {t('address')} <span className="error">*</span>
                            </label>
                            <input
                                {...register('address')}
                                type="text"
                                className={classNames('form-control', {
                                    'is-invalid': Boolean(errors.address?.message),
                                })}
                                disabled={isDisableUpdateCustomer}
                                readOnly={isDisableUpdateCustomer}
                            />
                            <span className="error">{errors.address?.message}</span>
                        </div>
                        {roleId === UserRole.PARTNER && (
                            <>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('partner.percent')}</label>
                                    <FormatNumber
                                        value={jsonPartner.partnerPercent ?? 0}
                                        isInput={true}
                                        onValueChange={(value: number) =>
                                            handleJsonFieldChange('partnerPercent', value.toString())
                                        }
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('partner.cost')} (AUD)</label>
                                    <FormatNumber
                                        value={jsonPartner.partnerCost ?? 0}
                                        isInput={true}
                                        onValueChange={(value: number) =>
                                            handleJsonFieldChange('partnerCost', value.toString())
                                        }
                                    />
                                </div>
                            </>
                        )}
                        {roleId === UserRole.CUSTOMER && (
                            <div className="col-12 col-sm-4 mb-1">
                                <label className="form-label">{t('shop.single')}</label>
                                <Select
                                    options={convertObjectToSelectOptions(shops as ItemParamModel[])}
                                    onChange={onChangeShop}
                                    value={shopValue}
                                    isDisabled={isDisableUpdateCustomer}
                                />
                            </div>
                        )}
                        {!id &&
                            includes(
                                [UserRole.ADMIN, UserRole.PARTNER, UserRole.MANUFACTURER, UserRole.DESIGNER],
                                roleId
                            ) && (
                                <>
                                    <div className="clear" />
                                    <div className="col-12 col-sm-4 mb-1">
                                        <label className="form-label">
                                            {t('password')} <span className="error">*</span>
                                        </label>
                                        <div className="input-group input-group-merge form-password-toggle">
                                            <input
                                                {...register('password')}
                                                className={classNames('form-control', 'form-control-merge', {
                                                    error: Boolean(errors.password?.message),
                                                })}
                                                type={showPass ? 'text' : 'password'}
                                                placeholder="············"
                                            />
                                            <span
                                                className="input-group-text cursor-pointer"
                                                onClick={() => setShowPass((prevShowPass) => !prevShowPass)}
                                            >
                                                {showPass ? <EyeOff size={14} /> : <Eye size={14} />}
                                            </span>
                                        </div>
                                        <span className="error">{errors.password?.message}</span>
                                    </div>
                                    <div className="col-12 col-sm-4 mb-1">
                                        <label className="form-label">
                                            {t('rePassword')} <span className="error">*</span>
                                        </label>
                                        <div className="input-group input-group-merge form-password-toggle">
                                            <input
                                                {...register('confirm_password')}
                                                className={classNames('form-control', 'form-control-merge', {
                                                    error: Boolean(errors.confirm_password?.message),
                                                })}
                                                type={showRePass ? 'text' : 'password'}
                                                placeholder="············"
                                            />
                                            <span
                                                className="input-group-text cursor-pointer"
                                                onClick={() => setShowRePass((prevShowRePass) => !prevShowRePass)}
                                            >
                                                {showRePass ? <EyeOff size={14} /> : <Eye size={14} />}
                                            </span>
                                        </div>
                                        <span className="error">{errors.confirm_password?.message}</span>
                                    </div>
                                </>
                            )}
                        {roleId === UserRole.MANUFACTURER && (
                            <>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('manufacturer.company_name')}</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={jsonManufacturer?.company}
                                        onChange={(e) => handleJsonFieldChange('company', e.target.value)}
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('manufacturer.address_1')}</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={jsonManufacturer?.add1}
                                        onChange={(e) => handleJsonFieldChange('add1', e.target.value)}
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('manufacturer.address_2')}</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={jsonManufacturer?.add2}
                                        onChange={(e) => handleJsonFieldChange('add2', e.target.value)}
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('manufacturer.city')}</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={jsonManufacturer?.city}
                                        onChange={(e) => handleJsonFieldChange('city', e.target.value)}
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('manufacturer.state')}</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={jsonManufacturer?.state}
                                        onChange={(e) => handleJsonFieldChange('state', e.target.value)}
                                    />
                                </div>
                                <div className="col-12 col-sm-4 mb-1">
                                    <label className="form-label">{t('manufacturer.postcode')}</label>
                                    <input
                                        type="text"
                                        className="form-control"
                                        value={jsonManufacturer?.postcode}
                                        onChange={(e) => handleJsonFieldChange('postcode', e.target.value)}
                                    />
                                </div>
                            </>
                        )}
                        {!isDisableUpdateCustomer && (
                            <div className="col-12">
                                <UpdateButton btnText={t('update')} isLoading={isSubmitting} btnClass={['mt-1']} />
                            </div>
                        )}
                    </div>
                </form>
            </div>
        </div>
    );
}
