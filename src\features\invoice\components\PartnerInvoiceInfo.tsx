import { useTranslation } from 'react-i18next';
import PartnerInvoice from '../../../types/PartnerInvoice';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import User from '../../../types/User';

interface IProps {
    partnerInvoice: PartnerInvoice;
    partner: User | undefined;
    productCount: number;
}

export default function PartnerInvoiceInfo({ partnerInvoice, partner, productCount }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <div className="d-flex justify-content-between flex-md-row flex-column mt-0">
            <div>
                <div className="logo-wrapper mb-2">
                    <img style={{ height: '150px' }} src="/assets/images/prp/logo.png" alt="" />
                </div>
                <p className="card-text">
                    {t('totalItem')}: {productCount}
                </p>
            </div>
            <div>
                <div className="d-flex justify-content-start mb-1">
                    <span className="w-px-150 text-heading">{t('poNumber')}</span>
                    <h6 className="mb-0">{partnerInvoice.barcode}</h6>
                </div>
                <div className="d-flex justify-content-start mb-1">
                    <span className="w-px-150 text-heading">{t('date')}</span>
                    <h6 className="mb-0">{formatDateTime(partnerInvoice.created_at!, FORMAT_DATE.SHOW_ONLY_DATE)}</h6>
                </div>
                <div className="demo-inline-spacing">
                    {partner && (
                        <div className="m-0 me-1">
                            <img style={{ height: '85px' }} src={partner.avatar} alt="" />
                            <p>{partner.first_name}</p>
                        </div>
                    )}
                    <div className="m-0">
                        {/*<Barcode image={transferGood.barcode.image} code={transferGood.barcode.code} />*/}
                    </div>
                </div>
            </div>
        </div>
    );
}
