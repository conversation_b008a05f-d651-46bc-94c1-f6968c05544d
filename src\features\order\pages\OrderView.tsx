import { keepPreviousData, useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY, REACT_APP_IS_ADMIN, REACT_APP_IS_MANUFACTURER } from 'constants/common';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Navigate, useParams } from 'react-router-dom';
import OrderService from 'services/OrderService';
import ProductService from 'services/ProductService';
import ShopService from 'services/ShopService';
import WarehouseService from 'services/WarehouseService';
import { ItemStatus } from 'types/common/Item';
import { showToast } from 'utils/common';
import ModalUpdateWarehouse from '../components/ModalUpdateWarehouse';
import OrderInfo from '../components/OrderInfo';
import OrderProducts from '../components/OrderProducts';
import { SearchUser, UserRole } from '../../../types/User';
import UserService from '../../../services/UserService';
import { OrderStatus } from '../../../types/Order';
import { SearchWarehouse } from '../../../types/Warehouse';

export default function OrderView() {
    const { id } = useParams();
    const [show, setShow] = useState(false);
    const { t } = useTranslation();

    const {
        data: order,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: [QUERY_KEY.ORDER, id],
        queryFn: ({ queryKey }) => OrderService.getItem(queryKey[1] ?? ''),
        enabled: !!id,
    });

    const { data: shop } = useQuery({
        queryKey: [QUERY_KEY.SHOP, order?.shop_id],
        queryFn: ({ queryKey }) => ShopService.getItem(queryKey[1] ?? ''),
        enabled: !!order,
    });

    const { data: warehouses } = useQuery({
        enabled: !!order,
        queryKey: [QUERY_KEY.WAREHOUSES, { status_id: ItemStatus.ACTIVE }],
        queryFn: ({ queryKey }) => WarehouseService.getList(queryKey[1] as SearchWarehouse),
    });

    const { data: products } = useQuery({
        enabled: !!order,
        queryKey: [
            QUERY_KEY.CHILDREN_PRODUCTS,
            order?.products.map(
                (item) =>
                    //isEmpty(item.designed_product_child_id) ? item.product_child_id : item.designed_product_child_id
                    item.product_child_id
            ),
            order?.id,
        ],
        queryFn: ({ queryKey }) => ProductService.getArrayChildren(queryKey[1] as string[], queryKey[2] as string),
    });

    const { data: shipTracks } = useQuery({
        enabled: !!order && order.status_id === OrderStatus.PACKED,
        queryKey: [QUERY_KEY.ORDER_TRACKS, order?.id],
        queryFn: ({ queryKey }) => OrderService.getTracks(queryKey[1] as string),
    });

    const { data: designerData } = useQuery({
        enabled: REACT_APP_IS_ADMIN || REACT_APP_IS_MANUFACTURER,
        queryKey: [
            QUERY_KEY.USERS,
            { role_id__i: UserRole.DESIGNER.toString(), status_id__i: ItemStatus.ACTIVE.toString() },
        ],
        queryFn: ({ queryKey }) => UserService.getList(queryKey[1] as SearchUser),
        placeholderData: keepPreviousData,
    });

    const updateMutation = useMutation({
        mutationFn: (wId: string) => OrderService.updateWarehouse(order!.id!, wId),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShow(false);
                    refetch();
                }
            }
        },
    });

    const handleUpdate = () => {
        if (!REACT_APP_IS_MANUFACTURER) {
            updateMutation.reset();
            setShow(true);
        }
    };

    if (isLoading) return <Spinner />;
    if (!isLoading && !order) return <Navigate to="/not-found" />;

    return order ? (
        <>
            <Helmet>
                <title>
                    {t('order.single')} {order.order_number}
                </title>
            </Helmet>
            <ContentHeader
                title={`${t('order.single')} ${order.order_number} - ${order.json.name}`}
                breadcrumbs={[
                    {
                        text: t('order.multiple'),
                        to: '/order',
                    },
                    {
                        text: `${t('order.single')} ${order.order_number}`,
                        to: '',
                    },
                ]}
            />
            <div className="flex-grow-1 container-p-y">
                <div className="row">
                    <OrderProducts
                        order={order}
                        shop={shop}
                        products={products ?? []}
                        warehouses={warehouses ?? []}
                        designers={designerData?.items ?? []}
                        shipTracks={shipTracks?.data}
                        onChooseWarehouse={handleUpdate}
                        refetch={refetch}
                    />
                    <OrderInfo order={order} />
                </div>
            </div>
            <ModalUpdateWarehouse
                show={show}
                warehouses={warehouses ?? []}
                isLoading={updateMutation.isPending}
                changeShow={(s: boolean) => setShow(s)}
                submitAction={(wId: string) => updateMutation.mutate(wId)}
            />
        </>
    ) : (
        <></>
    );
}
