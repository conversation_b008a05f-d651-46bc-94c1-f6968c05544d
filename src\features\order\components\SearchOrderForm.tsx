import SearchForm from 'components/partials/SearchForm';
import { REACT_APP_IS_ADMIN, REACT_APP_IS_DESIGNER, REACT_APP_IS_MANUFACTURER } from 'constants/common';
import { filter } from 'lodash';
import { useTranslation } from 'react-i18next';
import { ItemParamModel, YesNoChoices } from 'types/common/Item';
import { SearchField } from 'types/common/Search';
import { OrderStatusNames } from 'types/Order';
import Shop from 'types/Shop';
import User, { displayUserName } from 'types/User';
import Warehouse from 'types/Warehouse';
import { convertConstantToSelectOptions, convertObjectToSelectOptions, convertSelectToStringKey } from 'utils/common';

interface IProps {
    isLoading: boolean;
    customerId: string | undefined;
    customers: User[];
    partners: User[];
    shops: Shop[];
    warehouses: Warehouse[];
    designers?: User[];
}

export default function SearchOrderForm({
    isLoading,
    customerId,
    customers,
    partners,
    shops,
    warehouses,
    designers = [],
}: Readonly<IProps>) {
    const { t } = useTranslation();

    const fields: SearchField[] = [
        {
            name: 'shop_id__i',
            type: 'select',
            label: t('shop.single'),
            wrapClassName: 'col-md-6 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(shops as ItemParamModel[], true),
            },
            show: !customerId,
        },
        {
            name: 'warehouse_id__i',
            type: 'select',
            label: t('warehouse.single'),
            wrapClassName: 'col-md-6 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(warehouses as ItemParamModel[], true),
            },
            show: !customerId && !REACT_APP_IS_MANUFACTURER,
        },
        {
            name: 'partner_id__i',
            type: 'select',
            label: t('partner.single'),
            wrapClassName: 'col-md-6 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(
                    partners.map((item) => ({ id: item.id!, name: displayUserName(item) })),
                    true
                ),
            },
            show: !customerId && REACT_APP_IS_ADMIN,
        },
        {
            name: 'customer_id__i',
            type: 'select',
            label: t('customer.single'),
            wrapClassName: 'col-md-6 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(
                    customers.map((item) => ({ id: item.id!, name: displayUserName(item) })),
                    true
                ),
            },
            show: !customerId,
        },
        {
            name: 'designer_id__i',
            type: 'select',
            label: t('designer.single'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(
                    designers.map((item) => ({ id: item.id!, name: displayUserName(item) })),
                    true
                ),
            },
            show: REACT_APP_IS_ADMIN || REACT_APP_IS_MANUFACTURER,
        },
        {
            name: 'status_id__i',
            type: 'select',
            label: t('statusName'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: true,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(OrderStatusNames, t, true)),
            },
            show: !REACT_APP_IS_DESIGNER,
        },
        {
            name: 'is_done_designer',
            type: 'select',
            label: t('order.isDesignerDone'),
            wrapClassName: 'col-md-4 col-12',
            options: {
                multiple: false,
                choices: convertSelectToStringKey(convertConstantToSelectOptions(YesNoChoices, t, true)),
            },
            show: true,
        },
        /*{
            name: 'financial_status__i',
            type: 'select',
            label: t('financialStatus'),
            wrapClassName: 'col-md-6 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(FinancialStatusNames, true, t),
            },
            show: true,
        },
        {
            name: 'fulfillment_status__i',
            type: 'select',
            label: t('fulfillmentStatus'),
            wrapClassName: 'col-md-6 col-12',
            options: {
                multiple: true,
                choices: convertObjectToSelectOptions(FulfillmentStatusNames, true, t),
            },
            show: true,
        },*/
    ];

    return <SearchForm fields={filter(fields, { show: true })} isLoading={isLoading} />;
}
