import ModalContent from 'components/partials/ModalContent';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Select, { MultiValue } from 'react-select';
import Shop from 'types/Shop';
import User, { displayUserName } from 'types/User';

interface IProps {
    show: boolean;
    onClose: () => void;
    allShops: Shop[];
    allPartnersFiltered: User[];
    onUpdate: (selectedShopIds: string[]) => void;
    initShopIds: string[];
}

interface GroupedOption {
    label: string;
    options: { value: string; label: string }[];
}

export default function ModalShopAssignment({
    show,
    onClose,
    allShops,
    allPartnersFiltered,
    onUpdate,
    initShopIds,
}: Readonly<IProps>) {
    const { t } = useTranslation();
    const [selectedShops, setSelectedShops] = useState<MultiValue<{ value: string; label: string }>>([]);
    const [isLoading, setIsLoading] = useState(false);
    console.log('allShops:::', allShops);
    console.log('allPartnersFiltered:::', allPartnersFiltered);

    // Create grouped options based on partners
    const groupedOptions: GroupedOption[] = useMemo(
        () =>
            allPartnersFiltered
                .map((partner) => {
                    const partnerShops = allShops.filter((shop) => shop.partner_id === partner.id);

                    return {
                        label: displayUserName(partner),
                        options: partnerShops.map((shop) => ({
                            value: shop.id!,
                            label: shop.name,
                        })),
                    };
                })
                .filter((group) => group.options.length > 0),
        [allPartnersFiltered]
    );

    useEffect(() => {
        if (initShopIds.length > 0) {
            console.log('initShopIds:::', initShopIds);
            // Find the corresponding options for initShopIds
            const initialSelectedOptions: { value: string; label: string }[] = [];

            // Iterate through all grouped options to find matching shops
            groupedOptions.forEach((group) => {
                group.options.forEach((option) => {
                    if (initShopIds.includes(option.value)) {
                        initialSelectedOptions.push(option);
                    }
                });
            });

            setSelectedShops(initialSelectedOptions);
        } else {
            // Clear selection if no initShopIds
            setSelectedShops([]);
        }
    }, [initShopIds, groupedOptions]);

    const handleUpdate = async () => {
        setIsLoading(true);
        try {
            const selectedShopIds = selectedShops.map((shop) => shop.value);
            onUpdate(selectedShopIds);
            onClose();
        } catch (error) {
            // Handle error silently or show toast message
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        onClose();
    };

    const modalContent = (
        <>
            <div className="row">
                <div className="col-12">
                    <label className="form-label">Select Shops</label>
                    <Select
                        isMulti
                        value={selectedShops}
                        onChange={setSelectedShops}
                        options={groupedOptions}
                        styles={{
                            control: (baseStyles: object, state: { isFocused: boolean }) => ({
                                ...baseStyles,
                                borderColor: state.isFocused
                                    ? (baseStyles as { borderColor: string }).borderColor
                                    : '#d8d6de',
                                boxShadow: 'none',
                            }),
                        }}
                        placeholder="Select shops..."
                        noOptionsMessage={() => 'No options available'}
                        className="react-select-container"
                        classNamePrefix="react-select"
                    />
                </div>
            </div>
            <div className="modal-footer px-0">
                <button type="button" className="btn btn-outline-secondary" onClick={handleClose}>
                    Close
                </button>
                <button type="button" className="btn btn-primary mx-0" onClick={handleUpdate} disabled={isLoading}>
                    {isLoading && <span className="spinner-border spinner-border-sm me-1" />}
                    {t('update')}
                </button>
            </div>
        </>
    );

    return <ModalContent show={show} changeShow={() => handleClose()} title="Shop Assignment" content={modalContent} />;
}
