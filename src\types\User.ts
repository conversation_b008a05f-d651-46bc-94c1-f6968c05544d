import {
    REACT_APP_IS_ADMIN,
    REACT_APP_IS_DESIGNER,
    REACT_APP_IS_MANUFACTURER,
    REACT_APP_IS_PARTNER,
} from 'constants/common';
import { BaseModel, BaseSearch } from './common';
import { ItemParam, ItemStatus } from './common/Item';

export enum Gender {
    MALE = 1,
    FEMALE = 2,
    OTHER = 3,
}

export enum UserRole {
    ADMIN = 1,
    PARTNER = 2,
    CUSTOMER = 3,
    SUPPLIER = 4,
    MANUFACTURER = 5,
    DESIGNER = 6,
}
export interface LoginData {
    phone_number: string;
    password: string;
    role_id: number;
}

export interface ChangePassword {
    old_password: string;
    password: string;
    confirm_password: string;
    token: string;
}

export interface SearchUser extends BaseSearch {
    role_id__i?: number[] | string;
    status_id__i?: number[] | string;
    created_by__i?: string[] | string;
    gender_id__i?: string[] | string;
    verified_email?: string;
    shop_id__i?: string[] | string;
    country_id__i?: string[] | string;
    'shop.partner_id'?: string;
}

export interface TwoFAConfigType {
    two_factor_enabled: boolean;
    otp: string;
}

export interface Verify2FA {
    access_token: string;
    otp: string;
}

export type SearchUserParam = {
    [key in keyof SearchUser]: string;
};

export const UserRoleNames: ItemParam[] = [
    { id: UserRole.ADMIN, name: 'admin' },
    { id: UserRole.PARTNER, name: 'partner' },
    { id: UserRole.CUSTOMER, name: 'customer' },
    { id: UserRole.SUPPLIER, name: 'supplier' },
    { id: UserRole.MANUFACTURER, name: 'manufacturer' },
    { id: UserRole.DESIGNER, name: 'designer' },
];

export const GenderNames: ItemParam[] = [
    { id: Gender.MALE, name: 'male' },
    { id: Gender.FEMALE, name: 'female' },
    { id: Gender.OTHER, name: 'other' },
];

export const displayUserName = (user: User | null | undefined) => {
    if (user) {
        if (user.first_name && user.last_name) return `${user.first_name} ${user.last_name}`;
        if (user.first_name) return user.first_name;
        return user.last_name ?? '';
    }
    return '';
};

export const displayUserOnSelect = (user: User | undefined) =>
    user ? displayUserName(user) + ' - ' + user.phone_number : '';

export const getSystemRole = () => {
    if (REACT_APP_IS_ADMIN) return UserRole.ADMIN;
    if (REACT_APP_IS_PARTNER) return UserRole.PARTNER;
    if (REACT_APP_IS_MANUFACTURER) return UserRole.MANUFACTURER;
    if (REACT_APP_IS_DESIGNER) return UserRole.DESIGNER;
    return 0;
};

export default interface User extends BaseModel {
    first_name: string;
    last_name?: string;
    phone_number: string;
    email: string;
    password: string;
    avatar_id?: string;
    avatar?: string;
    role_id: UserRole;
    status_id: ItemStatus;
    gender_id: Gender;
    birthday?: string;
    address: string;
    verified_email: boolean;
    shop_id?: string;
    customer_id?: string;
    country_id: string;
    json?: UserJson;
    token: {
        access_token: string;
        refresh_token: string;
    };
    confirm_password?: string;
    two_factor_enabled?: boolean;
    name?: string;
}

export interface UserJson {
    company?: string;
    add1?: string;
    add2?: string;
    city?: string;
    state?: string;
    postcode?: string;

    partnerPercent?: number;
    partnerCost?: number;
}
