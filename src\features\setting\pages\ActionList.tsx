import { useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import { find } from 'lodash';
import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ActionService from 'services/ActionService';
import Action from 'types/Action';
import { showToast } from 'utils/common';
import ListAction from '../components/ListAction';
import ModalActionUpdate from '../components/ModalActionUpdate';

export default function ActionList() {
    const [itemId, setItemId] = useState('');
    const [showDelete, setShowDelete] = useState(false);
    const [showUpdate, setShowUpdate] = useState(false);
    const { t } = useTranslation();

    const { data, isLoading, isRefetching, refetch } = useQuery({
        queryKey: [QUERY_KEY.ACTIONS],
        queryFn: () => ActionService.getList(true),
    });

    const updateMutation = useMutation({
        mutationFn: (param: Partial<Action>) => ActionService.update(param, param.id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowUpdate(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const deleteMutation = useMutation({
        mutationFn: (id: string) => ActionService.deleteItem(id),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                if (data.success) {
                    setShowDelete(false);
                    setItemId('');
                    refetch();
                }
            }
        },
    });

    const updateItem = async (data: Action) => {
        if (!data.parent_id) delete data.parent_id;
        updateMutation.mutate(data);
    };

    const deleteItem = async () => {
        if (itemId) {
            deleteMutation.mutate(itemId);
        }
    };

    const handleEdit = (id: string) => {
        updateMutation.reset();
        setItemId(id);
        setShowUpdate(true);
    };

    const handleDelete = (id: string) => {
        deleteMutation.reset();
        setItemId(id);
        setShowDelete(true);
    };

    const actionMenu = (_id: number) => {
        updateMutation.reset();
        setShowUpdate(true);
        setItemId('');
    };

    return (
        <>
            <Helmet>
                <title>{t('actions')}</title>
            </Helmet>
            <ContentHeader
                title={t('actions')}
                contextMenu={[
                    {
                        text: t('add'),
                        to: '',
                        icon: 'PLUS',
                        fnCallBack: { actionMenu },
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {(isLoading || isRefetching) && <Spinner />}
                    {data && !isLoading && !isRefetching && (
                        <>
                            <div className="card">
                                <ListAction items={data} handleEdit={handleEdit} handleDelete={handleDelete} />
                            </div>
                            <ModalActionUpdate
                                show={showUpdate}
                                action={find(data, { id: itemId })}
                                listActions={data}
                                isLoading={updateMutation.isPending}
                                changeShow={(s: boolean) => setShowUpdate(s)}
                                submitAction={updateItem}
                            />
                        </>
                    )}
                    <ModalConfirm
                        show={showDelete}
                        text={t('confirm.delete')}
                        btnDisabled={deleteMutation.isPending}
                        changeShow={(s: boolean) => setShowDelete(s)}
                        submitAction={deleteItem}
                    />
                </div>
            </div>
        </>
    );
}
