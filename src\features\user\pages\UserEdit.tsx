import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import { useMutation, useQuery } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import Spinner from 'components/partials/Spinner';
import { QUERY_KEY } from 'constants/common';
import OrderList from 'features/order/pages/OrderList';
import { includes } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Navigate, useLocation, useNavigate, useParams } from 'react-router-dom';
import ActionService from 'services/ActionService';
import ItemParamService from 'services/ItemParamService';
import UserService from 'services/UserService';
import { displayUserName, UserRole, UserRoleNames } from 'types/User';
import { ItemParamType } from 'types/common/Item';
import { getFieldInArrayObject, showToast } from 'utils/common';
import CrossCheckList from '../components/CrossCheckList';
import SetPassForm from '../components/SetPassForm';
import UpdateUserForm from '../components/UpdateUserForm';
import UserActions from '../components/UserActions';
import UserGroups from '../components/UserGroups';

export default function UserEdit() {
    const { id } = useParams();
    const navigate = useNavigate();
    const { hash } = useLocation();
    const { t } = useTranslation();
    const [type, setType] = useState('admin');
    const [tabId, setTabId] = useState('info');

    useEffect(() => {
        if (hash) {
            let hashId = hash.replace('#', '');
            if (!includes(['info', 'password', 'groups', 'permissions', 'orders', 'crossCheck'], hashId)) {
                hashId = 'info';
            }
            setTabId(hashId);
        }
    }, [hash]);

    const getItem = async (userId: string) => {
        const response = await UserService.getItem(userId);
        if (response.success) {
            setType(getFieldInArrayObject(UserRoleNames, response.data.role_id, 'name', 'admin'));
            return response.data;
        }
        return null;
    };

    const { data: user, isLoading } = useQuery({
        queryKey: [QUERY_KEY.USER, id],
        queryFn: ({ queryKey }) => getItem(queryKey[1] ?? ''),
        enabled: !!id,
    });

    const { data: groups } = useQuery({
        queryKey: [QUERY_KEY.GROUPS, type],
        queryFn: () => ItemParamService.getList(ItemParamType.GROUP),
        enabled: user?.role_id === UserRole.ADMIN,
    });

    const { data: groupIds, refetch: refetchGroupIds } = useQuery({
        queryKey: [QUERY_KEY.USER_GROUPS, type, user?.id ?? ''],
        queryFn: ({ queryKey }) => UserService.getGroupIds(queryKey[2] ?? ''),
        enabled: user?.role_id === UserRole.ADMIN,
    });

    const { data: actions } = useQuery({
        queryKey: [QUERY_KEY.ACTIONS, type],
        queryFn: () => ActionService.getList(true),
        enabled: user?.role_id === UserRole.ADMIN,
    });

    const { data: actionIds, refetch: refetchActionIds } = useQuery({
        queryKey: [QUERY_KEY.USER_ACTIONS, type, user?.id ?? ''],
        queryFn: ({ queryKey }) => UserService.getActionIds(queryKey[2] ?? ''),
        enabled: user?.role_id === UserRole.ADMIN,
    });

    const updateUserGroupMutation = useMutation({
        mutationFn: ({ id, ids }: { id: string; ids: string[] }) => UserService.updateGroups(id, ids),
        onSettled: (data, error) => {
            if (error) showToast(false, [t('error.common')]);
            if (data) {
                showToast(data.success, data.messages);
                refetchGroupIds();
                refetchActionIds();
            }
        },
    });

    const handleTabChange = async (_event: React.SyntheticEvent, tab: string) => {
        setTabId(tab);
        navigate('#' + tab);
    };

    const grantGroups = (ids: string[]) => updateUserGroupMutation.mutate({ id: id ?? '', ids });

    if (!isLoading && !user) return <Navigate to="/not-found" />;

    return (
        <>
            <Helmet>
                <title>{t(`${type}.edit`)}</title>
            </Helmet>
            <ContentHeader
                title={t(`${type}.edit`)}
                breadcrumbs={[
                    {
                        text: t(`${type}.multiple`),
                        to: `/user/list/${type}`,
                    },
                    {
                        text: user ? `${displayUserName(user)}` : t(`${type}.edit`),
                        to: '',
                    },
                ]}
            />
            <div className="content-body">
                <div className="col-12">
                    {isLoading && <Spinner />}
                    {!isLoading && user && (
                        <Box sx={{ width: '100%', typography: 'body1' }}>
                            <TabContext value={tabId}>
                                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                                    <TabList onChange={handleTabChange} className="nav nav-pills mb-2">
                                        <Tab label={t('baseInformation')} value="info" />
                                        {includes(
                                            [
                                                UserRole.ADMIN,
                                                UserRole.PARTNER,
                                                UserRole.MANUFACTURER,
                                                UserRole.DESIGNER,
                                            ],
                                            user.role_id
                                        ) && <Tab label={t('resetPassword')} value="password" />}
                                        {user.role_id === UserRole.ADMIN && (
                                            <Tab label={t('group.multiple')} value="groups" />
                                        )}
                                        {user.role_id === UserRole.ADMIN && (
                                            <Tab label={t('permissions')} value="permissions" />
                                        )}
                                        {user.role_id === UserRole.CUSTOMER && (
                                            <Tab label={t('order.multiple')} value="orders" />
                                        )}
                                        {user.role_id === UserRole.SUPPLIER && (
                                            <Tab label={t('crossCheck')} value="crossCheck" />
                                        )}
                                    </TabList>
                                </Box>
                                <TabPanel value="info" style={{ padding: 0 }}>
                                    <UpdateUserForm id={id ?? ''} roleId={user.role_id} user={user} />
                                </TabPanel>
                                {includes(
                                    [UserRole.ADMIN, UserRole.PARTNER, UserRole.MANUFACTURER, UserRole.DESIGNER],
                                    user.role_id
                                ) && (
                                    <TabPanel value="password" style={{ padding: 0 }}>
                                        <SetPassForm id={id ?? ''} />
                                    </TabPanel>
                                )}
                                {user.role_id === UserRole.ADMIN && (
                                    <TabPanel value="groups" style={{ padding: 0 }}>
                                        <UserGroups
                                            groups={groups ?? []}
                                            groupIds={groupIds ?? []}
                                            loading={updateUserGroupMutation.isPending}
                                            handleSubmit={grantGroups}
                                        />
                                    </TabPanel>
                                )}
                                {user.role_id === UserRole.ADMIN && (
                                    <TabPanel value="permissions" style={{ padding: 0 }}>
                                        <UserActions actions={actions ?? []} actionIds={actionIds ?? []} />
                                    </TabPanel>
                                )}
                                {user.role_id === UserRole.CUSTOMER && (
                                    <TabPanel value="orders" style={{ padding: 0 }}>
                                        <OrderList customerId={id ?? ''} isFromCustomer={true} />
                                    </TabPanel>
                                )}
                                {user.role_id === UserRole.SUPPLIER && (
                                    <TabPanel value="crossCheck" style={{ padding: 0 }}>
                                        <CrossCheckList supplierId={id ?? ''} />
                                    </TabPanel>
                                )}
                            </TabContext>
                        </Box>
                    )}
                </div>
            </div>
        </>
    );
}
